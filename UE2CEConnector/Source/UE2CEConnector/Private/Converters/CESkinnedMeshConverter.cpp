#include "Converters/CESkinnedMeshConverter.h"

#include "Tasks/CESkinnedMeshTask.h"

#include "Animation/Skeleton.h"
#include "Engine/SkeletalMesh.h"

#include "Exporters/CEExportOptions.h"
#include "CETask.h"
#include "CELog.h"
#include "ResourceAsset_generated.h"
#include "ImportMeshAssetData_generated.h"
#include "CERenderStateDefines.h"
#include "Builders/CEConvertBuilder.h"
#include "Exchange/CEAssetExchange.h"
#include "Converters/CEConverterUtils.h"
#include <fstream>
#include <format>

#include "Exchange/StringExchange.h"

UCESkinnedMeshConverter::UCESkinnedMeshConverter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = USkeletalMesh::StaticClass();
}

void UCESkinnedMeshConverter::Convert(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object)
{
    USkeletalMesh* SkeletalMesh = Cast<USkeletalMesh>(Object);
    if (!SkeletalMesh)
    {
        return;
    }

    UCEExportOptions* Options = Builder->GetExportOptions();
    if (!Options)
    {
        return;
    }

    // Create and add the task
    Builder->SetupTask<FCESkinnedMeshTask>(SavePath, *Builder, SkeletalMesh);
}