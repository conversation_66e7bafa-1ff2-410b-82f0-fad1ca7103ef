#include "Exporters/CEAnimBlueprintExporter.h"
#include "Converters/CEAnimBlueprintConverter.h"
#include "Animation/AnimBlueprint.h"

UCEAnimBlueprintExporter::UCEAnimBlueprintExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UAnimBlueprint::StaticClass();
    FormatExtension.Add(TEXT("nda"));
    FormatDescription.Add(TEXT("CE Animation Blueprint"));
}
