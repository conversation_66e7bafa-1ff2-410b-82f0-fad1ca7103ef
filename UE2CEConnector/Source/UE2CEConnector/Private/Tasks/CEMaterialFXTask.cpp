#include "CEMaterialFXTask.h"

#include <Exchange/CEAssetExchange.h>
#include <Exchange/StringExchange.h>
#include "Materials/MaterialParameterCollection.h"
#include "CELog.h"

bool FCEMaterialFXTask::Complete()
{
    using namespace CEAssetExchange;

    const auto ExportOptions = mBuilder.GetOptions();

    FAssetExchangeInput Input;
    Input.Options = ExportOptions;
    Input.Sdk = mBuilder.AssetExchangeSdk.get();

    SDKSettings Settings;
    Settings.mBalanceMode = ExportOptions->bUseAssetExchangeSdkBalanceMode;
    Settings.mOverwriteExistingTexture = ExportOptions->bOverwriteExistingTexture;
    Settings.mOverwriteExistingMaterial = ExportOptions->bOverwriteExistingMaterial;
    Settings.mOverwriteExistingMesh = ExportOptions->bOverwriteExistingMesh;
    Settings.mOverwriteExistingWorld = ExportOptions->bOverwriteExistingLevel;
    Input.Sdk->BeginAssemble(&Settings);

    FString RelativePath = mBuilder.GetCEContentPath(MaterialInterface);
    Input.RelativePath = StringMapper::Instance().FromUEToCEString(*RelativePath);
    bool bSuccess = ExportMaterialFX(mBuilder, *MaterialInterface, &Input, StringMapper::Instance().FromUEToCEString(*ExportOptions->SM_DefaultMotherMeterial));

    return bSuccess;
}
