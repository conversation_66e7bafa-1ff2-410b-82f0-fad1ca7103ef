#pragma once

#include "CoreMinimal.h"
#include "AssetExchange/Include/CrossEngineSDK.h"
#include "CEBaseComponentExporter.generated.h"

namespace CEAssetExchange {
struct IEntity;
struct IWorldAssemble;
struct FAssetExchangeInput;
}

class FCEConvertBuilder;

UCLASS(Abstract)
class UCEBaseComponentExporter : public UObject
{
    GENERATED_UCLASS_BODY()
public:
    /**
     * Adds an entity to the world assemble from a scene component.
     * 
     * @param InBuilder - The conversion builder to use
     * @param InWorldAssemble - The world assemble to add the entity to
     * @param InSceneComponent - The scene component being processed
     * @param InParentEntity - The parent entity (if any)
     * @param InExportComponent - The component to export
     * @param InInput - The asset exchange input data
     * @return The created entity
     */
    virtual CEAssetExchange::IEntity* AddEntityFromSceneComponent(
        FCEConvertBuilder& InBuilder,
        CEAssetExchange::IWorldAssemble* InWorldAssemble,
        USceneComponent* InSceneComponent,
        CEAssetExchange::IEntity* InParentEntity,
        USceneComponent* InExportComponent,
        CEAssetExchange::FAssetExchangeInput* InInput);

    /**
     * Adds a game object to the game world from a scene component.
     * 
     * @param InBuilder - The conversion builder to use
     * @param InGameWorld - The game world to add the game object to
     * @param InParentGameObject - The parent game object (if any)
     * @param InExportComponent - The component to export
     * @return The created game object
     */
    virtual cesdk::cegf::SDKGameObject AddGameObjectFromSceneComponent(
        FCEConvertBuilder& InBuilder,
        ::cesdk::cegf::SDKGameWorld InGameWorld,
        ::cesdk::cegf::SDKGameObject InParentGameObject,
        USceneComponent* InExportComponent);
    
    /**
     * Collects all assets that the component depends on.
     * 
     * @param InBuilder - The conversion builder to use
     * @param InComponent - The component to collect dependencies for
     */
    virtual void CollectDependencyAssets(FCEConvertBuilder& InBuilder, USceneComponent* InComponent);
    
    /**
     * Checks if this exporter supports the given object.
     * 
     * @param Object - The object to check support for
     * @return True if this exporter supports the object, false otherwise
     */
    bool SupportsObject(const UObject* Object) const;

    /**
     * The component class that this exporter supports.
     */
    UPROPERTY()
    TSubclassOf<USceneComponent> SupportedClass;

protected:
    ::cesdk::cegf::SDKGameObjectComponent GetOrAddGameObjectComponent(cesdk::cegf::SDKGameObject InGameObject, std::string_view ComponentName);
};