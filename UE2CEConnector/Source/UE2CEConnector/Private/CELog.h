#pragma once

#include "Logging/LogMacros.h"
#include "Containers/Array.h"
#include "Containers/UnrealString.h"
#include "Misc/DateTime.h"
#include "Widgets/SWindow.h"
#include "Framework/Text/SlateTextRun.h"
#include "Widgets/Text/SRichTextBlock.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Layout/SBorder.h"

DEFINE_LOG_CATEGORY_STATIC(CELog, Log, All);

constexpr bool bEnableLog = true;

/**
 * Class to collect and display logs from CE_Log
 */
class FCELogCollector
{
public:
	enum class ELogType
	{
		warn,
        fx_compile_error,
	};

    static FCELogCollector& Get()
    {
        static FCELogCollector Instance;
        return Instance;
    }

    void AddWarningLog(const FString& LogMessage)
    {
        FString TimeStamp = FDateTime::Now().ToString(TEXT("%H:%M:%S"));
        FString FormattedLog = FString::Printf(TEXT("[%s] %s"), *TimeStamp, *LogMessage);
        LogMessages.Add(FormattedLog);
    }

    void ClearWarningLogs()
    {
        LogMessages.Empty();
    }

	void AddFxCompileErrorLog(const FString& LogMessage)
    {
        FString TimeStamp = FDateTime::Now().ToString(TEXT("%H:%M:%S"));
        FString FormattedLog = FString::Printf(TEXT("[%s] %s"), *TimeStamp, *LogMessage);
        FxCompileErrorMessages.Add(FormattedLog);
    }

    void ClearFxCompileErrorLogs()
    {
        FxCompileErrorMessages.Empty();
    }

    void ShowLogsInPopup(const FString& Title = TEXT("CE Export Logs"), ELogType logType = ELogType::warn)
    {
        if ((logType == ELogType::warn && LogMessages.IsEmpty()) || 
			(logType == ELogType::fx_compile_error && FxCompileErrorMessages.IsEmpty()))
        {
            return;
        }

        TSharedRef<SWindow> Window = SNew(SWindow)
            .Title(FText::FromString(Title))
            .ClientSize(FVector2D(800, 600))
            .SupportsMaximize(true)
            .SupportsMinimize(true);

        TSharedRef<SScrollBox> ScrollBox = SNew(SScrollBox);

		auto FuncShowText = [ScrollBox](const FString& LogMessage) 
		{ 
			ScrollBox->AddSlot()[SNew(STextBlock).Text(FText::FromString(LogMessage)).AutoWrapText(true)]; 
		};

		if (logType == ELogType::warn)
        {
            for (auto msg : LogMessages)
            {
                FuncShowText(msg);
            }
        }
        else if (logType == ELogType::fx_compile_error)
        {
            for (auto msg : FxCompileErrorMessages)
            {
                FuncShowText(msg);
            }
        }

        Window->SetContent(
            SNew(SBorder)
            .BorderImage(FCoreStyle::Get().GetBrush("ToolPanel.GroupBorder"))
            .Padding(FMargin(4.0f))
            [
                SNew(SVerticalBox)
                +SVerticalBox::Slot()
                .FillHeight(1.0f)
                [
                    ScrollBox
                ]
                +SVerticalBox::Slot()
                .AutoHeight()
                .HAlign(HAlign_Right)
                .Padding(0.0f, 4.0f, 0.0f, 0.0f)
                [
                    SNew(SButton)
                    .Text(FText::FromString(TEXT("Close")))
                    .OnClicked_Lambda([Window]()
                    {
                        Window->RequestDestroyWindow();
                        return FReply::Handled();
                    })
                ]
            ]
        );

        FSlateApplication::Get().AddWindow(Window);
    }

    const TArray<FString>& GetLogs() const
    {
        return LogMessages;
    }

private:
    FCELogCollector() = default;
    ~FCELogCollector() = default;

    TArray<FString> LogMessages;
    TArray<FString> FxCompileErrorMessages;
};

#define CE_Log(Format, ...) \
    if(bEnableLog) \
    { \
        UE_LOG(CELog, Log, Format, ##__VA_ARGS__); \
    }
#define CE_Log_PopWarn(Format, ...)                                                                                                                                                                                                                    \
    if (bEnableLog)                                                                                                                                                                                                                            \
    {                                                                                                                                                                                                                                          \
        UE_LOG(CELog, Log, Format, ##__VA_ARGS__);                                                                                                                                                                                             \
        FString LogMessage = FString::Printf(Format, ##__VA_ARGS__);                                                                                                                                                                           \
        FCELogCollector::Get().AddWarningLog(LogMessage);                                                                                                                                                                                             \
    }
#define CE_Log_PopFxCompileError(Format, ...)                                                                                                                                                                                                            \
    if (bEnableLog)                                                                                                                                                                                                                            \
    {                                                                                                                                                                                                                                          \
        UE_LOG(CELog, Log, Format, ##__VA_ARGS__);                                                                                                                                                                                             \
        FString LogMessage = FString::Printf(Format, ##__VA_ARGS__);                                                                                                                                                                           \
        FCELogCollector::Get().AddFxCompileErrorLog(LogMessage);                                                                                                                                                                                      \
    }