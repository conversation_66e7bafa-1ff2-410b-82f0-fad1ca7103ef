[/Script/EngineSettings.GeneralProjectSettings]
CompanyName=Epic Games, Inc.
CompanyDistinguishedName=CN=Epic Games, O=Epic Games, L=Cary, S=North Carolina, C=US
CopyrightNotice=Copyright Epic Games, Inc. All Rights Reserved.
Description=Action RPG Sample
Homepage=www.epicgames.com
LicensingTerms=
PrivacyPolicy=
ProjectID=28662D4D4477ABB9AB055AA3A5845C9C
ProjectName=ActionRPG
ProjectVersion=1.4.0
SupportContact=
ProjectDisplayedTitle=INVTEXT("Action RPG Sample")
ProjectDebugTitleInfo=INVTEXT("ActionRPG")

[/Script/Engine.AssetManagerSettings]
-PrimaryAssetTypesToScan=(PrimaryAssetType="Map",AssetBaseClass=/Script/Engine.World,bHasBlueprintClasses=False,bIsEditorOnly=True,Directories=((Path="/Game/Maps")))
-PrimaryAssetTypesToScan=(PrimaryAssetType="PrimaryAssetLabel",AssetBaseClass=/Script/Engine.PrimaryAssetLabel,bHasBlueprintClasses=False,bIsEditorOnly=True,Directories=((Path="/Game")))
+PrimaryAssetTypesToScan=(PrimaryAssetType="Map",AssetBaseClass=/Script/Engine.World,bHasBlueprintClasses=False,bIsEditorOnly=True,Directories=((Path="/Game/Maps")),SpecificAssets=,Rules=(Priority=-1,bApplyRecursively=True,ChunkId=-1,CookRule=Unknown))
+PrimaryAssetTypesToScan=(PrimaryAssetType="PrimaryAssetLabel",AssetBaseClass=/Script/Engine.PrimaryAssetLabel,bHasBlueprintClasses=False,bIsEditorOnly=False,Directories=((Path="/Game")),SpecificAssets=,Rules=(Priority=-1,bApplyRecursively=True,ChunkId=-1,CookRule=Unknown))
+PrimaryAssetTypesToScan=(PrimaryAssetType="Potion",AssetBaseClass=/Script/ActionRPG.RPGPotionItem,bHasBlueprintClasses=False,bIsEditorOnly=False,Directories=((Path="/Game/Items/Potions")),SpecificAssets=,Rules=(Priority=-1,bApplyRecursively=True,ChunkId=-1,CookRule=AlwaysCook))
+PrimaryAssetTypesToScan=(PrimaryAssetType="Skill",AssetBaseClass=/Script/ActionRPG.RPGSkillItem,bHasBlueprintClasses=False,bIsEditorOnly=False,Directories=((Path="/Game/Items/Skills")),SpecificAssets=,Rules=(Priority=-1,bApplyRecursively=True,ChunkId=-1,CookRule=AlwaysCook))
+PrimaryAssetTypesToScan=(PrimaryAssetType="Token",AssetBaseClass=/Script/ActionRPG.RPGTokenItem,bHasBlueprintClasses=False,bIsEditorOnly=False,Directories=((Path="/Game/Items/Tokens")),SpecificAssets=,Rules=(Priority=-1,bApplyRecursively=True,ChunkId=-1,CookRule=AlwaysCook))
+PrimaryAssetTypesToScan=(PrimaryAssetType="Weapon",AssetBaseClass=/Script/ActionRPG.RPGWeaponItem,bHasBlueprintClasses=False,bIsEditorOnly=False,Directories=((Path="/Game/Items/Weapons")),SpecificAssets=,Rules=(Priority=-1,bApplyRecursively=True,ChunkId=-1,CookRule=AlwaysCook))
bOnlyCookProductionAssets=False
bShouldGuessTypeAndNameInEditor=True
bShouldAcquireMissingChunksOnLoad=False

[/Script/GameplayAbilities.AbilitySystemGlobals]
+GameplayCueNotifyPaths=/Game/GameplayCueNotifies

[Internationalization]
+LocalizationPaths=%GAMEDIR%Content/Localization/ARPG

[/Script/UnrealEd.ProjectPackagingSettings]
Build=IfProjectHasCode
BuildConfiguration=PPBC_Development
BuildTarget=
StagingDirectory=(Path="")
FullRebuild=False
ForDistribution=False
IncludeDebugFiles=True
BlueprintNativizationMethod=Disabled
bIncludeNativizedAssetsInProjectGeneration=False
bExcludeMonolithicEngineHeadersInNativizedCode=False
UsePakFile=True
bGenerateChunks=False
bGenerateNoChunks=False
bChunkHardReferencesOnly=False
bForceOneChunkPerFile=False
MaxChunkSize=0
bBuildHttpChunkInstallData=False
HttpChunkInstallDataDirectory=(Path="")
HttpChunkInstallDataVersion=
IncludePrerequisites=False
IncludeAppLocalPrerequisites=False
bShareMaterialShaderCode=True
bSharedMaterialNativeLibraries=False
ApplocalPrerequisitesDirectory=(Path="")
IncludeCrashReporter=True
InternationalizationPreset=English
+CulturesToStage=en
+CulturesToStage=pt-BR
bCookAll=False
bCookMapsOnly=False
bSkipEditorContent=True
bSkipMovies=False
+IniKeyDenylist=KeyStorePassword
+IniKeyDenylist=KeyPassword
+IniKeyDenylist=rsa.privateexp
+IniKeyDenylist=rsa.modulus
+IniKeyDenylist=rsa.publicexp
+IniKeyDenylist=aes.key
+IniKeyDenylist=SigningPublicExponent
+IniKeyDenylist=SigningModulus
+IniKeyDenylist=SigningPrivateExponent
+IniKeyDenylist=EncryptionKey
+IniKeyDenylist=IniKeyBlacklist
+IniKeyDenylist=IniSectionBlacklist
+MapsToCook=(FilePath="/Game/Maps/ActionRPG_Main")
+MapsToCook=(FilePath="/Game/Maps/ActionRPG_P")

