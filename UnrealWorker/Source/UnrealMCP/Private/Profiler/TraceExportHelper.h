#pragma once
#include "TraceExportHelper.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogTraceExport, Log, All);
namespace TraceServices {
class IAnalysisSession;
}

struct FTraceExportOption
{
    // TODO
};

USTRUCT()
struct FEventData
{
    GENERATED_BODY()

    UPROPERTY()
    uint32 ThreadId;
    UPROPERTY()
    FString ThreadName;
    UPROPERTY()
    FString EventName;
    UPROPERTY()
    double StartTime;
    UPROPERTY()
    double EndTime;
    UPROPERTY()
    double Duration;
    UPROPERTY()
    uint32 Depth;
};

class FTraceExportHelper
{
public:
    static bool ConvertTraceToCSVFile(const FTraceExportOption& InOption, const TCHAR* InTraceFile, const TCHAR* InJsonFile);
    static bool ConvertTraceToCSVFile(const FTraceExportOption& InOption, const TraceServices::IAnalysisSession* InAnalysisSession, const TCHAR* InJsonFile);

    static FString ConvertTraceToCSV(const FTraceExportOption& InOption, const TCHAR* InTraceFile);
    static FString ConvertTraceToCSV(const FTraceExportOption& InOption, const TraceServices::IAnalysisSession* InAnalysisSession);
};
