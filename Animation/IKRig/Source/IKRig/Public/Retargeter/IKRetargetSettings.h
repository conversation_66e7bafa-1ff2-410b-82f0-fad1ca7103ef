// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "IKRetargetSettings.generated.h"

class URetargetRootSettings;
class URetargetChainSettings;

UENUM(BlueprintType)
enum class ERetargetTranslationMode : uint8
{
	None			UMETA(DisplayName = "None"),
	GloballyScaled	UMETA(DisplayName = "Globally Scaled"),
	Absolute		UMETA(DisplayName = "Absolute"),
};

UENUM(BlueprintType)
enum class ERetargetRotationMode : uint8
{
	
	Interpolated		UMETA(DisplayName = "Interpolated"),
	OneToOne			UMETA(DisplayName = "One to One"),
	OneToOneReversed	UMETA(DisplayName = "One to One Reversed"),
	None				UMETA(DisplayName = "None"),
};

USTRUCT(BlueprintType)
struct IKRIG_API FTargetChainSpeedPlantSettings
{
	GENERATED_BODY()

	/** The name of the curve on the source animation that contains the speed of the end effector bone.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Plant IK by Speed")
	bool EnableSpeedPlanting = false;
	
	/** The name of the curve on the source animation that contains the speed of the end effector bone.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, BlueprintReadWrite, Category = "Plant IK by Speed", meta = (ClampMin = "0.0", ClampMax = "100.0", UIMin = "0.0", UIMax = "100.0"))
	FName SpeedCurveName;

	/** Range 0 to 100. Default 15. The maximum speed a source bone can be moving while being considered 'planted'.
	*  The target IK goal will not be allowed to move whenever the source bone speed drops below this threshold speed. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Plant IK by Speed", meta = (ClampMin = "0.0", ClampMax = "100.0", UIMin = "0.0", UIMax = "100.0"))
	float SpeedThreshold = 15.0f;

	// How stiff the spring model is that smoothly pulls the IK position after unplanting (more stiffness means more oscillation around the target value)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Plant IK by Speed", meta = (ClampMin = "0.0", UIMin = "0.0"))
	float UnplantStiffness = 250.0f;

	// How much damping to apply to the spring (0 means no damping, 1 means critically damped which means no oscillation)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Plant IK by Speed", meta = (ClampMin = "0.0", ClampMax = "1.0", UIMin = "0.0", UIMax = "1.0"))
	float UnplantCriticalDamping = 1.0f;

	bool operator==(const FTargetChainSpeedPlantSettings& Other) const;
};

USTRUCT(BlueprintType)
struct IKRIG_API FTargetChainFKSettings
{
	GENERATED_BODY()

	/** Whether to copy the shape of the chain from the source skeleton using the Rotation and Translation modes. Default is true.
	* NOTE: All FK operations run before the IK pass to copy the shape of the FK chain from the source skeleton. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "FK Adjustments")
	bool EnableFK = true;
	
	/** Determines how rotation is copied from the source chain to the target chain. Default is Interpolated.
	* Interpolated: Source and target chains are normalized by their length, then each target bone rotation is generated by finding the rotation at the same normalized distance on the source chain and interpolating between the neighboring bones.
	* One to One: Each target bone rotation is copied from the equivalent bone in the source chain, based on the order in the chain, starting at the root of the chain. If the target chain has more bones than the source, the extra bones will remain at their reference pose.
	* One to One Reversed: Same as One-to-One, but starting from the tip of the chain.
	* None: The rotation of each target bone in the chain is left at the reference pose. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "FK Adjustments")
	ERetargetRotationMode RotationMode = ERetargetRotationMode::Interpolated;

	/** Range +/- infinity. Default 1. Scales the amount of rotation that is applied.
	*  If Rotation Mode is None this parameter has no effect.
	*  Otherwise, this parameter blends the rotation of each bone in the chain from the base retarget pose (0) to the retargeted pose (1).*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "FK Adjustments", meta = (UIMin = "0.0", UIMax = "1.0"))
	float RotationAlpha = 1.0f;

	/** Determines how translation is copied from the source chain to the target chain. Default is None.
	* None: Translation of target bones are left unmodified from the retarget pose.
	* Globally Scaled: Translation of target bone is set to the source bone offset multiplied by the global scale of the skeleton (determined by the relative height difference between retarget root bones).
	* Absolute: Translation of target bone is set to the absolute position of the source bone. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "FK Adjustments")
	ERetargetTranslationMode TranslationMode = ERetargetTranslationMode::None;

	/** Range +/- infinity. Default 1. Scales the amount of translation that is applied. Exact behavior depends on the Translation Mode.
	*  In None Mode, this parameter has no effect.
	*  In Globally Scaled and Absolute modes, the translation offset is scaled by this parameter.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "FK Adjustments", meta = (UIMin = "0.0", UIMax = "1.0"))
	float TranslationAlpha = 1.0f;

	/** Range 0 to 1. Default 1. Matches the twist angle of this chain (along the Pole direction) to the source chain.
	*  At 0, the chain's pole vector direction will be left alon
	*  At 1, the root bone of the chain will be twist-rotated in the pole direction to match the orientation of the source chain.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "FK Adjustments", meta = (UIMin = "0.0", UIMax = "1.0", ClampMin = "0.0", ClampMax = "1.0"))
	float PoleVectorMatching = 0.0f;

	/** Default is False. When true, the original offset between the source/target pole vectors will be maintained when using Pole Vector Matching. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "FK Adjustments")
	bool PoleVectorMaintainOffset = false;

	/** Range +/- 180. Default 0. An angular offset, in degrees, for the pole direction of the chain. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "FK Adjustments", meta = (UIMin = "-180.0", UIMax = "180.0", ClampMin = "-180.0", ClampMax = "180.0"))
	float PoleVectorOffset = 0.0f;

	bool operator==(const FTargetChainFKSettings& Other) const;
};

USTRUCT(BlueprintType)
struct IKRIG_API FTargetChainIKSettings
{
	GENERATED_BODY()

	/** Whether to modify the location of the IK goal on this chain. Default is true.
	 * NOTE: This only has an effect if the chain has an IK Goal assigned to it in the Target IK Rig asset.
	 * NOTE: If off, and this chain has an IK Goal, the IK will still be evaluated, but the Goal is set to the input bone location (from the FK pass).*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Adjustments")
	bool EnableIK = true;

	/** Range 0 to 1. Default 0. Blends IK goal position from retargeted location (0) to source bone location (1).
	*  At 0 the goal is placed at the retargeted location.
	*  At 1 the goal is placed at the location of the source chain's end bone. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Adjustments", meta = (ClampMin = "0.0", ClampMax = "1.0", UIMin = "0.0", UIMax = "1.0"))
	float BlendToSource = 0.0f;
	
	/** Range 0 to 1. Default 1. Weight each axis separately when using Blend To Source.
	*  At 0 the goal is placed at the retargeted location.
	*  At 1 the goal is placed at the location of the source chain's end bone. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Adjustments", meta = (ClampMin = "0.0", ClampMax = "1.0", UIMin = "0.0", UIMax = "1.0"))
	FVector BlendToSourceWeights = FVector::OneVector;

	/** Default 0, 0, 0. Apply a static global-space offset to IK goal position. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Adjustments")
	FVector StaticOffset = FVector::ZeroVector;

	/** Default 0, 0, 0. Apply a static local-space offset to IK goal position. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Adjustments")
	FVector StaticLocalOffset = FVector::ZeroVector;

	/** Default 0, 0, 0. Apply a static local-space offset to IK goal rotation. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Adjustments")
	FRotator StaticRotationOffset = FRotator::ZeroRotator;

	/** Range +-Infinity. Default 1. Scales the vertical component of the IK goal's position.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Adjustments", meta = (UIMin = "0.0", UIMax = "5.0"))
	float ScaleVertical = 1.0f;
	
	/** Range 0 to 5. Default 1. Brings IK goal closer (0) or further (1+) from origin of chain.
	*  At 0 the effector is placed at the origin of the chain (ie Shoulder, Hip etc).
	*  At 1 the effector is left at the end of the chain (ie Wrist, Foot etc)
	*  Values in-between 0-1 will slide the effector along the vector from the start to the end of the chain.
	*  Values greater than 1 will stretch the chain beyond the retargeted length. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK Adjustments", meta = (ClampMin = "0.0", ClampMax = "5.0", UIMin = "0.1", UIMax = "2.0"))
	float Extension = 1.0f;

	/** Is this IK goal affected by the stride warping (in Global Settings). Typically this is true for all feet, but not for hands.  */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Warping, meta = (DisplayName = "Affected by IK Warping"))
	bool bAffectedByIKWarping = true;

	bool operator==(const FTargetChainIKSettings& Other) const;
};

USTRUCT(BlueprintType)
struct IKRIG_API FTargetChainSettings
{
	GENERATED_BODY()

public:

	/** All settings for FK retargeting of this chain.
	 *FK retargeting runs before the IK pass. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "FK")
	FTargetChainFKSettings FK;

	/** All settings controlling the IK Goal assigned to this chain.
	 *The IK pass runs AFTER the FK pass and can be used to fix contacts. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "IK")
	FTargetChainIKSettings IK;

	/** All settings associated with planting IK goals based on the speed of the source.
	 *Speed planting will pin the IK goal to the location determined by the IK settings above. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Speed Planting")
	FTargetChainSpeedPlantSettings SpeedPlanting;
	
	bool operator==(const FTargetChainSettings& Other) const;
};

USTRUCT(BlueprintType)
struct IKRIG_API FTargetRootSettings
{
	GENERATED_BODY()

public:

	/** Range 0 to 1. Default 1. Blends the amount of retargeted root rotation to apply.
	*  At 0 the root is left at the rotation from the retarget pose.
	*  At 1 the root is rotated fully to match the source root rotation. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Retarget Settings", meta = (ClampMin = "0.0", ClampMax = "1.0", UIMin = "0.0", UIMax = "1.0"))
	float RotationAlpha = 1.0f;
	
	/** Range 0 to 1. Default 1. Blends the amount of retargeted root translation to apply.
	*  At 0 the root is left at the position from the retarget pose.
	*  At 1 the root will follow the source motion according to the behavior defined in the subsequent settings. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Retarget Settings", meta = (ClampMin = "0.0", ClampMax = "1.0", UIMin = "0.0", UIMax = "1.0"))
	float TranslationAlpha = 1.0f;
	
	/** Range 0 to 1. Default 0. Blends the retarget root's translation to the exact source location.
	*  At 0 the root is placed at the retargeted location.
	*  At 1 the root is placed at the location of the source's retarget root bone.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Retarget Settings", meta = (ClampMin = "0.0", ClampMax = "1.0", UIMin = "0.0", UIMax = "1.0"))
	float BlendToSource = 0.0f;

	/** Per-axis weights for the Blend to Source. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Retarget Settings", meta = (ClampMin = "0.0", ClampMax = "1.0", UIMin = "0.0", UIMax = "1.0"))
	FVector BlendToSourceWeights = FVector::OneVector;

	/** Default 1. Scales the motion of the root position in the horizontal plane (X,Y). */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Retarget Settings", meta = (UIMin = "0.0", UIMax = "3.0"))
	float ScaleHorizontal = 1.0f;

	/** Default 1. Scales the motion of the root position in the vertical direction (Z). */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Retarget Settings", meta = (UIMin = "0.0", UIMax = "3.0"))
	float ScaleVertical = 1.0f;

	/** Applies a static component-space translation offset to the retarget root.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Retarget Settings")
	FVector TranslationOffset = FVector::ZeroVector;

	/** Applies a static local-space rotation offset to the retarget root.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Retarget Settings", meta = (ClampMin = "-180.0", ClampMax = "180.0", UIMin = "-180.0", UIMax = "180.0"))
	FRotator RotationOffset = FRotator::ZeroRotator;

	/** Range 0 to 1. Default 1. Control whether modifications made to the root will affect the horizontal component of IK positions.
	*  At 0 the IK positions are independent of the root modifications.
	*  At 1 the IK positions are calculated relative to the modified root location.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Retarget Settings", meta = (ClampMin = "0.0", ClampMax = "1.0", UIMin = "0.0", UIMax = "1.0", DisplayName = "Affect IK Horizontal"))
	float AffectIKHorizontal = 1.0f;
	
	/** Range 0 to 1. Default 0. Control whether modifications made to the root will affect the vertical component of IK positions.
	*  At 0 the IK positions are independent of the root modifications.
	*  At 1 the IK positions are calculated relative to the modified root location.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Retarget Settings", meta = (ClampMin = "0.0", ClampMax = "1.0", UIMin = "0.0", UIMax = "1.0", DisplayName = "Affect IK Vertical"))
	float AffectIKVertical = 0.0f;

	FVector GetAffectIKWeightVector() const
	{
		return FVector(AffectIKHorizontal, AffectIKHorizontal, AffectIKVertical);
	}
};

UENUM()
enum class EBasicAxis
{
	X,
	Y,
	Z,
	NegX,
	NegY,
	NegZ
};

UENUM()
enum class EWarpingDirectionSource
{
	Goals,
	Chain,
	RootBone
};

USTRUCT(BlueprintType)
struct IKRIG_API FRetargetGlobalSettings
{
	GENERATED_BODY()
	
	/** When false, the motion of the Retarget Root bone is not copied from the source. Useful for debugging issues with the root settings.
	* Note: the retargeting order is: Root > FK > IK > Post
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = RetargetPhases)
	bool bEnableRoot = true;
	
	/** When false, limbs are not copied via FK. Useful for debugging limb issues suspected to be caused by FK chain settings.
	* Note: the retargeting order is: Root > FK > IK > Post
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = RetargetPhases)
	bool bEnableFK = true;
	
	/** When false, IK is not applied as part of retargeter. Useful for debugging limb issues suspected to be caused by IK.
	* Note: the retargeting order is: Root > FK > IK > Post
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = RetargetPhases)
	bool bEnableIK = true;

	/** When false, Post operations are not applied as part of retargeter. Useful for debugging issues suspected to be caused by the post phase.
	 * Note: the retargeting order is: Root > FK > IK > Post
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = RetargetPhases)
	bool bEnablePost = true;

	/** Enable IK Warping.
	 * These options allow for global modifications to all IK Goals that have "Affected by IK Warping" turned on (the default).
	 * "Affected by IK Warping" can be found in the IK settings of a retarget chain. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Warping)
	bool bWarping = false;

	/** Defines the source used to determine the forward direction as the character animates in world space. Default is "Goals".
	 * This method uses various positions on the character to create a "best fit" global rotation that approximates the facing direction of the character over time.
	 * This global rotation is used to define the forward and sideways directions used when warping goals along those axes.
	 * The options are:
	 * Goals: uses the positions of the IK goals to approximate the facing direction. This is best used on characters with a vertical spine, like bipeds.
	 * Chain: uses the positions of the bones in a retarget chain to approximate the facing direction. This is best when used with the spine chain for characters with a horizontal spine, like quadrupeds.
	 * Root Bone: uses the rotation of the root bone of the skeleton. This is most robust, but character must have correct root motion with yaw rotation in movement direction.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Warping)
	EWarpingDirectionSource DirectionSource = EWarpingDirectionSource::Goals;
	
	/** The world space axis that represents the forward facing direction for your character. By default, in Unreal, this is +Y.  */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Warping)
	EBasicAxis ForwardDirection = EBasicAxis::Y;

	/** When using the "Chain" option as a Direction Source, this defines the chain to use to determine the facing direction of the character.
	 * Typically this would be the chain that contains the Spine bones. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Warping, meta=(EditCondition="DirectionSource == EWarpingDirectionSource::Chain"))
	FName DirectionChain;

	/** Range 0 to Inf. Default 1. Warps IK goal positions in the forward direction. Useful for stride warping.
	 * Values below 1 will create smaller, squashed strides. Values greater than 1 will create stretched, longer strides.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Warping, meta = (UIMin = "0.0", UIMax = "5.0", ClampMin = "0.0"))
	float WarpForwards = 1.0f;

	/** Range -+Inf. Default is 0. A static offset in world units to move the IK goals perpendicular to the forward direction.
	 * Values less than zero will move the goals towards the center-line of the character. Values greater than zero push the goals outwards.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Warping, meta = (UIMin = "-10.0", UIMax = "100.0"))
	float SidewaysOffset = 0.0f;

	/** Range 0 to +Inf. Default is 1.0f.
	 * Values below 1 pull all the goals towards the average of all the goals (towards each other).
	 * Values greater than 1 push the goals apart.*/
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = Warping, meta = (UIMin = "0.0", UIMax = "2.0", ClampMin = "0.0"))
	float WarpSplay = 1.0f;

	static FVector GetAxisVector(const EBasicAxis& Axis)
	{
		switch (Axis)
		{
		case EBasicAxis::X:
			return FVector::XAxisVector;
		case EBasicAxis::Y:
			return FVector::YAxisVector;
		case EBasicAxis::Z:
			return FVector::ZAxisVector;
		case EBasicAxis::NegX:
			return -FVector::XAxisVector;
		case EBasicAxis::NegY:
			return -FVector::YAxisVector;
		case EBasicAxis::NegZ:
			return -FVector::ZAxisVector;
		default:
			checkNoEntry();
			return FVector::ZeroVector;
		}
	}
};