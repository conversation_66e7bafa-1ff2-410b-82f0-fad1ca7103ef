#include "CESkyAtmosphereActorExporter.h"
#include "Components/SkyAtmosphereComponent.h"
#include "Builders/CEConvertBuilder.h"
#include "Exchange/ExportWorld.h"

UCESkyAtmosphereActorExporter::UCESkyAtmosphereActorExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = ASkyAtmosphere::StaticClass();
}

UCESkyAtmosphereActorExporter::AddEntityFromActorRetType UCESkyAtmosphereActorExporter::AddEntityFromActor(
    FCEConvertBuilder& InBuilder,
    CEAssetExchange::IWorldAssemble* InWorldAssemble,
    USceneComponent* InSceneComponent,
    CEAssetExchange::IEntity* LevelEntity,
    AActor* InExportActor,
    CEAssetExchange::FAssetExchangeInput* InInput)
{
    auto* SkyAtmosphere = Cast<USkyAtmosphereComponent>(InExportActor->GetComponentByClass(USkyAtmosphereComponent::StaticClass()));
    return {InBuilder.AddEntityFromSceneComponent(InWorldAssemble, InSceneComponent, LevelEntity, SkyAtmosphere, InInput), false};
}