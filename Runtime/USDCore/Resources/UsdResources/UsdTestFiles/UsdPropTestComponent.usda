#usda 1.0
(
	defaultPrim = "TestComponentDef"
)

def "TestComponentDef"()
{
	int Int8Property = -120 (
		customData = { string unrealPropertyPath = "Int8Property" }
	)
	
	int Int16Property = 731 (
		customData = { string unrealPropertyPath = "Int16Property" }
	)
	
	int Int32Property = 4234234 (
		customData = { string unrealPropertyPath = "Int32Property" }
	)
	
	int64 Int64Property = -10 (
		customData = { string unrealPropertyPath = "Int64Property" }
	)
	
	uchar ByteProperty = 254 (
		customData = { string unrealPropertyPath = "ByteProperty" }
	)
	
	uint UnsignedInt16Property = 65534 (
		customData = { string unrealPropertyPath = "UnsignedInt16Property" }
	)
	
	uint UnsignedInt32Property = 4294967294 (
		customData = { string unrealPropertyPath = "UnsignedInt32Property" }
	)
	
	uint64 UnsignedInt64Property = 1000 (
		customData = { string unrealPropertyPath = "UnsignedInt64Property" }
	)
	
	float FloatProperty = 2322.323486 (
		customData = { string unrealPropertyPath = "FloatProperty" }
	)
	
	double DoubleProperty = 8.8465165464 (
		customData = { string unrealPropertyPath = "DoubleProperty" }
	)
	
	token NameProperty = "This is a name. Declaring as a string works too" (
		customData = { string unrealPropertyPath = "NameProperty" }
	)
	
	string StringProperty = "This is a string. Declaring as a token works too" (
		customData = { string unrealPropertyPath = "StringProperty" }
	)
	
	string TextProperty = "This is a text property. Declaring as string or token works" (
		customData = { string unrealPropertyPath = "TextProperty" }
	)
	
	bool BoolProperty = true (
		customData = { string unrealPropertyPath = "BoolProperty" }
	)
	
	# currently vector types must be float not double
	float2 Vector2Property = (50,50) (
		customData = { string unrealPropertyPath = "Vector2Property" }
	)
	
	# currently vector types must be float not double
	float3 Vector3Property = (100.19,200.09,300.4564) (
		customData = { string unrealPropertyPath = "Vector3Property" }
	)
	
	# currently vector types must be float not double
	float4 Vector4Property = (1225.0, 89.45444, 45.58, 5.06) (
		customData = { string unrealPropertyPath = "Vector4Property" }
	)
	
	# currently color types must be float not double (3 or 4)
	color4f LinearColorProperty = (.4,.2,1.0,.8) (
		customData = { string unrealPropertyPath = "LinearColorProperty" }
	)

	# currently color types must be float not double
	color3f ColorProperty = (1.0,.5,0) (
		customData = { string unrealPropertyPath = "ColorProperty" }
	)
	
	# should be fully qualified path to the object.  Can be a string or token
	string ObjectProperty = "/Engine/EngineMaterials/DefaultMaterial" (
		customData = { string unrealPropertyPath = "ObjectProperty" }
	)
	
	# string or token works for enums
	token EnumProperty = "EnumVal4" (
		customData = { string unrealPropertyPath = "EnumProperty" }
	)
	
	# float3s can also represent "FRotator" types in Unreal.  Type is an euler angle. See FRotator::MakeFromEuler.   
	# Note: Does not do any coordinate system conversions
	float3 RotatorProperty = (90,180,60) (
		customData = { string unrealPropertyPath = "RotatorProperty" }
	)
	
	int[] Int8ArrayProperty = [-50,123](
		customData = { string unrealPropertyPath = "Int8ArrayProperty" }
	)
	
	int[] Int16ArrayProperty = [731, 3400] (
		customData = { string unrealPropertyPath = "Int16ArrayProperty" }
	)
	
	int[] Int32ArrayProperty = [4234234, 102204020] (
		customData = { string unrealPropertyPath = "Int32ArrayProperty" }
	)
	
	int64[] Int64ArrayProperty = [-10,2131123123] (
		customData = { string unrealPropertyPath = "Int64ArrayProperty" }
	)
	
	uchar[] ByteArrayProperty = [254, 90] (
		customData = { string unrealPropertyPath = "ByteArrayProperty" }
	)
	
	uint[] UnsignedInt16ArrayProperty = [65534, 12000] (
		customData = { string unrealPropertyPath = "UnsignedInt16ArrayProperty" }
	)
	
	uint[] UnsignedInt32ArrayProperty = [4294967294, 12001] (
		customData = { string unrealPropertyPath = "UnsignedInt32ArrayProperty" }
	)
	
	# USD doesnt seem to like 64 bit literals even though it claims support
	uint64[] UnsignedInt64ArrayProperty = [1000, 500000000] (
		customData = { string unrealPropertyPath = "UnsignedInt64ArrayProperty" }
	)
	
	float[] FloatArrayProperty = [2322.323486, 1.01] (
		customData = { string unrealPropertyPath = "FloatArrayProperty" }
	)
	
	double[] DoubleArrayProperty = [8.8465165464, 1000012012.000102] (
		customData = { string unrealPropertyPath = "DoubleArrayProperty" }
	)
	
	token[] NameArrayProperty = ["This is a name. Declaring as a string works too", "This is another name in an array"] (
		customData = { string unrealPropertyPath = "NameArrayProperty" }
	)
	
	string[] StringArrayProperty = ["This is a string. Declaring as a token works too", "this is another string in an array"] (
		customData = { string unrealPropertyPath = "StringArrayProperty" }
	)
	
	string[] TextArrayProperty = ["This is a text property. Declaring as string or token works", "this is another text"] (
		customData = { string unrealPropertyPath = "TextArrayProperty" }
	)
	
	bool[] BoolArrayProperty = [true, false, true, false, false, true, true, true] (
		customData = { string unrealPropertyPath = "BoolArrayProperty" }
	)
	
	# currently vector types must be float not double
	float2[] Vector2ArrayProperty = [(50,50),(50.1,50.2),(50.3,50.4),(50.5,50.6),(50.7,50.8),(50.9,51.0)] (
		customData = { string unrealPropertyPath = "Vector2ArrayProperty" }
	)
	
	# currently vector types must be float not double
	float3[] Vector3ArrayProperty = [(100,100,100),(100.1,200.2,300.3)] (
		customData = { string unrealPropertyPath = "Vector3ArrayProperty" }
	)
	
	# currently vector types must be float not double
	float4[] Vector4ArrayProperty = [(1225.0, 89.45444, 45.58, 5.06),(798.787, 4564.457896, 6546.2134, 465.564),(4564.0, 4564.444, 313.7897, 789.7897)] (
		customData = { string unrealPropertyPath = "Vector4ArrayProperty" }
	)
	
	# currently color types must be float not double. 3 or 4 channels are allowed
	color4f[] LinearColorArrayProperty = [(.4,.2,1.0,.8),(1.0,.2,0.0,1.0),(.8,1.0,0.0,.1),(0,.5,.5,.5)] (
		customData = { string unrealPropertyPath = "LinearColorArrayProperty" }
	)

	# currently color types must be float not double. 3 or 4 channels are allowed
	color3f[] ColorArrayProperty = [(1.0,0,0),(0,1.0, 0),(0,0,1)] (
		customData = { string unrealPropertyPath = "ColorArrayProperty" }
	)
	
	# should be fully qualified path to the object.  Can be a string or token
	string[] ObjectArrayProperty = ["/Engine/EngineMaterials/DefaultMaterial", "/Engine/EngineResources/DefaultTexture"] (
		customData = { string unrealPropertyPath = "ObjectArrayProperty" }
	)
	
	# string or token works for enums
	token[] EnumArrayProperty = ["EnumVal4", "EnumVal3", "EnumVal2", "EnumVal1"] (
		customData = { string unrealPropertyPath = "EnumArrayProperty" }
	)
	
	# float3s can also represent "FRotator" types in Unreal.  Type is an euler angle. See FRotator::MakeFromEuler.   
	# Note: Does not do any coordinate system conversions
	float3[] RotatorArrayProperty = [(90,180,60),(100,50,120),(0,0,0)] (
		customData = { string unrealPropertyPath = "RotatorArrayProperty" }
	)
}
