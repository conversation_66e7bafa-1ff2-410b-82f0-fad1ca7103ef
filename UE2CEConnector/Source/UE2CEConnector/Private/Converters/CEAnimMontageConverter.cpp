#include "Converters/CEAnimMontageConverter.h"

#include "Animation/AnimMontage.h"
#include "Exporters/CEExportOptions.h"
#include "Builders/CEConvertBuilder.h"
#include "Tasks/CEAnimaMontageTask.h"

UCEAnimMontageConverter::UCEAnimMontageConverter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UAnimMontage::StaticClass();
}

void UCEAnimMontageConverter::Convert(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object)
{
    UAnimMontage* AnimMontage = Cast<UAnimMontage>(Object);
    if (!AnimMontage)
    {
        return;
    }

    UCEExportOptions* Options = Builder->GetExportOptions();
    if (!Options)
    {
        return;
    }

    // Create and add the task
    Builder->SetupTask<FCEAnimaMontageTask>(Save<PERSON><PERSON>, *<PERSON><PERSON><PERSON>, AnimMontage);
}