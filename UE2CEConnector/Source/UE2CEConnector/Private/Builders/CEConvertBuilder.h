#pragma once
#include "CrossEngineJsonFile.h"
#include "Exporters/CEExportOptions.h"
#include "CETask.h"
#include "ActorExporters/CEBaseActorExporter.h"
#include "AssetExchange/include/SDK.h"

struct FCEResource;
class UCEBaseConverter;
class UCEBaseComponentExporter;
class UCEBaseActorExporter;

namespace CEAssetExchange {
struct IWorldAssemble;
struct IEntity;
struct FAssetExchangeInput;
}

class FCEConvertBuilder
{
public:
    FCEConvertBuilder(const FString& ExportPath, const UCEExportOptions* Options);
    ~FCEConvertBuilder();
    FString mRootPath;

    std::shared_ptr<CEAssetExchange::IAssetExchangeSDK> AssetExchangeSdk;

    TSharedPtr<FJsonObject> GetAssetMappings() const
    {
        return AssetMappings;
    }

    template<typename TaskType, typename... TaskArgTypes, typename = typename TEnableIf<TIsDerivedFrom<TaskType, FCETask>::Value>::Type>
    int32 SetupTask(TaskArgTypes&&... Args)
    {
        return SetupTask(MakeUnique<TaskType>(Forward<TaskArgTypes>(Args)...));
    }

    template<typename TaskType, typename = typename TEnableIf<TIsDerivedFrom<TaskType, FCETask>::Value>::Type>
    int32 SetupTask(TUniquePtr<TaskType> Task)
    {
        return SetupTask(TUniquePtr<FCETask>(Task.Release()));
    }

    int32 SetupTask(TUniquePtr<FCETask> Task);
    void CollectAllDependencyTasks(FFeedbackContext* Context);
    void CompleteAllTasks(FFeedbackContext* Context);

    const UCEExportOptions* GetOptions() const
    {
        return mOptions;
    }

    UCEExportOptions* GetExportOptions() const
    {
        return const_cast<UCEExportOptions*>(mOptions);
    }

    // Converters
    void GetOrAdd(const FString& SavePath, UObject* Object);

    // using UE relative path to save file
    void GetOrAdd(UObject* Object);

    FString GetCEContentPath(const UObject* Object);

    // component exporter
    CEAssetExchange::IEntity* AddEntityFromSceneComponent(CEAssetExchange::IWorldAssemble* InWorldAssemble,
                                                          USceneComponent* InSceneComponent,
                                                          CEAssetExchange::IEntity* InParentEntity,
                                                          USceneComponent* InExportComponent,
                                                          CEAssetExchange::FAssetExchangeInput* InInput);

    cesdk::cegf::SDKGameObject AddGameObjectFromSceneComponent(::cesdk::cegf::SDKGameWorld InGameWorld,
                                                                ::cesdk::cegf::SDKGameObject InParentGameObject,
                                                                USceneComponent* InExportComponent);

    void CollectComponentDependencyAsset(USceneComponent* InComponent);

    // Actor exporter
    UCEBaseActorExporter::AddEntityFromActorRetType AddEntityFromActor(CEAssetExchange::IWorldAssemble* InWorldAssemble,
                                                                       USceneComponent* InSceneComponent,
                                                                       CEAssetExchange::IEntity* LevelEntity,
                                                                       AActor* InExportActor,
                                                                       CEAssetExchange::FAssetExchangeInput* InInput);

    cesdk::cegf::SDKGameObject AddGameObjectFromActor(::cesdk::cegf::SDKGameWorld InGameWorld,
                                                       ::cesdk::cegf::SDKGameObject InParentGameObject,
                                                       AActor* InExportActor);

    void CollectActorDependencyAsset(AActor* InActor);

private:
    FText GetPriorityMessageFormat(ECETaskPriority Priority);

    FString GetCEProjectRootDir(const FString& ExportPath);

    const UCEExportOptions* mOptions;

    TSharedPtr<FJsonObject> AssetMappings;

    enum EBuilderState
    {
        eUninitialized,
        eCollectTasks,
        eCompleteTasks,
    };

    EBuilderState BuilderState;
    FCriticalSection TasksLock;
    TArray<TUniquePtr<FCETask>> Tasks;

    void RegisterExporter();

    // Converters
    UCEBaseConverter* FindConverter(const UObject* Object);
    TArray<TObjectPtr<UCEBaseConverter>> Converters;
    TSet<UObject*> ConvertedObjects;

    // ComponentExport
    UCEBaseComponentExporter* FindComponentExporter(USceneComponent* Comp) const;
    TArray<TObjectPtr<UCEBaseComponentExporter>> ComponentExporters;

    // ActorExport
    UCEBaseActorExporter* FindActorExporter(AActor* Actor) const;
    TArray<TObjectPtr<UCEBaseActorExporter>> ActorExporters;

    // StatisticsReport
    TMap<UClass*, int32> UnsupportClassStatistics;
};