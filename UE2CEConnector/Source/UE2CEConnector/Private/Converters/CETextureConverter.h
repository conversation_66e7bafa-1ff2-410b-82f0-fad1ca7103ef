#pragma once
#include "CEBaseConverter.h"
#include "Builders/CEConvertBuilder.h"
#include "CEResource.h"
#include "Engine/EngineTypes.h"
#include "CETextureConverter.generated.h"

UCLASS()
class UCETextureConverter final : public UCEBaseConverter
{
public:
    GENERATED_UCLASS_BODY()
protected:
    virtual void Convert(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object) override;

    virtual FString GetCEFileExtension(const UObject* Object) override;
};