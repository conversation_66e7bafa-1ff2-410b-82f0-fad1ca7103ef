

[/Script/GameplayTags.GameplayTagsSettings]
ImportTagsFromConfig=True
WarnOnInvalidTags=True
ClearInvalidTags=False
AllowEditorTagUnloading=True
AllowGameTagUnloading=False
FastReplication=False
InvalidTagCharacters="\"\',"
NumBitsForContainerSize=6
NetIndexFirstBitSegment=16
+GameplayTagList=(Tag="Ability.Item",DevComment="")
+GameplayTagList=(Tag="Ability.Melee",DevComment="")
+GameplayTagList=(Tag="Ability.Melee.Close",DevComment="")
+GameplayTagList=(Tag="Ability.Melee.Far",DevComment="")
+GameplayTagList=(Tag="Ability.Ranged",DevComment="")
+GameplayTagList=(Tag="Ability.Skill",DevComment="")
+GameplayTagList=(Tag="Cooldown.Skill",DevComment="")
+GameplayTagList=(Tag="EffectContainer.Default",DevComment="")
+GameplayTagList=(Tag="Event.Montage.Player.Combo.BurstPound",DevComment="")
+GameplayTagList=(Tag="Event.Montage.Player.Combo.ChestKick",DevComment="")
+GameplayTagList=(Tag="Event.Montage.Player.Combo.FrontalAttack",DevComment="")
+GameplayTagList=(Tag="Event.Montage.Player.Combo.GroundPound",DevComment="")
+GameplayTagList=(Tag="Event.Montage.Player.Combo.JumpSlam",DevComment="")
+GameplayTagList=(Tag="Event.Montage.Shared.UseItem",DevComment="")
+GameplayTagList=(Tag="Event.Montage.Shared.UseSkill",DevComment="")
+GameplayTagList=(Tag="Event.Montage.Shared.WeaponHit",DevComment="")
+GameplayTagList=(Tag="GameplayCue.Potion.PowerBoost",DevComment="")
+GameplayTagList=(Tag="Status.DamageImmune",DevComment="")

