#pragma once

#include "CoreMinimal.h"
#include "Exporters/Exporter.h"
#include "CEBaseExporter.generated.h"

class FCEConvertBuilder;
class UCEExportOptions;

UCLASS(Abstract)
class UE2CECONNECTOR_API UCEBaseExporter : public UExporter
{
public:

	GENERATED_BODY()

	explicit UCEBaseExporter(const FObjectInitializer& ObjectInitializer);

	bool ExportBinary(UObject* Object, const TCHAR* Type, FArchive& Archive, FFeedbackContext* Warn, int32 FileIndex, uint32 PortFlags) override;

protected:

	/**
	 * Adds an export object task to the conversion builder.
	 * 
	 * @param Builder - The conversion builder to add the object to
	 * @param Object - The object to be added to the builder
	 * @return True if the object was successfully added, false otherwise
	 */
	virtual bool AddObject(FCEConvertBuilder& Builder, UObject* Object);

private:
    UCEExportOptions* GetExportOptions();
};
