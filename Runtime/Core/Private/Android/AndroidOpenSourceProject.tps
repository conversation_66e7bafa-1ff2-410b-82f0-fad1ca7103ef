<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Android Open Source Project</Name>
  <Location>/Engine/Source/Runtime/Core/Private/Android/</Location>
  <Date>2016-06-07T15:38:49.8903216-04:00</Date>
  <Function>Code snippet from libcorkscrew/arch-arm/backtrace-arm.c gives us reliable Android call stacks.</Function>
  <Justification>Android exception handler and crash reporting</Justification>
  <Eula>http://www.apache.org/licenses/LICENSE-2.0</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/AndroidOpenSourceProject_License.txt</LicenseFolder>
</TpsData>