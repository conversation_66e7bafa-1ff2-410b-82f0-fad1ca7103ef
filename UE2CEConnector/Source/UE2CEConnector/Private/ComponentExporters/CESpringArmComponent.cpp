#include "CESpringArmComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "Exchange/StringExchange.h"
#include "UE2CEConnectorUtils/UE2CEConnectorUtils.h"

UCESpringArmComponent::UCESpringArmComponent(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = USpringArmComponent::StaticClass();
}

cesdk::cegf::SDKGameObject UCESpringArmComponent::AddGameObjectFromSceneComponent(FCEConvertBuilder& InBuilder, cesdk::cegf::SDKGameWorld InGameWorld, cesdk::cegf::SDKGameObject InParentGameObject, USceneComponent* InExportComponent)
{
    constexpr std::string_view SpringArmScriptPath = "Contents/TypeScript/ActionRPG/SpringArmController.mts";
    auto GameObject = Super::AddGameObjectFromSceneComponent(InBuilder, InGameWorld, InParentGameObject, InExportComponent);
    GameObject.SetScriptPath(SpringArmScriptPath.data());
    GameObject.AddComponent("cegf::InputComponent");
    return GameObject;
}