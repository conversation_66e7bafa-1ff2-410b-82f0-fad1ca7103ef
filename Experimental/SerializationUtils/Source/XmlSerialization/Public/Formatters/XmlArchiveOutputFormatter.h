// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Serialization/StructuredArchiveFormatter.h"
#include "UObject/ObjectResource.h"
#include "XmlSerializationDefines.h"

#if WITH_TEXT_ARCHIVE_SUPPORT

class XMLSERIALIZATION_API FXmlArchiveOutputFormatter final : public FStructuredArchiveFormatter
{
private:
	// Non-copyable - Need to implement if needed.
	FXmlArchiveOutputFormatter(FXmlArchiveOutputFormatter&) = delete;
	FXmlArchiveOutputFormatter& operator=(const FXmlArchiveOutputFormatter&) = delete;
	// Non-movable - Need to implement if needed.
	FXmlArchiveOutputFormatter(FXmlArchiveOutputFormatter&&) = delete;
	FXmlArchiveOutputFormatter& operator=(FXmlArchiveOutputFormatter&&) = delete;

public:
	/**
	 *	@remark Inner archive must remain valid throughout the lifetime of this object.
	 */
	FXmlArchiveOutputFormatter(FArchive& InInner);
	
	virtual ~FXmlArchiveOutputFormatter() override;

	bool SaveDocumentToInnerArchive(EXmlSerializationEncoding InEncoding = EXmlSerializationEncoding::Utf8);
	
	void SerializeObjectsInPlace(bool bEnabled) { bSerializeObjectsInPlace = bEnabled; }
	void SerializeSoftObjectsInPlace(bool bEnabled) { bSerializeSoftObjectsInPlace = bEnabled; }

	virtual FArchive& GetUnderlyingArchive() override;

	virtual bool HasDocumentTree() const override;

	virtual void EnterRecord() override;
	virtual void LeaveRecord() override;
	virtual void EnterField(FArchiveFieldName Name) override;
	virtual void LeaveField() override;
	virtual bool TryEnterField(FArchiveFieldName Name, bool bEnterWhenSaving) override;

	virtual void EnterArray(int32& NumElements) override;
	virtual void LeaveArray() override;
	virtual void EnterArrayElement() override;
	virtual void LeaveArrayElement() override;

	virtual void EnterStream() override;
	virtual void LeaveStream() override;
	virtual void EnterStreamElement() override;
	virtual void LeaveStreamElement() override;

	virtual void EnterMap(int32& NumElements) override;
	virtual void LeaveMap() override;
	virtual void EnterMapElement(FString& Name) override;
	virtual void LeaveMapElement() override;

	virtual void EnterAttributedValue() override;
	virtual void EnterAttribute(FArchiveFieldName AttributeName) override;
	virtual void EnterAttributedValueValue() override;
	virtual void LeaveAttribute() override;
	virtual void LeaveAttributedValue() override;
	virtual bool TryEnterAttribute(FArchiveFieldName AttributeName, bool bEnterWhenSaving) override;

	virtual bool TryEnterAttributedValueValue() override;

	virtual void Serialize(uint8& Value) override;
	virtual void Serialize(uint16& Value) override;
	virtual void Serialize(uint32& Value) override;
	virtual void Serialize(uint64& Value) override;
	virtual void Serialize(int8& Value) override;
	virtual void Serialize(int16& Value) override;
	virtual void Serialize(int32& Value) override;
	virtual void Serialize(int64& Value) override;
	virtual void Serialize(float& Value) override;
	virtual void Serialize(double& Value) override;
	virtual void Serialize(bool& Value) override;
	virtual void Serialize(FString& Value) override;
	virtual void Serialize(FName& Value) override;
	virtual void Serialize(UObject*& Value) override;
	virtual void Serialize(FText& Value) override;
	virtual void Serialize(FWeakObjectPtr& Value) override;
	virtual void Serialize(FSoftObjectPtr& Value) override;
	virtual void Serialize(FSoftObjectPath& Value) override;
	virtual void Serialize(FLazyObjectPtr& Value) override;
	virtual void Serialize(FObjectPtr& Value) override;
	virtual void Serialize(TArray<uint8>& Value) override;
	virtual void Serialize(void* Data, uint64 DataSize) override;

	void SetObjectIndicesMap(const TMap<UObject*, FPackageIndex>* InObjectIndicesMap)
	{
		ObjectIndicesMap = InObjectIndicesMap;
	}

private:
	bool IsObjectAllowed(UObject* InObject) const;

private:
	class FImpl;
	FImpl* Impl;
	
	FArchive& Inner;
	
	const TMap<UObject*, FPackageIndex>* ObjectIndicesMap = nullptr;
	bool bSerializeObjectsInPlace = false;
	bool bSerializeSoftObjectsInPlace = false;
};

#endif
