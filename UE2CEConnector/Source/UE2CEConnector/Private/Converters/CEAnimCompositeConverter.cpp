#include "Converters/CEAnimCompositeConverter.h"

#include "Animation/AnimSequence.h"
#include "Animation/AnimComposite.h"
#include "Exporters/CEExportOptions.h"
#include "Builders/CEConvertBuilder.h"
#include "Tasks/CEAnimCompositeTask.h"


UCEAnimCompositeConverter::UCEAnimCompositeConverter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UAnimComposite::StaticClass();
}

void UCEAnimCompositeConverter::Convert(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object)
{
    UAnimComposite* AnimComposite = Cast<UAnimComposite>(Object);
    if (!AnimComposite)
    {
        return;
    }

    UCEExportOptions* Options = Builder->GetExportOptions();
    if (!Options)
    {
        return;
    }

    // Create and add the task
    Builder->SetupTask<FCEAnimCompositeTask>(Save<PERSON><PERSON>, *Builder, AnimComposite);
}