#include "MCPWebSocketServer.h"

#include "MCPFileLogger.h"

#include "IWebSocketNetworkingModule.h"
#include "Modules/ModuleManager.h"

FThreadSafeCounter FMCPWebSocketConnection::IdGenerator = FThreadSafeCounter(100);

FMCPWebSocketConnection::FMCPWebSocketConnection(INetworkingWebSocket* InSocketConnection)
    : Id(IdGenerator.Increment())
    , SocketConnection(InSocketConnection)
{
    UrlArgs = SocketConnection->GetUrlArgs();
}

FMCPWebSocketConnection::~FMCPWebSocketConnection()
{
    if (SocketConnection)
    {
        delete SocketConnection;
        SocketConnection = nullptr;
    }
}

uint16 FMCPWebSocketConnection::GetId() const
{
    return Id;
}

TArray<FString> FMCPWebSocketConnection::GetUrlArgs() const
{
    return UrlArgs;
}

bool FMCPWebSocketConnection::Send(FString Message) const
{
    // Convert FString into uint8 array.
    FTCHARToUTF8 UTF8String(*Message);

    // Send the uint8 buffer
    // Note: Due to how this socket connection is implemented, only binary messages are supported
    return SocketConnection->Send((const uint8*)UTF8String.Get(), UTF8String.Length(), false);
}

void FMCPWebSocketConnection::SetCallbacks()
{
    if (SocketConnection)
    {
        SocketConnection->SetReceiveCallBack(OnPacketReceivedCallback);
        SocketConnection->SetSocketClosedCallBack(OnClosedCallback);
    }
}

/*
 * ------------- FMCPWebSocketServer -------------
 */

FMCPWebSocketServer::FMCPWebSocketServer()
    : bLaunched(false)
{}

FMCPWebSocketServer::~FMCPWebSocketServer()
{
    Stop();
}

void FMCPWebSocketServer::EnableWebServer(TArray<FWebSocketHttpMount> InDirectoriesToServe)
{
    bEnableWebServer = true;
    DirectoriesToServe = InDirectoriesToServe;
}

bool FMCPWebSocketServer::Launch(uint16 Port)
{
    WSServer = FModuleManager::Get().LoadModuleChecked<IWebSocketNetworkingModule>(TEXT("WebSocketNetworking")).CreateServer();

    if (bEnableWebServer)
    {
        WSServer->EnableHTTPServer(DirectoriesToServe);
    }

    OnClientConnectedCallback.BindRaw(this, &FMCPWebSocketServer::OnConnectionOpened);
    bLaunched = WSServer->Init(Port, OnClientConnectedCallback);
    if (!bLaunched)
    {
        MCP_LOG_ERROR("Failed to launch Websocket server at ws://127.0.0.1:%d", Port);
        WSServer.Reset();
        OnClientConnectedCallback.Unbind();
    }
    else
    {
        MCP_LOG_INFO("Started Websocket server at ws://127.0.0.1:%d", Port);
    }
    return bLaunched;
}

bool FMCPWebSocketServer::IsLaunched() const
{
    return bLaunched;
}

bool FMCPWebSocketServer::HasConnections() const
{
    return Connections.Num() > 0;
}

bool FMCPWebSocketServer::GetFirstConnection(uint16& OutConnectionId) const
{
    if (Connections.Num() > 0)
    {
        OutConnectionId = Connections.CreateConstIterator().Key();
        return true;
    }
    return false;
}

void FMCPWebSocketServer::NameConnection(uint16 ConnectionId, const FString& Name)
{
    if (auto* Connection = Connections.Find(ConnectionId))
    {
        NamedConnections.FindOrAdd(Name) = ConnectionId;
    }
}

void FMCPWebSocketServer::RemoveName(const FString& Name)
{
    NamedConnections.Remove(Name);
}

bool FMCPWebSocketServer::GetNamedConnection(const FString& Name, uint16& OutConnectionId) const
{
    if (auto* ConnectionId = NamedConnections.Find(Name))
    {
        OutConnectionId = *ConnectionId;
        return true;
    }
    return false;
}

TArray<FString> FMCPWebSocketServer::GetConnectionNames() const
{
    TArray<FString> Names;
    for (auto [Name, ConnectionId] : NamedConnections)
    {
        Names.Add(Name);
    }
    return Names;
}

void FMCPWebSocketServer::Stop()
{
    bLaunched = false;
    Connections.Empty();
    OnClientConnectedCallback.Unbind();
    WSServer.Reset();
}

bool FMCPWebSocketServer::Close(uint16 ConnectionId)
{
    for (const FString* Key = NamedConnections.FindKey(ConnectionId); Key; Key = NamedConnections.FindKey(ConnectionId))
    {
        NamedConnections.Remove(*Key);
    }

    if (Connections.Contains(ConnectionId))
    {
        return Connections.Remove(ConnectionId) > 0;
    }
    else
    {
        MCP_LOG_WARNING("Could not close websocket connection because there was no connection=%d.", ConnectionId);
        return false;
    }
}

bool FMCPWebSocketServer::Send(uint16 ConnectionId, FString Message) const
{
    if (Connections.Contains(ConnectionId))
    {
        return Connections[ConnectionId]->Send(Message);
    }
    else
    {
        MCP_LOG_WARNING("Did not send websocket message because there was no connection=%d.", ConnectionId);
        return false;
    }
}

bool FMCPWebSocketServer::Send(const FString& ConnectionName, FString Message) const
{
    if (auto* ConnectionId = NamedConnections.Find(ConnectionName))
    {
        return Send(*ConnectionId, Message);
    }
    else
    {
        MCP_LOG_WARNING("Did not send websocket message because there was no connection named %s.", *ConnectionName);
        return false;
    }
}

void FMCPWebSocketServer::OnConnectionOpened(INetworkingWebSocket* Socket)
{
    if (!Socket)
    {
        MCP_LOG_ERROR("Websocket client connected with a null socket.");
        return;
    }
    else
    {
        MCP_LOG_INFO("Websocket client connected. Remote=%s | Local=%s", *Socket->RemoteEndPoint(true), *Socket->LocalEndPoint(true));
    }

    // Had a new client connect over websocket, store the connection with a unique ID.
    TUniquePtr<FMCPWebSocketConnection> Connection = MakeUnique<FMCPWebSocketConnection>(Socket);
    const uint16 Id = Connection->GetId();

    // Bind to socket callbacks for messages/closed.
    Connection->OnPacketReceivedCallback.BindRaw(this, &FMCPWebSocketServer::OnPacketReceived, Id);
    Connection->OnClosedCallback.BindRaw(this, &FMCPWebSocketServer::OnConnectionClosed, Id);
    Connection->SetCallbacks();
    Connections.Add(Id, MoveTemp(Connection));

    FString NamedConnectionName = "";

    if (FString UrlArg = Socket->GetUrlArgByName(TEXT("name=")); UrlArg.IsEmpty())
    {
        NamedConnectionName = FString::Printf(TEXT("Connection_%d"), Id);
    }
    else
    {
        UrlArg.Split("=", nullptr, &NamedConnectionName);
    }
    NameConnection(Id, NamedConnectionName);


    // Broadcast that we got a new websocket connection
    OnOpenConnection.Broadcast(Id);
}

void FMCPWebSocketServer::OnPacketReceived(void* Data, int32 Size, uint16 ConnectionId)
{
    if (Size > 0)
    {
        TArrayView<uint8> DataView = MakeArrayView(static_cast<uint8*>(Data), Size);
        OnMessage.Broadcast(ConnectionId, DataView);

        MessageForwarding(ConnectionId, DataView);
    }
}

void FMCPWebSocketServer::OnConnectionClosed(uint16 ConnectionId)
{
    for (const FString* Key = NamedConnections.FindKey(ConnectionId); Key; Key = NamedConnections.FindKey(ConnectionId))
    {
        NamedConnections.Remove(*Key);
    }

    Connections.Remove(ConnectionId);
    OnClosedConnection.Broadcast(ConnectionId);
}

void FMCPWebSocketServer::Tick(float DeltaTime)
{
    if (!IsLaunched())
    {
        return;
    }

    if (WSServer)
    {
        // Tick the websocket server
        WSServer->Tick();
    }
}


void FMCPWebSocketServer::MessageForwarding(uint16 ConnectionId, TArrayView<uint8> DataView)
{
    static FString Target_ALL = TEXT("All");
    static FString Target_Debug = TEXT("Debug");
    static FString Target_Server = TEXT("Server");

    FUTF8ToTCHAR TCharStr(reinterpret_cast<const char*>(DataView.GetData()), DataView.Num());
    FString MessageJson(TCharStr);
    TSharedPtr<FJsonObject> Message;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(MessageJson);
    if (FJsonSerializer::Deserialize(Reader, Message) && Message.IsValid())
    {
        FString target = Target_ALL;

        if (FString value; Message->TryGetStringField("target", value))
            target = value;

        if (target == Target_Server)
        {
            ProcessMessage(Message);
        }
        else if (target == Target_ALL)
        {
            for (auto& [id, connection] : GetConnections())
            {
                if (id != ConnectionId)
                    connection->Send(MessageJson);
            }
        }
        else
        {
            Send(target, MessageJson);
        }

        if (uint16 debug_Id; GetNamedConnection(Target_Debug, debug_Id))
        {
            Send(debug_Id, MessageJson);
        }
    }
}

void FMCPWebSocketServer::ProcessMessage(TSharedPtr<FJsonObject> Message)
{

}
