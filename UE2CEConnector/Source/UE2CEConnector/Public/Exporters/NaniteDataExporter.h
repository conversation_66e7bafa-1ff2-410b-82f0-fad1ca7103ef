#pragma once

#include "CoreMinimal.h"
#include "Engine/StaticMesh.h"
#include "Rendering/NaniteResources.h"

/**
 * 简化的Nanite数据导出格式，用于自研引擎
 * 设计原则：
 * 1. 保持数据结构清晰简单
 * 2. 去除UE特有的复杂性
 * 3. 保留核心的虚拟化几何特性
 * 4. 便于自研引擎实现和调试
 */

namespace CEAssetExchange
{
    // 简化的集群数据结构
    struct SimplifiedCluster
    {
        // 基本几何信息
        uint32 NumVertices;
        uint32 NumTriangles;
        uint32 MaterialIndex;
        
        // LOD和边界信息
        float LODError;
        float EdgeLength;
        float BoundingSphere[4];    // x,y,z,radius
        float BoundingBox[6];       // min_x,min_y,min_z,max_x,max_y,max_z
        
        // 顶点数据偏移（在顶点缓冲区中的位置）
        uint32 VertexDataOffset;
        uint32 IndexDataOffset;
        
        // 层次结构信息
        uint32 ParentClusterIndex;  // INDEX_NONE表示根集群
        uint32 ChildClusterStart;   // 子集群起始索引
        uint32 NumChildClusters;    // 子集群数量
        uint32 HierarchyLevel;      // 在层次结构中的级别
        
        // 标志位
        uint32 Flags;               // 是否为叶子节点等标志
    };
    
    // 简化的顶点数据
    struct SimplifiedVertex
    {
        float Position[3];
        float Normal[3];
        float Tangent[4];           // xyz为切线，w为手性
        float UV[2];                // 主UV坐标
        uint32 Color;               // RGBA颜色，打包为uint32
    };
    
    // 简化的层次节点
    struct SimplifiedHierarchyNode
    {
        float BoundingSphere[4];    // x,y,z,radius
        float BoundingBox[6];       // min_x,min_y,min_z,max_x,max_y,max_z
        float MinLODError;
        float MaxParentLODError;
        
        uint32 ChildStartIndex;     // 子节点/集群起始索引
        uint32 NumChildren;         // 子节点/集群数量
        bool bIsLeaf;               // 是否为叶子节点（指向集群）
        uint32 Level;               // 层次级别
    };
    
    // 简化的材质范围
    struct SimplifiedMaterialRange
    {
        uint32 MaterialIndex;
        uint32 TriangleStart;
        uint32 TriangleCount;
    };
    
    // 主要的Nanite网格数据结构
    struct SimplifiedNaniteMesh
    {
        // 基本信息
        uint32 Version;             // 数据格式版本
        uint32 NumInputTriangles;   // 原始三角形数量
        uint32 NumInputVertices;    // 原始顶点数量
        uint32 NumClusters;         // 总集群数量
        uint32 NumHierarchyNodes;   // 层次节点数量
        
        // 精度信息
        int32 PositionPrecision;    // 位置精度
        int32 NormalPrecision;      // 法线精度
        int32 TangentPrecision;     // 切线精度
        
        // 边界信息
        float MeshBounds[6];        // 整个网格的边界框
        
        // 数据数组大小
        uint32 VertexDataSize;      // 顶点数据总大小（字节）
        uint32 IndexDataSize;       // 索引数据总大小（字节）
        uint32 ClusterDataSize;     // 集群数据大小
        uint32 HierarchyDataSize;   // 层次数据大小
        uint32 MaterialRangeCount;  // 材质范围数量
        
        // 数据指针（序列化时为偏移量）
        SimplifiedCluster* Clusters;
        SimplifiedHierarchyNode* HierarchyNodes;
        SimplifiedVertex* VertexData;
        uint32* IndexData;
        SimplifiedMaterialRange* MaterialRanges;
        
        // 根集群信息
        uint32 NumRootClusters;
        uint32* RootClusterIndices;
    };
    
    // LOD级别信息
    struct SimplifiedLODLevel
    {
        float ScreenSize;           // 屏幕大小阈值
        uint32 ClusterStart;        // 该LOD级别的集群起始索引
        uint32 ClusterCount;        // 该LOD级别的集群数量
        uint32 TriangleCount;       // 该LOD级别的三角形数量
        float Error;                // LOD误差
    };
    
    // 完整的导出数据包
    struct NaniteExportData
    {
        SimplifiedNaniteMesh Mesh;
        TArray<SimplifiedLODLevel> LODLevels;
        
        // 辅助信息
        FString SourceMeshName;
        uint32 SourceMeshHash;      // 用于验证数据一致性
        
        // 统计信息
        struct Statistics
        {
            uint32 OriginalTriangles;
            uint32 NaniteTriangles;
            uint32 CompressionRatio;    // 压缩比例（百分比）
            uint32 MaxHierarchyDepth;   // 最大层次深度
            float BuildTime;            // 构建时间（秒）
        } Stats;
    };
}

/**
 * Nanite数据导出器
 */
class EXPORT_API FNaniteDataExporter
{
public:
    /**
     * 导出Nanite网格数据到简化格式
     * @param StaticMesh 源静态网格
     * @param OutExportData 导出的数据
     * @return 是否成功导出
     */
    static bool ExportNaniteData(const UStaticMesh* StaticMesh, CEAssetExchange::NaniteExportData& OutExportData);
    
    /**
     * 将导出数据序列化到文件
     * @param ExportData 要序列化的数据
     * @param FilePath 输出文件路径
     * @return 是否成功保存
     */
    static bool SaveToFile(const CEAssetExchange::NaniteExportData& ExportData, const FString& FilePath);
    
    /**
     * 从文件加载导出数据
     * @param FilePath 文件路径
     * @param OutExportData 加载的数据
     * @return 是否成功加载
     */
    static bool LoadFromFile(const FString& FilePath, CEAssetExchange::NaniteExportData& OutExportData);
    
    /**
     * 验证Nanite数据的完整性
     * @param ExportData 要验证的数据
     * @return 验证结果
     */
    static bool ValidateData(const CEAssetExchange::NaniteExportData& ExportData);

private:
    // 内部辅助函数
    static bool ExtractClusterData(const Nanite::FResources& NaniteResources, CEAssetExchange::SimplifiedNaniteMesh& OutMesh);
    static bool ExtractHierarchyData(const Nanite::FResources& NaniteResources, CEAssetExchange::SimplifiedNaniteMesh& OutMesh);
    static bool ExtractVertexData(const Nanite::FResources& NaniteResources, CEAssetExchange::SimplifiedNaniteMesh& OutMesh);
    static void CalculateStatistics(const Nanite::FResources& NaniteResources, CEAssetExchange::NaniteExportData& ExportData);
};
