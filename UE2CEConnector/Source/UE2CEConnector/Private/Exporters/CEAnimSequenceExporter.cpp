#include "Exporters/CEAnimSequenceExporter.h"
#include "Converters/CEAnimSequenceConverter.h"
#include "Animation/AnimSequence.h"

UCEAnimSequenceExporter::UCEAnimSequenceExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UAnimSequence::StaticClass();
    FormatExtension.Add(TEXT("nda"));
    FormatDescription.Add(TEXT("CE Animation Sequence"));
}
