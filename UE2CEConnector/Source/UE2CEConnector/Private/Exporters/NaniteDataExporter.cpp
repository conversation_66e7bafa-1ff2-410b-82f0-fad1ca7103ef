#include "Exporters/NaniteDataExporter.h"
#include "Engine/StaticMesh.h"
#include "Rendering/NaniteResources.h"
#include "StaticMeshResources.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"
#include <Developer/NaniteBuilder/Private/Cluster.h>
#include <Developer/NaniteBuilder/Private/ClusterDAG.h>
#include <Developer/NaniteBuilder/Private/NaniteEncode.cpp>

DEFINE_LOG_CATEGORY_STATIC(LogNaniteExporter, Log, All);

bool FNaniteDataExporter::ExportNaniteData(const UStaticMesh* StaticMesh, CEAssetExchange::NaniteExportData& OutExportData)
{
    if (!StaticMesh)
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("StaticMesh is null"));
        return false;
    }

    // 检查是否启用了Nanite
    if (!StaticMesh->NaniteSettings.bEnabled)
    {
        UE_LOG(LogNaniteExporter, Warning, TEXT("Nanite is not enabled for mesh: %s"), *StaticMesh->GetName());
        return false;
    }

    // 获取Nanite资源
    const FStaticMeshRenderData* RenderData = StaticMesh->GetRenderData();
    if (!RenderData || !RenderData->NaniteResourcesPtr)
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("No valid Nanite resources found for mesh: %s"), *StaticMesh->GetName());
        return false;
    }

    const Nanite::FResources& NaniteResources = *RenderData->NaniteResourcesPtr;
    
    // 初始化导出数据
    OutExportData.SourceMeshName = StaticMesh->GetName();
    OutExportData.SourceMeshHash = GetTypeHash(StaticMesh->GetPathName());
    
    // 填充基本信息
    CEAssetExchange::NaniteMesh& Mesh = OutExportData.Mesh;
    Mesh.Version = 1;
    Mesh.NumInputTriangles = NaniteResources.NumInputTriangles;
    Mesh.NumInputVertices = NaniteResources.NumInputVertices;
    Mesh.NumClusters = NaniteResources.NumClusters;
    Mesh.NumHierarchyNodes = NaniteResources.HierarchyNodes.Num();
    Mesh.PositionPrecision = NaniteResources.PositionPrecision;
    Mesh.NormalPrecision = NaniteResources.NormalPrecision;
    Mesh.TangentPrecision = NaniteResources.TangentPrecision;

    // 计算网格边界
    if (StaticMesh->GetRenderData()->LODResources.Num() > 0)
    {
        const FBoxSphereBounds& Bounds = StaticMesh->GetBounds();
        const FBox& Box = Bounds.GetBox();
        Mesh.MeshBounds[0] = Box.Min.X;
        Mesh.MeshBounds[1] = Box.Min.Y;
        Mesh.MeshBounds[2] = Box.Min.Z;
        Mesh.MeshBounds[3] = Box.Max.X;
        Mesh.MeshBounds[4] = Box.Max.Y;
        Mesh.MeshBounds[5] = Box.Max.Z;
    }

    // 使用ReadPages函数读取Nanite页面数据
    if (!ReadPages(NaniteResources, OutExportData))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to read Nanite pages data"));
        return false;
    }

    // 提取集群数据
    if (!ExtractClusterData(NaniteResources, Mesh))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to extract cluster data"));
        return false;
    }

    // 提取层次结构数据
    if (!ExtractHierarchyData(NaniteResources, Mesh))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to extract hierarchy data"));
        return false;
    }

    // 提取顶点数据
    if (!ExtractVertexData(NaniteResources, Mesh))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to extract vertex data"));
        return false;
    }

    // 计算统计信息
    CalculateStatistics(NaniteResources, OutExportData);

    UE_LOG(LogNaniteExporter, Log, TEXT("Successfully exported Nanite data for mesh: %s"), *StaticMesh->GetName());
    UE_LOG(LogNaniteExporter, Log, TEXT("  Clusters: %d, Hierarchy Nodes: %d"), Mesh.NumClusters, Mesh.NumHierarchyNodes);
    UE_LOG(LogNaniteExporter, Log, TEXT("  Original Triangles: %d, Vertices: %d"), Mesh.NumInputTriangles, Mesh.NumInputVertices);

    return true;
}

bool FNaniteDataExporter::ExtractClusterData(const Nanite::FResources& NaniteResources, CEAssetExchange::NaniteMesh& OutMesh)
{
    // 注意：这里需要访问Nanite的内部数据结构
    // 由于Nanite数据是高度压缩和优化的，我们需要通过特殊方式解析
    
    UE_LOG(LogNaniteExporter, Warning, TEXT("Cluster data extraction requires access to Nanite internal structures"));
    UE_LOG(LogNaniteExporter, Warning, TEXT("This is a simplified implementation - full extraction needs deeper integration"));
    
    // 分配集群数组
    OutMesh.ClusterDataSize = OutMesh.NumClusters * sizeof(CEAssetExchange::Cluster);
    
    // 这里应该解析RootData和StreamablePages中的集群信息
    // 由于Nanite数据格式复杂，这里提供框架代码

    
    return true;
}

bool FNaniteDataExporter::ExtractHierarchyData(const Nanite::FResources& NaniteResources, CEAssetExchange::NaniteMesh& OutMesh)
{
    const TArray<Nanite::FPackedHierarchyNode>& HierarchyNodes = NaniteResources.HierarchyNodes;
    
    OutMesh.HierarchyDataSize = HierarchyNodes.Num() * sizeof(CEAssetExchange::HierarchyNode);
    
    // 这里需要解包Nanite的层次结构数据
    // FPackedHierarchyNode包含了压缩的边界和引用信息
    
    UE_LOG(LogNaniteExporter, Log, TEXT("Processing %d hierarchy nodes"), HierarchyNodes.Num());
    
    return true;
}

bool FNaniteDataExporter::ExtractVertexData(const Nanite::FResources& NaniteResources, CEAssetExchange::NaniteMesh& OutMesh)
{
    // Nanite的顶点数据是高度压缩的，存储在页面中
    // 需要通过Nanite的解码器来获取实际的顶点数据
    
    UE_LOG(LogNaniteExporter, Warning, TEXT("Vertex data extraction requires Nanite decompression"));
    UE_LOG(LogNaniteExporter, Warning, TEXT("Consider using fallback LOD data for initial implementation"));
    
    return true;
}

void FNaniteDataExporter::CalculateStatistics(const Nanite::FResources& NaniteResources, CEAssetExchange::NaniteExportData& ExportData)
{
    CEAssetExchange::NaniteExportData::Statistics& Stats = ExportData.Stats;
    
    Stats.OriginalTriangles = NaniteResources.NumInputTriangles;
    Stats.NaniteTriangles = 0; // 需要从集群数据计算
    Stats.CompressionRatio = 0; // 需要计算压缩比
    Stats.MaxHierarchyDepth = 0; // 需要遍历层次结构计算
    Stats.BuildTime = 0.0f; // 构建时间（如果可获取）
}

bool FNaniteDataExporter::ValidateData(const CEAssetExchange::NaniteExportData& ExportData)
{
    const CEAssetExchange::NaniteMesh& Mesh = ExportData.Mesh;

    // 基本验证
    if (Mesh.Version == 0 || Mesh.NumClusters == 0)
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Invalid mesh data: Version=%d, Clusters=%d"), Mesh.Version, Mesh.NumClusters);
        return false;
    }

    if (ExportData.SourceMeshName.IsEmpty())
    {
        UE_LOG(LogNaniteExporter, Warning, TEXT("Source mesh name is empty"));
    }

    UE_LOG(LogNaniteExporter, Log, TEXT("Nanite data validation passed"));
    return true;
}

// 模仿Nanite::WritePages的ReadPages函数
bool FNaniteDataExporter::ReadPages(const Nanite::FResources& NaniteResources,
                                   CEAssetExchange::NaniteExportData& OutExportData)
{
    UE_LOG(LogNaniteExporter, Log, TEXT("=== ReadPages: 开始读取Nanite页面数据 ==="));

    // 1. 读取根页面数据
    if (!ReadRootPages(NaniteResources, OutExportData))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to read root pages"));
        return false;
    }

    // 2. 读取流式页面数据
    if (!ReadStreamablePages(NaniteResources, OutExportData))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to read streamable pages"));
        return false;
    }

    // 3. 读取层次结构数据
    if (!ReadHierarchyData(NaniteResources, OutExportData))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to read hierarchy data"));
        return false;
    }

    // 4. 读取页面依赖关系
    if (!ReadPageDependencies(NaniteResources, OutExportData))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to read page dependencies"));
        return false;
    }

    UE_LOG(LogNaniteExporter, Log, TEXT("ReadPages completed successfully"));
    return true;
}

bool FNaniteDataExporter::ReadRootPages(const Nanite::FResources& NaniteResources,
                                       CEAssetExchange::NaniteExportData& OutExportData)
{
    UE_LOG(LogNaniteExporter, Log, TEXT("Reading root pages..."));

    const TArray<uint8>& RootData = NaniteResources.RootData;
    if (RootData.Num() == 0)
    {
        UE_LOG(LogNaniteExporter, Warning, TEXT("No root data found"));
        return true; // 不是错误，可能没有根页面
    }

    uint32 DataOffset = 0;
    const uint32 NumRootPages = NaniteResources.NumRootPages;

    UE_LOG(LogNaniteExporter, Log, TEXT("  Root pages count: %d"), NumRootPages);
    UE_LOG(LogNaniteExporter, Log, TEXT("  Root data size: %d bytes"), RootData.Num());

    // 解析根页面数据
    for (uint32 PageIndex = 0; PageIndex < NumRootPages && DataOffset < (uint32)RootData.Num(); PageIndex++)
    {
        if (!ReadSinglePage(RootData.GetData(), DataOffset, RootData.Num(), PageIndex, true, OutExportData))
        {
            UE_LOG(LogNaniteExporter, Error, TEXT("Failed to read root page %d"), PageIndex);
            return false;
        }
    }

    return true;
}

bool FNaniteDataExporter::ReadStreamablePages(const Nanite::FResources& NaniteResources,
                                             CEAssetExchange::NaniteExportData& OutExportData)
{
    UE_LOG(LogNaniteExporter, Log, TEXT("Reading streamable pages..."));

    const FByteBulkData& StreamablePages = NaniteResources.StreamablePages;
    if (StreamablePages.GetBulkDataSize() == 0)
    {
        UE_LOG(LogNaniteExporter, Warning, TEXT("No streamable pages found"));
        return true; // 不是错误，可能只有根页面
    }

    // 获取流式页面数据
    const void* BulkDataPtr = StreamablePages.LockReadOnly();
    if (!BulkDataPtr)
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to lock streamable pages data"));
        return false;
    }

    const uint8* StreamableData = static_cast<const uint8*>(BulkDataPtr);
    const uint32 StreamableDataSize = StreamablePages.GetBulkDataSize();

    UE_LOG(LogNaniteExporter, Log, TEXT("  Streamable data size: %d bytes"), StreamableDataSize);

    // 解析流式页面
    const TArray<Nanite::FPageStreamingState>& PageStates = NaniteResources.PageStreamingStates;
    const uint32 NumStreamablePages = PageStates.Num() - NaniteResources.NumRootPages;

    for (uint32 i = 0; i < NumStreamablePages; i++)
    {
        const uint32 PageIndex = NaniteResources.NumRootPages + i;
        if (PageIndex >= (uint32)PageStates.Num())
            break;

        const Nanite::FPageStreamingState& PageState = PageStates[PageIndex];

        if (PageState.BulkOffset + PageState.BulkSize > StreamableDataSize)
        {
            UE_LOG(LogNaniteExporter, Error, TEXT("Page %d data out of bounds"), PageIndex);
            continue;
        }

        uint32 PageDataOffset = PageState.BulkOffset;
        if (!ReadSinglePage(StreamableData, PageDataOffset, StreamableDataSize, PageIndex, false, OutExportData))
        {
            UE_LOG(LogNaniteExporter, Warning, TEXT("Failed to read streamable page %d"), PageIndex);
        }
    }

    StreamablePages.Unlock();
    return true;
}

bool FNaniteDataExporter::ReadSinglePage(const uint8* PageData, uint32& DataOffset, uint32 MaxDataSize,
                                        uint32 PageIndex, bool bIsRootPage,
                                        CEAssetExchange::NaniteExportData& OutExportData)
{
    if (DataOffset >= MaxDataSize)
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Page %d: Data offset out of bounds"), PageIndex);
        return false;
    }

    // 读取Fixup Chunk (如果存在)
    // Fixup chunk在页面数据之前，包含页面间的引用修复信息
    if (!ReadFixupChunk(PageData, DataOffset, MaxDataSize, PageIndex, OutExportData))
    {
        UE_LOG(LogNaniteExporter, Warning, TEXT("Failed to read fixup chunk for page %d"), PageIndex);
    }

    // 定义页面磁盘头部结构（模仿Nanite的FPageDiskHeader）
    struct FPageDiskHeader
    {
        uint32 NumClusters;
        uint32 NumRawFloat4s;
        uint32 NumVertexRefs;
        uint32 DecodeInfoOffset;
        uint32 StripBitmaskOffset;
        uint32 VertexRefBitmaskOffset;
    };
    
    // 读取页面磁盘头部
    if (DataOffset + sizeof(FPageDiskHeader) > MaxDataSize)
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Page %d: Not enough data for page header"), PageIndex);
        return false;
    }

    const FPageDiskHeader* PageHeader = reinterpret_cast<const FPageDiskHeader*>(PageData + DataOffset);
    DataOffset += sizeof(FPageDiskHeader);

    UE_LOG(LogNaniteExporter, Log, TEXT("Page %d: Clusters=%d, RawFloat4s=%d, VertexRefs=%d"),
        PageIndex, PageHeader->NumClusters, PageHeader->NumRawFloat4s, PageHeader->NumVertexRefs);

    // 读取集群头部数据
    if (!ReadClusterHeaders(PageData, DataOffset, MaxDataSize, PageHeader->NumClusters, PageIndex, OutExportData))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to read cluster headers for page %d"), PageIndex);
        return false;
    }

    // 读取页面的其他数据段
    if (!ReadPageDataSections(PageData, DataOffset, MaxDataSize, *PageHeader, PageIndex, OutExportData))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to read page data sections for page %d"), PageIndex);
        return false;
    }

    return true;
}

bool FNaniteDataExporter::ReadFixupChunk(const uint8* PageData, uint32& DataOffset, uint32 MaxDataSize,
                                        uint32 PageIndex, CEAssetExchange::NaniteExportData& OutExportData)
{
    // Fixup chunk结构（模仿Nanite的FFixupChunk）
    struct FFixupHeader
    {
        uint16 Magic;
        uint16 NumClusters;
        uint16 NumHierarchyFixups;
        uint16 NumClusterFixups;
    };

    if (DataOffset + sizeof(FFixupHeader) > MaxDataSize)
    {
        return false; // 没有足够数据，可能没有fixup chunk
    }

    const FFixupHeader* FixupHeader = reinterpret_cast<const FFixupHeader*>(PageData + DataOffset);

    // 检查魔数（NANITE_FIXUP_MAGIC的值需要从Nanite源码中获取）
    const uint16 NANITE_FIXUP_MAGIC = 0x4E46; // 'NF'
    if (FixupHeader->Magic != NANITE_FIXUP_MAGIC)
    {
        return false; // 不是有效的fixup chunk
    }

    DataOffset += sizeof(FFixupHeader);

    UE_LOG(LogNaniteExporter, Log, TEXT("Page %d Fixup: HierarchyFixups=%d, ClusterFixups=%d"),
        PageIndex, FixupHeader->NumHierarchyFixups, FixupHeader->NumClusterFixups);

    // 跳过fixup数据（在简化导出中我们不需要这些引用修复信息）
    const uint32 HierarchyFixupSize = FixupHeader->NumHierarchyFixups * 8; // 假设每个fixup 8字节
    const uint32 ClusterFixupSize = FixupHeader->NumClusterFixups * 16;    // 假设每个fixup 16字节

    DataOffset += HierarchyFixupSize + ClusterFixupSize;

    return true;
}

bool FNaniteDataExporter::ReadClusterHeaders(const uint8* PageData, uint32& DataOffset, uint32 MaxDataSize,
                                            uint32 NumClusters, uint32 PageIndex,
                                            CEAssetExchange::NaniteExportData& OutExportData)
{
    // 集群磁盘头部结构（模仿Nanite的FClusterDiskHeader）
    struct FClusterDiskHeader
    {
        uint32 IndexDataOffset;
        uint32 PageClusterMapOffset;
        uint32 VertexRefDataOffset;
        uint32 LowBytesOffset;
        uint32 MidBytesOffset;
        uint32 HighBytesOffset;
        uint32 NumVertexRefs;
        uint32 NumPrevRefVerticesBeforeDwords;
        uint32 NumPrevNewVerticesBeforeDwords;
    };

    const uint32 ClusterHeadersSize = NumClusters * sizeof(FClusterDiskHeader);
    if (DataOffset + ClusterHeadersSize > MaxDataSize)
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Page %d: Not enough data for cluster headers"), PageIndex);
        return false;
    }

    const FClusterDiskHeader* ClusterHeaders = reinterpret_cast<const FClusterDiskHeader*>(PageData + DataOffset);
    DataOffset += ClusterHeadersSize;

    // 处理每个集群头部
    for (uint32 ClusterIndex = 0; ClusterIndex < NumClusters; ClusterIndex++)
    {
        const FClusterDiskHeader& ClusterHeader = ClusterHeaders[ClusterIndex];

        UE_LOG(LogNaniteExporter, VeryVerbose, TEXT("  Cluster %d: VertexRefs=%d, IndexOffset=%d"),
            ClusterIndex, ClusterHeader.NumVertexRefs, ClusterHeader.IndexDataOffset);

        // 在这里可以提取集群的基本信息到简化格式
        // 实际的顶点和索引数据需要从页面的数据段中解析
    }

    return true;
}

bool FNaniteDataExporter::ReadPageDataSections(const uint8* PageData, uint32& DataOffset, uint32 MaxDataSize,
                                              const FPageDiskHeader& PageHeader, uint32 PageIndex,
                                              CEAssetExchange::NaniteExportData& OutExportData)
{
    // 这里需要读取页面的各个数据段：
    // - 材质范围数据
    // - 顶点重用批次信息
    // - 骨骼影响数据（如果有）
    // - 扩展数据
    // - 解码信息
    // - 索引数据
    // - 位置数据
    // - 属性数据

    UE_LOG(LogNaniteExporter, VeryVerbose, TEXT("Reading page %d data sections..."), PageIndex);

    // 由于Nanite的数据格式非常复杂且高度压缩，这里只做基本的数据段识别
    // 完整的解析需要使用Nanite的内部解码器

    // 跳过剩余的页面数据（在实际实现中需要解析这些数据）
    // 这里只是演示框架

    return true;
}

bool FNaniteDataExporter::ReadHierarchyData(const Nanite::FResources& NaniteResources,
                                           CEAssetExchange::NaniteExportData& OutExportData)
{
    UE_LOG(LogNaniteExporter, Log, TEXT("Reading hierarchy data..."));

    const TArray<Nanite::FPackedHierarchyNode>& HierarchyNodes = NaniteResources.HierarchyNodes;
    const TArray<uint32>& HierarchyRootOffsets = NaniteResources.HierarchyRootOffsets;

    UE_LOG(LogNaniteExporter, Log, TEXT("  Hierarchy nodes: %d"), HierarchyNodes.Num());
    UE_LOG(LogNaniteExporter, Log, TEXT("  Root offsets: %d"), HierarchyRootOffsets.Num());

    // 解析层次结构节点
    for (int32 NodeIndex = 0; NodeIndex < HierarchyNodes.Num(); NodeIndex++)
    {
        const Nanite::FPackedHierarchyNode& PackedNode = HierarchyNodes[NodeIndex];

        // 这里需要解包FPackedHierarchyNode到SimplifiedHierarchyNode
        // FPackedHierarchyNode包含压缩的边界和引用信息

        UE_LOG(LogNaniteExporter, VeryVerbose, TEXT("  Node %d: Processing packed hierarchy node"), NodeIndex);

        // 在实际实现中，需要调用Nanite的解包函数来获取实际的层次数据
    }

    return true;
}

bool FNaniteDataExporter::ReadPageDependencies(const Nanite::FResources& NaniteResources,
                                              CEAssetExchange::NaniteExportData& OutExportData)
{
    UE_LOG(LogNaniteExporter, Log, TEXT("Reading page dependencies..."));

    const TArray<uint32>& PageDependencies = NaniteResources.PageDependencies;
    const TArray<Nanite::FPageStreamingState>& PageStates = NaniteResources.PageStreamingStates;

    UE_LOG(LogNaniteExporter, Log, TEXT("  Page dependencies: %d"), PageDependencies.Num());
    UE_LOG(LogNaniteExporter, Log, TEXT("  Page streaming states: %d"), PageStates.Num());

    // 处理页面依赖关系
    for (int32 StateIndex = 0; StateIndex < PageStates.Num(); StateIndex++)
    {
        const Nanite::FPageStreamingState& State = PageStates[StateIndex];

        UE_LOG(LogNaniteExporter, VeryVerbose, TEXT("  Page %d: Dependencies start=%d, num=%d, depth=%d"),
            StateIndex, State.DependenciesStart, State.DependenciesNum, State.MaxHierarchyDepth);

        // 在简化格式中，我们可能不需要完整的依赖关系
        // 但可以提取一些基本的层次深度信息
    }

    return true;
}
