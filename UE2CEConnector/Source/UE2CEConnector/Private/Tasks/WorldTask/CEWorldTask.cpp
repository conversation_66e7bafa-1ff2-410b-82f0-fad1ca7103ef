#include "CEWorldTask.h"
#include "ResourceAsset_generated.h"
#include "ImportMeshAssetData_generated.h"
#include "CETask.h"
#include "Exchange/CEAssetExchange.h"
#include "CELog.h"
#include "Exchange/MaterialFxExchange.h"
#include "Exchange/StringExchange.h"

FCEWorldTask::FCEWorldTask(const FString& path, FCEConvertBuilder& builder, const UWorld* world)
    : FCETask{ECETaskPriority::World, path, builder, world}
      , mWorld{world} {}

FString FCEWorldTask::GetName() const
{
    return (mWorld->GetMapName());
}

bool FCEWorldTask::Complete()
{
    // TODO export world by cross engine sdk
    if (FPaths::GetExtension(mSavePath) == "world")
    {
        return ExportWorldBySDK();
    }
    else
    {
        return ExportWorldByAssetExchange();
    }
}

bool FCEWorldTask::ExportWorldByAssetExchange()
{
    const auto ExportOptions = mBuilder.GetOptions();

    auto worldFileName = FPaths::GetCleanFilename(mSavePath);

    using namespace CEAssetExchange;

    FAssetExchangeInput Input;
    Input.Options = ExportOptions;
    Input.Sdk = mBuilder.AssetExchangeSdk.get();

    SDKSettings Settings;
    Settings.mBalanceMode = ExportOptions->bUseAssetExchangeSdkBalanceMode;
    Settings.mBalanceModeJobCount = 1;
    Settings.mOverwriteExistingTexture = ExportOptions->bOverwriteExistingTexture;
    Settings.mOverwriteExistingMaterial = ExportOptions->bOverwriteExistingMaterial;
    Settings.mOverwriteExistingMesh = ExportOptions->bOverwriteExistingMesh;
    Settings.mOverwriteExistingWorld = ExportOptions->bOverwriteExistingLevel;
    Input.Sdk->BeginAssemble(&Settings);

    const bool AsCEPrefab = ExportOptions->bExportLevelAsCEPrefab;
    if (AsCEPrefab)
        worldFileName = FPaths::ChangeExtension(worldFileName, "prefab");
    Input.RelativePath = StringMapper::Instance().FromUEToCEString(*FPaths::Combine(TEXT("Contents"), AsCEPrefab ? TEXT("Prefab") : TEXT("World"), worldFileName));
    bool bSuccess = ExportWorld(mBuilder, *mWorld, &Input, AsCEPrefab);

    return bSuccess;
}

bool FCEWorldTask::ExportWorldBySDK()
{
    CEAssetExchange::MaterialFxMapper::Instance().GetMaterialFxExported().Empty();
    cesdk::ExportGameWorld(
        CEAssetExchange::StringMapper::Instance().FromUEToCEString(*FPaths::ConvertRelativePathToFull(mSavePath)),
        [this](cesdk::cegf::SDKGameWorld GameWorld) {
            cesdk::cegf::SDKGameObject RootGameObject = GameWorld.GetRootGameObject();
            for (int32 Index = 0; Index < mWorld->GetNumLevels(); Index++)
            {
                ULevel* Level = mWorld->GetLevel(Index);

                for (AActor* Actor : Level->Actors)
                {
                    if (Actor == nullptr)
                        continue;

                    mBuilder.AddGameObjectFromActor(GameWorld, RootGameObject, Actor);
                }
            }
        });

    return true;
}

bool FCEWorldTask::CollectDependencyTask()
{
    FCETask::CollectDependencyTask();
    for (int32 Index = 0; Index < mWorld->GetNumLevels(); Index++)
    {
        ULevel* Level = mWorld->GetLevel(Index);

        for (AActor* Actor : Level->Actors)
        {
            if (Actor == nullptr)
                continue;

            mBuilder.CollectActorDependencyAsset(Actor);
        }
    }
    return true;
}

