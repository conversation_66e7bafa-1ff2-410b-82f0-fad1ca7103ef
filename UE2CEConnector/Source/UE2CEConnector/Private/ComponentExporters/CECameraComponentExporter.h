#pragma once
#include "CoreMinimal.h"
#include "CEBaseComponentExporter.h"
#include "CECameraComponentExporter.generated.h"


UCLASS()
class UCECameraComponentExporter : public UCEBaseComponentExporter
{
    GENERATED_UCLASS_BODY()
public:
    virtual CEAssetExchange::IEntity* AddEntityFromSceneComponent(FCEConvertBuilder& InBuilder,
                                                                  CEAssetExchange::IWorldAssemble* InWorldAssemble,
                                                                  USceneComponent* InSceneComponent,
                                                                  CEAssetExchange::IEntity* InParentEntity,
                                                                  USceneComponent* InExportComponent,
                                                                  CEAssetExchange::FAssetExchangeInput* InInput) override;
    virtual cesdk::cegf::SDKGameObject AddGameObjectFromSceneComponent(FCEConvertBuilder& InBuilder,
                                                                       ::cesdk::cegf::SDKGameWorld InGameWorld,
                                                                       ::cesdk::cegf::SDKGameObject InParentGameObject,
                                                                       USceneComponent* InExportComponent) override;
};