#include "MCPConvertTraceHandler.h"
#include "TraceExportHelper.h"
#include "MCPFileLogger.h"

TSharedPtr<FJsonObject> FMCPConvertTraceHandler::Execute(const TSharedPtr<FJsonObject>& Params, FSocket* ClientSocket)
{
    FString TraceFile;
    bool hasTraceFile = Params->TryGetStringField(TEXT("trace_file"), TraceFile);

    FString CSVFile;
    bool hasCSVFile = Params->TryGetStringField(TEXT("csv_file"), CSVFile);

    if(!hasTraceFile || !hasCSVFile)
    {
        MCP_LOG_WARNING("Missing 'trace_file' or 'csv_file' field in convert_trace command");
        return CreateErrorResponse("Missing 'trace_file' or 'csv_file' field. You must provide either asset path or destination path.");
    }

    bool success = FTraceExportHelper::ConvertTraceToCSVFile(FTraceExportOption{}, *TraceFile, *CSVFile);

    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    if (success)
    {
        Response->SetStringField(TEXT("status"), TEXT("success"));
        Response->SetStringField(TEXT("message"), TEXT("convert trace file success"));    
    }
    else
    {
        Response->SetStringField(TEXT("status"), TEXT("error"));
        Response->SetStringField(TEXT("message"), TEXT("convert trace file failed"));    
    }
    
    return Response;
}
