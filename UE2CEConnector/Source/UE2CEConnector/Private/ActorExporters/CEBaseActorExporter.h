#pragma once

#include "CoreMinimal.h"
#include "AssetExchange/Include/CrossEngineSDK.h"
#include "CEBaseActorExporter.generated.h"

namespace CEAssetExchange {
struct IEntity;
struct IWorldAssemble;
struct FAssetExchangeInput;
}

class FCEConvertBuilder;
class AActor;

UCLASS(Abstract)
class UCEBaseActorExporter : public UObject
{
    GENERATED_UCLASS_BODY()
public:
    /**
     * Return structure for the AddEntityFromActor function.
     * Contains the created entity and a flag indicating whether child components should be processed.
     */
    struct AddEntityFromActorRetType
    {
        CEAssetExchange::IEntity* Entity = nullptr;
        bool bNeedWalkChildren = true;
    };

    /**
     * Adds an entity to the world assemble from an actor.
     * 
     * @param InBuilder - The conversion builder to use
     * @param InWorldAssemble - The world assemble to add the entity to
     * @param InSceneComponent - The scene component being processed
     * @param LevelEntity - The parent entity (usually the level entity)
     * @param InExportActor - The actor to export
     * @param InInput - The asset exchange input data
     * @return A structure containing the created entity and a flag indicating whether child components should be processed
     */
    virtual AddEntityFromActorRetType AddEntityFromActor(
        FCEConvertBuilder& InBuilder,
        CEAssetExchange::IWorldAssemble* InWorldAssemble,
        USceneComponent* InSceneComponent,
        CEAssetExchange::IEntity* LevelEntity,
        AActor* InExportActor,
        CEAssetExchange::FAssetExchangeInput* InInput);

    /**
     * Adds a game object to the game world from an actor.
     * 
     * @param InBuilder - The conversion builder to use
     * @param InGameWorld - The game world to add the game object to
     * @param InParentGameObject - The parent game object (if any)
     * @param InExportActor - The actor to export
     * @return The created game object
     */
    virtual cesdk::cegf::SDKGameObject AddGameObjectFromActor(
        FCEConvertBuilder& InBuilder,
        ::cesdk::cegf::SDKGameWorld InGameWorld,
        ::cesdk::cegf::SDKGameObject InParentGameObject,
        AActor* InExportActor);

    /**
     * Collects all assets that the actor depends on.
     * 
     * @param InBuilder - The conversion builder to use
     * @param InActor - The actor to collect dependencies for
     */
    virtual void CollectDependencyAssets(FCEConvertBuilder& InBuilder, AActor* InActor);

    /**
     * Checks if this exporter supports the given object.
     * 
     * @param Object - The object to check support for
     * @return True if this exporter supports the object, false otherwise
     */
    bool SupportsObject(const UObject* Object) const;

    /**
     * The actor class that this exporter supports.
     */
    UPROPERTY()
    TSubclassOf<AActor> SupportedClass;

protected:
    /**
     * Recursively processes all components in the scene component hierarchy.
     * 
     * @param InBuilder - The conversion builder to use
     * @param InGameWorld - The game world to add the game objects to
     * @param InParentGameObject - The parent game object
     * @param InRootComponent - The root component to start traversal from
     */
    void TraverseSceneComponent(FCEConvertBuilder& InBuilder,
                                ::cesdk::cegf::SDKGameWorld InGameWorld,
                                ::cesdk::cegf::SDKGameObject InParentGameObject,
                                USceneComponent* InRootComponent);
};