#pragma once
#include "CoreMinimal.h"

class USceneComponent;
class FCEConvertBuilder;

namespace CEAssetExchange {
struct IEntity;
struct IWorldAssemble;
struct FAssetExchangeInput;
IEntity* AddFoliage(FCEConvertBuilder& Builder, IWorldAssemble* World, USceneComponent* Scene, IEntity* Parent, USceneComponent* Comp, FAssetExchangeInput* Input);
IEntity* AddModel(FCEConvertBuilder& Builder, IWorldAssemble* World, USceneComponent* Scene, IEntity* Parent, USceneComponent* Comp, FAssetExchangeInput* Input);
IEntity* AddLight(FCEConvertBuilder& Builder, IWorldAssemble* World, USceneComponent* Scene, IEntity* Parent, USceneComponent* Comp, FAssetExchangeInput* Input);
IEntity* AddCamera(FCEConvertBuilder& Builder, IWorldAssemble* World, USceneComponent* Scene, IEntity* Parent, USceneComponent* Comp, FAssetExchangeInput* Input);
IEntity* AddSkyAtmosphere(FCEConvertBuilder& Builder, IWorldAssemble* World, USceneComponent* Scene, IEntity* Parent, USceneComponent* Comp, FAssetExchangeInput* Input);
IEntity* AddLandscape(FCEConvertBuilder& Builder, IWorldAssemble* World, USceneComponent* Scene, IEntity* Parent, USceneComponent* Comp, FAssetExchangeInput* Input);
IEntity* AddEntity(IWorldAssemble* World, const USceneComponent* Scene, IEntity* Parent);
}

CEAssetExchange::IEntity* AddFromScene(FCEConvertBuilder& Builder, CEAssetExchange::IWorldAssemble* WorldA, USceneComponent* Node, CEAssetExchange::IEntity* Parent, CEAssetExchange::FAssetExchangeInput* Input);