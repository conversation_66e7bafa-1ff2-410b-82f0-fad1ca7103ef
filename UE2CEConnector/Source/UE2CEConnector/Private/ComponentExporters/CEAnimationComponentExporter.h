#pragma once
#include "CoreMinimal.h"
#include "CEBaseComponentExporter.h"
#include "CEAnimationComponentExporter.generated.h"

class UCharacterMovementComponent;
class USkeletalMeshComponent;
class UCapsuleComponent;
UCLASS()
class UCEAnimationComponentExporter : public UCEBaseComponentExporter
{
    GENERATED_UCLASS_BODY()
public:
    virtual cesdk::cegf::SDKGameObject AddGameObjectFromSceneComponent(
        FCEConvertBuilder& InBuilder,
        cesdk::cegf::SDKGameWorld InGameWorld,
        cesdk::cegf::SDKGameObject InParentGameObject,
        USceneComponent* InExportComponent) override;
private:
    cesdk::cegf::SDKGameObject CreateAnimationObject(
        FCEConvertBuilder& InBuilder,
        cesdk::cegf::SDKGameWorld InGameWorld,
        cesdk::cegf::SDKGameObject InParentGameObject,
        USceneComponent* InExportComponent);

    cesdk::cegf::SDKGameObject CreateCharacterObject(
        FCEConvertBuilder& InBuilder,
        cesdk::cegf::SDKGameWorld InGameWorld,
        cesdk::cegf::SDKGameObject InParentGameObject,
        USceneComponent* InExportComponent);

    void SetSkeletalMeshComponent(FCEConvertBuilder& InBuilder, cesdk::cegf::SDKGameObject GameObject, USkeletalMeshComponent* MeshComp);
    void SetCapsulelComponent(FCEConvertBuilder& InBuilder, cesdk::cegf::SDKGameObject GameObject, UCapsuleComponent* CapsuleComp, USkeletalMeshComponent* MeshComp);
    void SetCharacterMovementComponent(FCEConvertBuilder& InBuilder, cesdk::cegf::SDKGameObject GameObject, UCharacterMovementComponent* MovementComp);
};