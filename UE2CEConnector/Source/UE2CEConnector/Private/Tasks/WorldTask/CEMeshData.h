#pragma once

#include "CoreMinimal.h"
#include "MeshDescription.h"
#include "StaticMeshAttributes.h"
#include "Developer/MeshMergeUtilities/Private/MeshMergeHelpers.h"
#include "Rendering/SkeletalMeshRenderData.h"

struct FCEMeshData
{
    FCEMeshData(const UStaticMesh* StaticMesh, const UStaticMeshComponent* StaticMeshComponent, int32 LODIndex)
        : Parent(nullptr)
    {
        FStaticMeshAttributes(Description).Register();
        if (StaticMeshComponent != nullptr)
        {
            Name = StaticMeshComponent->GetName();
            FMeshMergeHelpers::RetrieveMesh(StaticMeshComponent, LODIndex, Description, true);
        }
        else
        {
            StaticMesh->GetName(Name);
            FMeshMergeHelpers::RetrieveMesh(StaticMesh, LODIndex, Description);
        }

        const int32 NumTexCoords = StaticMesh->GetLODForExport(LODIndex).VertexBuffers.StaticMeshVertexBuffer.GetNumTexCoords();
        TexCoord = FMath::Min(StaticMesh->GetLightMapCoordinateIndex(), NumTexCoords - 1);
    };

    FCEMeshData(const USkeletalMesh* SkeletalMesh, const USkeletalMeshComponent* SkeletalMeshComponent, int32 LODIndex)
    {

    };

    const FCEMeshData* GetParent() const
    {

    };

    FString Name;
    FMeshDescription Description;
    int32 TexCoord;

    // TODO: find a better name for referencing the mesh-only data (no component)
    const FCEMeshData* Parent;
};