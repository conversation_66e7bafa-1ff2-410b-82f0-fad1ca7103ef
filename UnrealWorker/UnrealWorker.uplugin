{"FileVersion": 3, "Version": 106, "VersionName": "1.0.6", "FriendlyName": "UnrealWorker", "Description": "AI agent for UE programming", "Category": "Exporters", "CreatedBy": "", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EngineVersion": "5.0", "CanContainContent": true, "IsBetaVersion": true, "Installed": true, "EnabledByDefault": true, "Modules": [{"Name": "UnrealMCP", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "NodeToCode", "Type": "Editor", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}], "Plugins": [{"Name": "WebBrowserWidget", "Enabled": true}, {"Name": "WebSocketNetworking", "Enabled": true}]}