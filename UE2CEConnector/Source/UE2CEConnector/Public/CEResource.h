#pragma once
#include "CoreMinimal.h"
#include "ImportMeshAssetData_generated.h"
#include "basis_export.h"

/// @file information about NDA format
#define MAKE_DWORD(A, B, C, D)             ((A << 24) | (B << 16) | (C << 8) | D)
#define MAKE_QWORD(A, B, C, D, E, F, G, H) ((A << 56) | (B << 48) | (C << 40) | (D << 32) | (E << 24) | (F << 16) | (G << 8) | H)
constexpr uint32 ASSET_MAGIC_NUMBER = MAKE_DWORD('.', 'n', 'd', 'a');
constexpr uint32 ASSET_MAGIC_NUMBER_JMETA = MAKE_DWORD('j', 'n', 'd', 'a');
// tail magic number only used in editor
constexpr uint32 NDA_TAIL_MAGIC = ('E' | ('N' << 8) | ('D' << 16) | ('!' << 24));
constexpr uint32 NDA_VERSION = 4;
constexpr uint32 NDA_CONTENT_TYPE = 1;
constexpr uint64 GUID_L = 0x0001;
constexpr uint64 GUID_H = 0x0002;

constexpr uint32 MESH_ASSET_DATA_RESOURCE = 23;
constexpr uint32 TEX2D_RESOURCE = 7;
constexpr uint32 TEXCUBE_RESOURCE = 15;

constexpr uint32 VIRTUAL_TEX2D_MIN_BLOCK_SIZE = 128;

struct FCEResource
{
    TUniquePtr<CrossSchema::ImportMeshAssetDataT> MeshAssetDataT;
};
enum TextureType : uint32
{
    ImageTexture = 0,
    RectangularCubeMap,
    NormalTexture,
    DataTexture,
};

enum TextureCompression : uint32
{
    Uncompressed,
    CompressedHQ,
    Compressed,
    CompressedLQ,
    BC7,
    ASTC,
    PVRTC,
    ETC2
};
enum ImportColorSpace : uint32
{
    Linear,
    SRGB,
    Count,
};
struct CETextureImportSetting
{
    TextureType Type = TextureType::ImageTexture;
    ImportColorSpace ColorSpace = ImportColorSpace::Linear;
    TextureCompression Compression = TextureCompression::CompressedLQ;
    bool GenerateMipmap = true;
    bool OpenGLESCompatible = true;
    bool IsStreamFile = false;
};
