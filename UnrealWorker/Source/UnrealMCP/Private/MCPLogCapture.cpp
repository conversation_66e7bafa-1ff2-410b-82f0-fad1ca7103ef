#include "MCPLogCapture.h"
#include "Misc/DateTime.h"
#include "Misc/Paths.h"
#include "Misc/Guid.h"
#include "Engine/Engine.h"
#include "HAL/PlatformProcess.h"
#include "MCPFileLogger.h"

FMCPLogCapture::FMCPLogCapture()
    : bIsCapturing(false)
{
}

FMCPLogCapture::~FMCPLogCapture()
{
    if (bIsCapturing)
    {
        StopCapture();
    }
}

void FMCPLogCapture::StartCapture()
{
    FScopeLock Lock(&LogMutex);
    
    if (bIsCapturing)
    {
        return; // Already capturing
    }
    
    // Clear previous logs
    CapturedLogs.Empty();
    
    // Add this output device to the global log
    GLog->AddOutputDevice(this);
    bIsCapturing = true;
    
    // Add a header to captured logs
    FString Header = FString::Printf(TEXT("=== UE Log Capture Started at %s ===\n"), *FDateTime::Now().ToString());
    CapturedLogs += Header;
    
    MCP_LOG_INFO("Started UE log capture");
}

FString FMCPLogCapture::StopCapture()
{
    FScopeLock Lock(&LogMutex);
    
    if (!bIsCapturing)
    {
        return FString();
    }
    
    // Remove this output device from global log
    GLog->RemoveOutputDevice(this);
    bIsCapturing = false;
    
    // Add footer
    FString Footer = FString::Printf(TEXT("=== UE Log Capture Ended at %s ===\n"), *FDateTime::Now().ToString());
    CapturedLogs += Footer;
    
    MCP_LOG_INFO("Stopped UE log capture, captured %d characters", CapturedLogs.Len());
    
    // Return the captured content
    FString Result = CapturedLogs;
    CapturedLogs.Empty();
    return Result;
}

void FMCPLogCapture::Serialize(const TCHAR* V, ELogVerbosity::Type Verbosity, const FName& Category)
{
    FScopeLock Lock(&LogMutex);
    
    if (!bIsCapturing)
    {
        return;
    }
    
    // Format the log entry and add to buffer
    FString FormattedEntry = FormatLogEntry(V, Verbosity, Category);
    CapturedLogs += FormattedEntry;
    
    // Also log to MCP logger for debugging
    if (Category != TEXT("LogMCP"))  // Avoid infinite recursion
    {
        MCP_LOG_VERBOSE("Captured log: [%s][%s] %s", *Category.ToString(), *VerbosityToString(Verbosity), V ? V : TEXT(""));
    }
}

FString FMCPLogCapture::FormatLogEntry(const TCHAR* Message, ELogVerbosity::Type Verbosity, const FName& Category)
{
    FString TimeStamp = FDateTime::Now().ToString(TEXT("%Y-%m-%d %H:%M:%S"));
    FString VerbosityStr = VerbosityToString(Verbosity);
    FString CategoryStr = Category.ToString();
    
    // Format: [Timestamp][Category][Verbosity] Message
    return FString::Printf(TEXT("[%s][%s][%s] %s\n"), 
        *TimeStamp, 
        *CategoryStr, 
        *VerbosityStr, 
        Message ? Message : TEXT(""));
}

FString FMCPLogCapture::VerbosityToString(ELogVerbosity::Type Verbosity)
{
    switch (Verbosity)
    {
        case ELogVerbosity::Fatal:      return TEXT("Fatal");
        case ELogVerbosity::Error:      return TEXT("Error");
        case ELogVerbosity::Warning:    return TEXT("Warning");
        case ELogVerbosity::Display:    return TEXT("Display");
        case ELogVerbosity::Log:        return TEXT("Log");
        case ELogVerbosity::Verbose:    return TEXT("Verbose");
        case ELogVerbosity::VeryVerbose: return TEXT("VeryVerbose");
        default:                        return TEXT("Unknown");
    }
}
