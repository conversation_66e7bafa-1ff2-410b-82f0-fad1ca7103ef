#pragma once

inline TSharedRef<FJsonValue> ConvertToJsonValue(const bool& in)
{
    return MakeShareable(new FJsonValueBoolean(in));
}

inline TSharedRef<FJsonValue> ConvertToJsonValue(const FString& in)
{
    return MakeShareable(new FJsonValueString(in));
}

inline TSharedRef<FJsonValue> ConvertToJsonValue(const TSharedRef<FJsonObject>& JsonObject)
{
    return MakeShareable(new FJsonValueObject(JsonObject));
}

template<typename T>
    requires std::is_integral_v<T> || std::is_floating_point_v<T>
inline TSharedRef<FJsonValue> ConvertToJsonValue(const T& in)
{
    return MakeShareable(new FJsonValueNumber(in));
}

template<typename T>
inline TSharedRef<FJsonValue> ConvertToJsonValue(const TArray<T>& inArr)
{
    TArray<TSharedPtr<FJsonValue>> arr;
    for (const auto& v : inArr)
        arr.Push(ConvertToJsonValue(v));
    return MakeShareable(new FJsonValueArray(arr));
}

template<typename T>
inline TSharedRef<FJsonValue> ConvertToJsonValue(const TMap<FString, T>& inMap)
{
    TSharedRef<FJsonObject> JsonObject = MakeShared<FJsonObject>();
    for (const auto& [k, v] : inMap)
    {
        JsonObject->SetField(k, ConvertToJsonValue(v));
    }
    return MakeShareable(new FJsonValueObject(JsonObject));
}

template<typename T>
concept TJsonObject = requires(T t) { t.ToJsonObject(); };
template<typename TJsonObject>
inline TSharedRef<FJsonValue> ConvertToJsonValue(const TJsonObject& in)
{
    return MakeShareable(new FJsonValueObject(in.ToJsonObject()));
}

template<typename TJsonObject>
inline TSharedRef<FJsonValue> ConvertToJsonValue(const TSharedPtr<TJsonObject>& in)
{
    return MakeShareable(new FJsonValueObject(in->ToJsonObject()));
}

#define CONVERT_TO_JSON(JsonObject, Keyworld)           JsonObject->SetField(#Keyworld, ConvertToJsonValue(Keyworld));


struct FCEAnimNotify
{
    FString Name;
    float TriggerTimeInSec;
    float EndTriggerTimeInSec;
    float TriggerWeightThreshold;
    uint32 NotifyLODThreshold = 0;
    FString Type = "AnimNotifyScript";

    TSharedRef<FJsonObject> ToJsonObject() const
    {
        TSharedRef<FJsonObject> JsonObject = MakeShared<FJsonObject>();
        CONVERT_TO_JSON(JsonObject, Name);
        CONVERT_TO_JSON(JsonObject, TriggerTimeInSec);
        CONVERT_TO_JSON(JsonObject, EndTriggerTimeInSec);
        CONVERT_TO_JSON(JsonObject, TriggerWeightThreshold);
        CONVERT_TO_JSON(JsonObject, NotifyLODThreshold);
        CONVERT_TO_JSON(JsonObject, Type);
        return JsonObject;
    }
};

struct FCEAnimSlotTrackSegment
{
    FString Name;
    FString ReltvPath;
    float StartPos;
    float EndPos;
    float PlayRate;
    uint32 LoopingCount;

    TSharedRef<FJsonObject> ToJsonObject() const
    {
        TSharedRef<FJsonObject> JsonObject = MakeShared<FJsonObject>();
        CONVERT_TO_JSON(JsonObject, Name);
        CONVERT_TO_JSON(JsonObject, ReltvPath);
        CONVERT_TO_JSON(JsonObject, StartPos);
        CONVERT_TO_JSON(JsonObject, EndPos);
        CONVERT_TO_JSON(JsonObject, PlayRate);
        CONVERT_TO_JSON(JsonObject, LoopingCount);
        return JsonObject;
    }
};


struct FCEAnimSlotTrackSection
{
    FString Name;
    int32 SegmentStartIndex;
    int32 SegmentEndIndex;
    bool Loop;

    TSharedRef<FJsonObject> ToJsonObject() const
    {
        TSharedRef<FJsonObject> JsonObject = MakeShared<FJsonObject>();
        CONVERT_TO_JSON(JsonObject, Name);
        CONVERT_TO_JSON(JsonObject, SegmentStartIndex);
        CONVERT_TO_JSON(JsonObject, SegmentEndIndex);
        CONVERT_TO_JSON(JsonObject, Loop);
        return JsonObject;
    }
};


struct FCEAnimSlotTrack
{
public:
    FString SlotName;
    TArray<FCEAnimSlotTrackSegment> Segments;
    TArray<FCEAnimSlotTrackSection> Sections;

    TSharedRef<FJsonObject> ToJsonObject() const
    {
        TSharedRef<FJsonObject> JsonObject = MakeShared<FJsonObject>();
        CONVERT_TO_JSON(JsonObject, SlotName);
        CONVERT_TO_JSON(JsonObject, Segments);
        CONVERT_TO_JSON(JsonObject, Sections);
        return JsonObject;
    }
};

struct FCEAnimNotifyTrack {
public:
    FString Name;
    TArray<FCEAnimNotify> Notifies;
    TSharedRef<FJsonObject> ToJsonObject() const
    {
        TSharedRef<FJsonObject> JsonObject = MakeShared<FJsonObject>();
        CONVERT_TO_JSON(JsonObject, Name);
        CONVERT_TO_JSON(JsonObject, Notifies);
        return JsonObject;
    }
};


struct FCEAnimComposite
{
    FString SyncGroupName;
    FString GroupName;
    FCEAnimSlotTrack AnimTrack;
    TArray<FCEAnimNotifyTrack> NotifyTracks;

    TSharedRef<FJsonObject> ToJsonObject() const
    {
        TSharedRef<FJsonObject> JsonObject = MakeShared<FJsonObject>();
        CONVERT_TO_JSON(JsonObject, SyncGroupName);
        CONVERT_TO_JSON(JsonObject, GroupName);
        CONVERT_TO_JSON(JsonObject, AnimTrack);
        CONVERT_TO_JSON(JsonObject, NotifyTracks);
        return JsonObject;
    }
};

struct FCEAnimMontage
{
    FString SyncGroupName;
    FString GroupName;
    TArray<FCEAnimSlotTrack> AnimTracks;
    TArray<FCEAnimNotifyTrack> NotifyTracks;

    TSharedRef<FJsonObject> ToJsonObject() const
    {
        TSharedRef<FJsonObject> JsonObject = MakeShared<FJsonObject>();
        CONVERT_TO_JSON(JsonObject, SyncGroupName);
        CONVERT_TO_JSON(JsonObject, GroupName);
        CONVERT_TO_JSON(JsonObject, AnimTracks);
        CONVERT_TO_JSON(JsonObject, NotifyTracks);
        return JsonObject;
    }
};


// ======= Blend Space =======
struct FCEBlendSpaceAxis
{
    FString Axis;
    FString AxisName;
    float Min = 0.0f;
    float Max = 100.0f;
    int32 GridNum = 1;
    TSharedRef<FJsonObject> ToJsonObject() const
    {
        TSharedRef<FJsonObject> JsonObject = MakeShared<FJsonObject>();
        CONVERT_TO_JSON(JsonObject, Axis);
        CONVERT_TO_JSON(JsonObject, AxisName);
        CONVERT_TO_JSON(JsonObject, Min);
        CONVERT_TO_JSON(JsonObject, Max);
        CONVERT_TO_JSON(JsonObject, GridNum);
        return JsonObject;
    }
};

struct FCEBlendSpacePatternedPoint
{
    FString Axis = "X";
    FString Name;
    FString Path;
    float Pos = 1.0f;
    TSharedRef<FJsonObject> ToJsonObject() const
    {
        TSharedRef<FJsonObject> JsonObject = MakeShared<FJsonObject>();
        CONVERT_TO_JSON(JsonObject, Axis);
        CONVERT_TO_JSON(JsonObject, Name);
        CONVERT_TO_JSON(JsonObject, Path);
        CONVERT_TO_JSON(JsonObject, Pos);
        return JsonObject;
    }
};

struct FCEBlendSpacePatternedLine
{
    FString Axis = "Y";
    float Pos = 0.0f;
    TArray<FCEBlendSpacePatternedPoint> Points;
    TSharedRef<FJsonObject> ToJsonObject() const
    {
        TSharedRef<FJsonObject> JsonObject = MakeShared<FJsonObject>();
        CONVERT_TO_JSON(JsonObject, Axis);
        CONVERT_TO_JSON(JsonObject, Pos);
        CONVERT_TO_JSON(JsonObject, Points);
        return JsonObject;
    }
};

struct FCEBlendSpace
{
    TArray<FCEBlendSpacePatternedLine> GridPattern;
    TArray<FCEBlendSpaceAxis> AxisData;
    TSharedRef<FJsonObject> ToJsonObject() const
    {
        TSharedRef<FJsonObject> JsonObject = MakeShared<FJsonObject>();
        CONVERT_TO_JSON(JsonObject, GridPattern);
        CONVERT_TO_JSON(JsonObject, AxisData);
        return JsonObject;
    }
};

