#include "Converters/CEBaseConverter.h"

UCEBaseConverter::UCEBaseConverter(const FObjectInitializer& ObjectInitializer): Super(ObjectInitializer)
{
    SupportedClass = NULL;   
    ConvertNum = 0;
}

FString UCEBaseConverter::GetCEContentPath(const UObject* Object)
{
    if (!Object)
    {
        return FString();
    }

    // Get the full path of the object
    FString ObjectPath = FPaths::GetPath(Object->GetPathName());
    
    FString ContentPath;
    
    // For Game content, replace only the /Game/ prefix
    if (ObjectPath.StartsWith(TEXT("/Game/")))
    {
        // Extract the part after /Game/ and prepend Contents/
        ContentPath = FPaths::Combine(TEXT("Contents"),ObjectPath.Mid(6));
    }
    // For Engine content, replace only the /Engine/ prefix
    else
    {
        // Extract the part after /Engine/ and prepend Contents/Engine/
        ContentPath = FPaths::Combine(TEXT("Contents"), ObjectPath);
    }

    ContentPath = FPaths::Combine(ContentPath, Object->GetName()) + GetCEFileExtension(Object);
    return ContentPath;
}

