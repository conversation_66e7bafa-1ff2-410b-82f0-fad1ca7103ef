#pragma once
#include "CEBaseActorExporter.h"
#include "CEInstancedFoliageActorExporter.generated.h"

UCLASS()
class UCEInstancedFoliageActorExporter : public UCEBaseActorExporter
{
public:
    GENERATED_UCLASS_BODY()
    virtual AddEntityFromActorRetType AddEntityFromActor(FCEConvertBuilder& InBuilder,
                                                         CEAssetExchange::IWorldAssemble* InWorldAssemble,
                                                         USceneComponent* InSceneComponent,
                                                         CEAssetExchange::IEntity* LevelEntity,
                                                         AActor* InExportActor,
                                                         CEAssetExchange::FAssetExchangeInput* InInput) override;
};