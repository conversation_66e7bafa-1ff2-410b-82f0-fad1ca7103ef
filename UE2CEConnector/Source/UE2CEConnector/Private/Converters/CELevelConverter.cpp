#include "Converters/CELevelConverter.h"

#include "Builders/CEConvertBuilder.h"
#include "Engine/EngineTypes.h"
#include "Tasks/WorldTask/CEWorldTask.h"

UCELevelConverter::UCELevelConverter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UWorld::StaticClass();
}

void UCELevelConverter::Convert(FCEConvertBuilder* Builder, const FString& savePath, UObject* Object)
{
    UWorld* Level = Cast<UWorld>(Object);
    if (Level)
        Builder->SetupTask<FCEWorldTask>(savePath, *Builder, Level);
}
