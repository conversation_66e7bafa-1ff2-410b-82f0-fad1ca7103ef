#include "CEInstancedFoliageActorExporter.h"

#include "InstancedFoliageActor.h"
#include "Builders/CEConvertBuilder.h"
#include "Exchange/ExportUtils.h"

UCEInstancedFoliageActorExporter::UCEInstancedFoliageActorExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = AInstancedFoliageActor::StaticClass();
}

UCEInstancedFoliageActorExporter::AddEntityFromActorRetType UCEInstancedFoliageActorExporter::AddEntityFromActor(
    FCEConvertBuilder& InBuilder,
    CEAssetExchange::IWorldAssemble* InWorldAssemble,
    USceneComponent* InSceneComponent,
    CEAssetExchange::IEntity* LevelEntity,
    AActor* InExportActor,
    CEAssetExchange::FAssetExchangeInput* InInput)
{
    TInlineComponentArray<UHierarchicalInstancedStaticMeshComponent*> HISMComponents(InExportActor);
    CEAssetExchange::IEntity* Entity = CEAssetExchange::FExportUtils::AddEntity(InWorldAssemble, InSceneComponent, LevelEntity);

    for (auto Component : HISMComponents)
    {
        if (auto MeshComponent = Cast<UHierarchicalInstancedStaticMeshComponent>(Component))
        {
            InBuilder.AddEntityFromSceneComponent(InWorldAssemble, InSceneComponent, Entity, MeshComponent, InInput);
        }
        else
        {
            CEAssetExchange::FExportUtils::AddEntity(InWorldAssemble, InSceneComponent, Entity);
        }
    }
    return {Entity, false};
}


