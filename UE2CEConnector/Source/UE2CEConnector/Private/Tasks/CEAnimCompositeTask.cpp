#include "CEAnimCompositeTask.h"

#include "CELog.h"
#include "Animation/AnimComposite.h"
#include "Builders/CEConvertBuilder.h"
#include "Converters/CEAnimationCommon.h"


FCEAnimCompositeTask::FCEAnimCompositeTask(const FString& savePath, FCEConvertBuilder& builder, const UAnimComposite* animComposite)
    : FCETask(ECETaskPriority::Animation, savePath, builder, animComposite)
      , AnimComposite(animComposite) {}

FString FCEAnimCompositeTask::GetName() const
{
    return FString::Printf(TEXT("Exporting Animation Composite: %s"), *AnimComposite->GetName());
}

bool FCEAnimCompositeTask::CollectDependencyTask()
{
    if (!AnimComposite)
    {
        return false;
    }

    // Get AnimSeq
    for (auto& Segment : AnimComposite->AnimationTrack.AnimSegments)
    {
        mDependencies.Add(Segment.AnimReference);
        mBuilder.GetOrAdd(Segment.AnimReference.Get());
    }
    return true;
}

bool FCEAnimCompositeTask::Complete()
{
    if (!AnimComposite)
    {
        CE_Log(TEXT("Invalid Animation Composite for export"));
        return false;
    }

    // Get the reference skeleton from the animation
    USkeleton* Skeleton = AnimComposite->GetSkeleton();
    if (!Skeleton)
    {
        CE_Log(TEXT("Animation Sequence %s has no valid skeleton"), *AnimComposite->GetName());
        return false;
    }

    FCEAnimComposite CEAnimComposite;

    // Get Slot AnimTrack
    for (auto& Segment : AnimComposite->AnimationTrack.AnimSegments)
    {
        FCEAnimSlotTrackSegment CESegement;
        CESegement.ReltvPath = mBuilder.GetCEContentPath(Segment.AnimReference.Get());
        CESegement.StartPos = Segment.AnimStartTime;
        CESegement.EndPos = Segment.AnimEndTime;
        CESegement.PlayRate = Segment.AnimPlayRate;
        CESegement.LoopingCount = Segment.LoopingCount;
        CEAnimComposite.AnimTrack.Segments.Push(CESegement);
    }

    // Get Anim Notify
    for (auto& NotifyTrack : AnimComposite->AnimNotifyTracks)
    {
        FCEAnimNotifyTrack CENotifyTrack;
        CENotifyTrack.Name = NotifyTrack.TrackName.ToString();
        for (auto* Notify : NotifyTrack.Notifies)
        {
            FCEAnimNotify CENotify;
            CENotify.Name = Notify->NotifyName.ToString();
            CENotify.TriggerTimeInSec = Notify->GetTriggerTime();
            CENotify.EndTriggerTimeInSec = Notify->GetEndTriggerTime();
            CENotify.TriggerWeightThreshold = Notify->TriggerWeightThreshold;
            CENotifyTrack.Notifies.Push(std::move(CENotify));
        }
        CEAnimComposite.NotifyTracks.Push(std::move(CENotifyTrack));
    }

    TSharedRef<FJsonObject> JsonObject = CEAnimComposite.ToJsonObject();
    FString JsonCEAnimComposite;
    auto JsonWriter = TJsonWriterFactory<TCHAR, TPrettyJsonPrintPolicy<TCHAR>>::Create(&JsonCEAnimComposite, 0);
    bool bSuccess = FJsonSerializer::Serialize(JsonObject, JsonWriter);
    JsonWriter->Close();

    FString ExportPath = FPaths::Combine(mBuilder.mRootPath, mBuilder.GetCEContentPath(AnimComposite));

    //FFileHelper::SaveStringToFile(JsonCEAnimComposite, *(ExportPath.Replace(TEXT(".nda"), TEXT(".composite"))));

    auto res = cesdk::resource::SDKAnimCompositeResource(TCHAR_TO_UTF8(*ExportPath));
    res.Init(TCHAR_TO_UTF8(*JsonCEAnimComposite));
    res.Serialize();

    return true;
}