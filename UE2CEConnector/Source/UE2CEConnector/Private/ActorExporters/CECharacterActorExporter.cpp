#include "CECharacterActorExporter.h"

#include "GameFramework/Character.h"


UCECharacterActorExporter::UCECharacterActorExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = ACharacter::StaticClass();
}

cesdk::cegf::SDKGameObject UCECharacterActorExporter::AddGameObjectFromActor(FCEConvertBuilder& InBuilder, ::cesdk::cegf::SDKGameWorld InGameWorld, ::cesdk::cegf::SDKGameObject InParentGameObject, AActor* InExportActor)
{
    return UCEBaseActorExporter::AddGameObjectFromActor(InBuilder, InGameWorld, InParentGameObject, InExportActor);
}