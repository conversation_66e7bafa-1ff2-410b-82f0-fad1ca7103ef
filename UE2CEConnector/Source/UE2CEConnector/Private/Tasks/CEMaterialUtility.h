#pragma once
#include "CETask.h"
#include "Builders/CEConvertBuilder.h"
class SProperties
{
public:
    FString _BaseMap = "EngineResource/Texture/DefaultTexture.nda";
    float _BaseColor[4] = { 1, 1, 1, 1 };

    bool ALPHA_CLIPPING = false;
    float _AlphaClip = { 0 };

    bool DOUBLE_SIDED = false;
    float _DoubleSidedConstants[4] = { 1, 1, -1, 0 };

    bool _NORMALMAP_TANGENT_SPACE = true;
    FString _NormalMap = "EngineResource/Texture/DefaultTexture.nda";
    std::string _NormalMapOS = "EngineResource/Texture/DefaultTexture.nda";
    float _NormalScale = { 1.f };

    FString _MaskMap = "EngineResource/Texture/DefaultTexture.nda";
    float _Metallic = { 1.f };
    float _AORemapMin = { 0.f };
    float _AORemapMax = { 0.f };
    float _Smoothness = { 1.f };
    float _SmoothnessRemapMin = { 0.f };
    float _SmoothnessRemapMax = { 1.f };

    FString _ThicknessMap = "EngineResource/Texture/DefaultTexture.nda";
    float _SubsurfaceMask = { 1.f };
    float _Thickness = { 1.f };

    /// <summary>
    /// For Default Standard material
    /// </summary>
    FString color_texture = "EngineResource/Texture/DefaultTexture.nda";
    FString normal_texture = "EngineResource/Texture/DefaultTexture.nda";
    FString rm_texture = "EngineResource/Texture/DefaultTexture.nda";
    bool NORMAL_MAP = true;
    int MATERIAL_TYPE = { 0 };
};

enum class ECEShadingModel
{
    None = -1,
    Default,
    Unlit,
    ClearCoat
};

struct FCEPropertyBakeOutput
{
    FORCEINLINE FCEPropertyBakeOutput(const EMaterialProperty& Property, EPixelFormat PixelFormat, TArray<FColor>& Pixels, FIntPoint Size, float EmissiveScale)
        : Property(Property), PixelFormat(PixelFormat), Pixels(Pixels), Size(Size), EmissiveScale(EmissiveScale), bIsConstant(false)
    {}

    FORCEINLINE FCEPropertyBakeOutput(const EMaterialProperty& Property, FIntPoint Size, float EmissiveScale)
        : Property(Property), Size(Size), EmissiveScale(EmissiveScale), bIsConstant(false)
    {}

    const EMaterialProperty& Property;
    EPixelFormat PixelFormat;
    TArray<FColor> Pixels;
    FIntPoint Size;
    float EmissiveScale;

    bool bIsConstant;
    FLinearColor ConstantValue;
};

struct FCEMaterialUtility
{
    template<class InputType>
    static const FMaterialInput<InputType>* GetInputForProperty(const UMaterialInterface* Material, const EMaterialProperty& Property)
    {
        UMaterial* UnderlyingMaterial = const_cast<UMaterial*>(Material->GetMaterial());
        const FExpressionInput* ExpressionInput = UnderlyingMaterial->GetExpressionInputForProperty(Property);
        return static_cast<const FMaterialInput<InputType>*>(ExpressionInput);
    }

    static FVector4 GetPropertyDefaultValue(const EMaterialProperty& Property)
    {
        // TODO: replace with GMaterialPropertyAttributesMap lookup (when public API available)

        switch (Property)
        {
        case EMaterialProperty::MP_EmissiveColor:          return FVector4(0, 0, 0, 0);
        case EMaterialProperty::MP_Opacity:                return FVector4(1, 0, 0, 0);
        case EMaterialProperty::MP_OpacityMask:            return FVector4(1, 0, 0, 0);
        case EMaterialProperty::MP_BaseColor:              return FVector4(0, 0, 0, 0);
        case EMaterialProperty::MP_Metallic:               return FVector4(0, 0, 0, 0);
        case EMaterialProperty::MP_Specular:               return FVector4(.5, 0, 0, 0);
        case EMaterialProperty::MP_Roughness:              return FVector4(.5, 0, 0, 0);
        case EMaterialProperty::MP_Anisotropy:             return FVector4(0, 0, 0, 0);
        case EMaterialProperty::MP_Normal:                 return FVector4(0, 0, 1, 0);
        case EMaterialProperty::MP_Tangent:                return FVector4(1, 0, 0, 0);
        case EMaterialProperty::MP_WorldPositionOffset:    return FVector4(0, 0, 0, 0);
        case EMaterialProperty::MP_WorldDisplacement_DEPRECATED:	return FVector4(0, 0, 0, 0);
        case EMaterialProperty::MP_TessellationMultiplier_DEPRECATED:	return FVector4(1, 0, 0, 0);
        case EMaterialProperty::MP_SubsurfaceColor:        return FVector4(1, 1, 1, 0);
        case EMaterialProperty::MP_CustomData0:            return FVector4(1, 0, 0, 0);
        case EMaterialProperty::MP_CustomData1:            return FVector4(.1, 0, 0, 0);
        case EMaterialProperty::MP_AmbientOcclusion:       return FVector4(1, 0, 0, 0);
        case EMaterialProperty::MP_Refraction:             return FVector4(1, 0, 0, 0);
        case EMaterialProperty::MP_PixelDepthOffset:       return FVector4(0, 0, 0, 0);
        case EMaterialProperty::MP_ShadingModel:           return FVector4(0, 0, 0, 0);
        default:                        break;
        }

        if (Property >= MP_CustomizedUVs0 && Property <= MP_CustomizedUVs7)
        {
            return FVector4(0, 0, 0, 0);
        }

        /*if (Property == TEXT("ClearCoatBottomNormal"))
        {
            return FVector4(0, 0, 1, 0);
        }*/
        check(false);
        return FVector4();
    }

    static uint32 GetMaskComponentCount(const FExpressionInput& ExpressionInput)
    {
        return ExpressionInput.MaskR + ExpressionInput.MaskG + ExpressionInput.MaskB + ExpressionInput.MaskA;
    }

    static FLinearColor GetMask(const FExpressionInput& ExpressionInput)
    {
        return FLinearColor(ExpressionInput.MaskR, ExpressionInput.MaskG, ExpressionInput.MaskB, ExpressionInput.MaskA);
    }
};

