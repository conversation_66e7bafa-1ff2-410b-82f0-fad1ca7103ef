#include "CECameraComponentExporter.h"
#include "Camera/CameraComponent.h"
#include "Exchange/ExportWorld.h"
#include "UE2CEConnectorUtils/UE2CEConnectorUtils.h"
#include "Exchange/StringExchange.h"
#include "CineCameraComponent.h"
#include "AssetExchange/Include/SDKComponent/SDKCameraComponent.h"

UCECameraComponentExporter::UCECameraComponentExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UCameraComponent::StaticClass();
}

CEAssetExchange::IEntity* UCECameraComponentExporter::AddEntityFromSceneComponent(
    FCEConvertBuilder& InBuilder,
    CEAssetExchange::IWorldAssemble* InWorldAssemble,
    USceneComponent* InSceneComponent,
    CEAssetExchange::IEntity* InParentEntity,
    USceneComponent* InExportComponent,
    CEAssetExchange::FAssetExchangeInput* InInput)
{
    return CEAssetExchange::AddCamera(InBuilder, InWorldAssemble, InSceneComponent, InParentEntity, InExportComponent, InInput);
}

cesdk::cegf::SDKGameObject UCECameraComponentExporter::AddGameObjectFromSceneComponent(
    FCEConvertBuilder& InBuilder,
    ::cesdk::cegf::SDKGameWorld InGameWorld,
    ::cesdk::cegf::SDKGameObject InParentGameObject,
    USceneComponent* InExportComponent)
{
    auto ComputeVFOVInRad = [](float HFOVInDeg, float AspectRatio) -> float
    {
        float HFOVInRad = HFOVInDeg / 180 * PI;
        float VFOVInRad = 2 * FGenericPlatformMath::Atan(FGenericPlatformMath::Tan(HFOVInRad / 2) / AspectRatio);
        return VFOVInRad;
    };

    if (!InExportComponent)
        return InParentGameObject;
    
    UCameraComponent* CameraC = Cast<UCameraComponent>(InExportComponent);
    cesdk::cegf::SDKGameObject CameraGameOBject = Super::AddGameObjectFromSceneComponent(InBuilder, InGameWorld, InParentGameObject, InExportComponent); 

    // Transform X to Z by rotate at Y axis
    cesdk::cross::TRSQuaternionType rotate = CameraGameOBject.GetLocalRotation();
    FQuat R{rotate.x, rotate.y, rotate.z, rotate.w};
    R *= FQuat{FVector::YAxisVector, PI / 2};
    rotate = {R.X, R.Y,R.Z, R.W};
    CameraGameOBject.SetLocalRotation(rotate);
    
    cesdk::cegf::SDKGameObjectComponent SDKGameObjectComponent = CameraGameOBject.AddComponent("cegf::CameraComponent");
    cesdk::cegf::SDKCameraComponent CameraComponent(SDKGameObjectComponent.componentInstance);
    FMinimalViewInfo ViewInfo;
    CameraC->GetCameraView(0, ViewInfo);
    CameraComponent.SetAspectRatio(ViewInfo.AspectRatio);
    CameraComponent.SetFOV(ComputeVFOVInRad(ViewInfo.FOV, ViewInfo.AspectRatio));
    CameraComponent.SetPerspectiveNear(GNearClippingPlane);
    CameraComponent.SetPerspectiveFar(1000000.0f);
    CameraComponent.SetOrthogonal(ViewInfo.OrthoWidth, ViewInfo.OrthoWidth / ViewInfo.AspectRatio, ViewInfo.OrthoNearClipPlane, ViewInfo.OrthoFarClipPlane);
    if (const UCineCameraComponent* CineCameraComponent = Cast<UCineCameraComponent>(CameraC))
    {
        CameraComponent.SetFocalLength(CineCameraComponent->CurrentFocalLength);
        CameraComponent.SetSensorWidth(CineCameraComponent->Filmback.SensorWidth);
        CameraComponent.SetSensorHeight(CineCameraComponent->Filmback.SensorHeight);
        CameraComponent.SetMinFocalLength(CineCameraComponent->LensSettings.MinFocalLength);
        CameraComponent.SetMaxFocalLength(CineCameraComponent->LensSettings.MaxFocalLength);
    }
    CameraComponent.SetProjectionMode(ViewInfo.ProjectionMode == ECameraProjectionMode::Perspective ? cesdk::cegf::CameraProjectionMode::Perspective : cesdk::cegf::CameraProjectionMode::Orthogonal);
    return CameraGameOBject;
}