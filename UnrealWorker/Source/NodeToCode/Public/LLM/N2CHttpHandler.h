// Copyright (c) 2025 <PERSON> (Protospatial). All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "N2CHttpHandlerBase.h"
#include "N2CHttpHandler.generated.h"

/**
 * @class UN2CHttpHandler
 * @brief Concrete implementation of HTTP handler for LLM requests
 */
UCLASS()
class NODETOCODE_API UN2CHttpHandler : public UN2CHttpHandlerBase
{
    GENERATED_BODY()

    // Uses base class implementation
};
