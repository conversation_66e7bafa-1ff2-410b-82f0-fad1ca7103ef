#include "Exporters/CEExportOptions.h"
#include "UObject/Object.h"
#include "UObject/UObjectGlobals.h"

UCEExportOptions::UCEExportOptions(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    ResetToDefault();
}

void UCEExportOptions::ResetToDefault()
{
    bUseAssetExchangeSdkBalanceMode = true;
    bOverwriteExistingTexture = true;
    bOverwriteExistingMaterial = true;
    bOverwriteExistingMesh = true;
    bOverwriteExistingLevel = true;
    MotherMaterialContentPath = "Contents/MotherMaterial";
    SM_DefaultMotherMeterial = "PipelineResource/FFSRP/Shader/Material/Lit/LitUE.fx.nda";
    HISM_DefaultMotherMeterial = "PipelineResource/FFSRP/Shader/Material/Lit/LitVegetation.fx.nda";
    LandscapeMesh_Meterial = "PipelineResource/FFSRP/Shader/Material/Lit/LitUE.fx.nda";
    bExportLevelAsCEPrefab = true;
    bExportNaniteProxy = true;
    bGenerateNaniteLODsForDGWOnly = false;
    NaniteEnabledEnum = ENaniteEnabled::Keep;
    FallbackPercentTriangles = 0.5;
    FallbackMaxTriangles = 1500000;
    IgnoreSmallMeshTriangleSize = 500000;
    DisableNaniteIfTriangleLessThan = 300000;
    bExportTextureSource = true;    
    bExportUDIMTextureSource = false;
    FlipGreenChannelEnum = ETextureFlipGreenChannel::Keep;
    bExportAllUsedTextures = true;
    bExportCustomNodeShaderCode = false;
    MaxTerrainGridSize = 4;
    bSplitLODFiles = false;
    bExportAsFoliageComponent = true;
    bUseDefaultMaterialTestOnly = false;
    MinClusterSize = 10;
    MaxClusterSize = 20;
    BakedTextureSize = 2048;
    bForceUseDefaultMaterial = false;
}