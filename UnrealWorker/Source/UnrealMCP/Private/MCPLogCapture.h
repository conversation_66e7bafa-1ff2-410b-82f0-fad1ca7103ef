#pragma once

#include "CoreMinimal.h"
#include "Misc/OutputDevice.h"
#include "HAL/CriticalSection.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformFilemanager.h"

/**
 * Custom log output device for capturing UE logs during Python execution
 */
class FMCPLogCapture : public FOutputDevice
{
public:
    FMCPLogCapture();
    virtual ~FMCPLogCapture();

    /**
     * Start capturing logs
     */
    void StartCapture();

    /**
     * Stop capturing logs and return the captured content
     * @return The captured log content as a string
     */
    FString StopCapture();

    /**
     * Override from FOutputDevice - called when a log message is generated
     */
    virtual void Serialize(const TCHAR* V, ELogVerbosity::Type Verbosity, const FName& Category) override;

private:
    /** Critical section for thread safety */
    FCriticalSection LogMutex;
    
    /** Whether we are currently capturing logs */
    bool bIsCapturing;
    
    /** Buffer to store captured logs */
    FString CapturedLogs;
    
    /**
     * Format a log entry with timestamp, category, verbosity, and message
     */
    FString FormatLogEntry(const TCHAR* Message, ELogVerbosity::Type Verbosity, const FName& Category);
    
    /**
     * Convert verbosity enum to string
     */
    FString VerbosityToString(ELogVerbosity::Type Verbosity);
};
