#pragma once
#include <string>
#include <array>
#include <limits>
#include <assert.h>

#define CEMeta(...)

using SInt8 = std::int8_t;
using UInt8 = std::uint8_t;
using SInt16 = std::int16_t;
using UInt16 = std::uint16_t;
using SInt32 = std::int32_t;
using UInt32 = std::uint32_t;
using SInt64 = std::int64_t;
using UInt64 = std::uint64_t;
//using SizeType = std::size_t;
//using SSizeType = SInt64;

template<typename SizeT = size_t, typename T, SizeT N>
constexpr SizeT ArrayCount(T (&)[N]) noexcept
{
    return N;
}

template<typename E, typename = std::enable_if_t<std::is_enum_v<E>>>
constexpr auto ToUnderlying(E e) noexcept
{
    return static_cast<std::underlying_type_t<E>>(e);
}

namespace cross {

enum class CEMeta(Editor) VertexFrequency
{
    PerVertex,
    PerInstance,
    PerPatch,
    PerPatchControlPoint,
    Constant
};

// Do NOT change element order in following enum
enum class CEMeta(Editor) VertexFormat
{
    Unknown,
    Float,
    Float2,
    Float3,
    Float4,

    Half2,
    Half4,

    Byte4,
    UByte4,
    Byte4_Norm,
    UByte4_Norm,
    Color = UByte4_Norm,

    Short2,
    Short4,
    UShort2,
    UShort4,
    Short2_Norm,
    Short4_Norm,
    UShort2_Norm,
    UShort4_Norm,

    UInt_R10G10B10A2,
    UInt_R10G10B10A2_Norm,
    UInt4,
    INT4,

    UInt,   // 32bit
    INT,    // 32bit

    Short3_Norm,
    UShort3_Norm,
    Half3,
    Byte3_Norm,
    UByte3_Norm,
};

CEMeta(Editor) constexpr UInt32 GetByteSize(VertexFormat fmt)
{
    switch (fmt)
    {
    case VertexFormat::Float4:
    case VertexFormat::UInt4:
    case VertexFormat::INT4:
        return 16;
    case VertexFormat::Float3:
        return 12;
    case VertexFormat::Float2:
        return 8;
    case VertexFormat::Short4:
    case VertexFormat::UShort4:
    case VertexFormat::Short4_Norm:
    case VertexFormat::UShort4_Norm:
        return 8;
    case VertexFormat::UInt:
    case VertexFormat::INT:
    case VertexFormat::Float:
    case VertexFormat::Half2:
    case VertexFormat::Byte4:
    case VertexFormat::UByte4:
    case VertexFormat::Byte4_Norm:
    case VertexFormat::UByte4_Norm:
    case VertexFormat::Short2:
    case VertexFormat::UShort2:
    case VertexFormat::Short2_Norm:
    case VertexFormat::UShort2_Norm:
    case VertexFormat::UInt_R10G10B10A2:
    case VertexFormat::UInt_R10G10B10A2_Norm:
        return 4;
    default:
        return 0;
    }
}

enum class CEMeta(Editor) VertexSemanticSerial : UInt32
{
    Position = 0,
    Color,
    Normal,
    Tangent,
    BiNormal,
    BlendIndex,
    BlendWeight,
    TexCoord,
    PtSize,
    PositionT,
    TessFactor,
    InstanceData,
    Count   // keep it last
};

const std::array<VertexSemanticSerial, 12> gAllVertexSemanticSerial{VertexSemanticSerial::Position,
                                                                    VertexSemanticSerial::Color,
                                                                    VertexSemanticSerial::Normal,
                                                                    VertexSemanticSerial::Tangent,
                                                                    VertexSemanticSerial::BiNormal,
                                                                    VertexSemanticSerial::BlendIndex,
                                                                    VertexSemanticSerial::BlendWeight,
                                                                    VertexSemanticSerial::TexCoord,
                                                                    VertexSemanticSerial::PtSize,
                                                                    VertexSemanticSerial::PositionT,
                                                                    VertexSemanticSerial::TessFactor,
                                                                    VertexSemanticSerial::InstanceData
};


const std::array<std::string, 12> gAllVertexSemanticSerialName{
    "POSITION",
    "COLOR",
    "NORMAL",
    "TANGENT",
    "BINORMAL",
    "BLENDINDEX",
    "BLENDWEIGHT",
    "TEXCOORD",
    "PSIZE",
    "POSITIONT",
    "TESSFACTOR",
    "INSTANCEDATA",
};

enum CEMeta(Editor) VertexSemantic
{
    SemanticPosition = 1 << 5,
    SemanticColor = 1 << 6,
    SemanticNormal = 1 << 7,
    SemanticTangent = 1 << 8,
    SemanticBiNormal = 1 << 9,
    SemanticBlendIndex = 1 << 10,
    SemanticBlendWeight = 1 << 11,
    SemanticTexCoord = 1 << 12,
    SemanticPSize = 1 << 13,
    SemanticPositionT = 1 << 14,   // Transformed Vertex Position
    SemanticTessFactor = 1 << 15,
    SemanticInstance = 1 << 16,
};

enum class CEMeta(Editor) VertexChannel
{
    Unkown = 0,

    Position0 = 32 | 0,
    Position1 = 32 | 1,
    Position2 = 32 | 2,
    Position3 = 32 | 3,
    PositionLast = Position3,

    Color0 = 64 | 0,
    Color1 = 64 | 1,
    Color2 = 64 | 2,
    Color3 = 64 | 3,
    ColorLast = Color3,

    Normal0 = 128 | 0,
    Normal1 = 128 | 1,
    Normal2 = 128 | 2,
    Normal3 = 128 | 3,
    NormalLast = Normal3,

    Tangent0 = 256 | 0,
    Tangent1 = 256 | 1,
    Tangent2 = 256 | 2,
    Tangent3 = 256 | 3,
    TangentLast = Tangent3,

    BiNormal0 = 512 | 0,
    BiNormal1 = 512 | 1,
    BiNormal2 = 512 | 2,
    BiNormal3 = 512 | 3,
    BiNormalLast = BiNormal3,

    BlendWeight0 = 2048 | 0,
    BlendWeight1 = 2048 | 1,
    BlendWeight2 = 2048 | 2,
    BlendWeight3 = 2048 | 3,
    BlendWeightLast = BlendWeight3,

    BlendIndex0 = 1024 | 0,
    BlendIndex1 = 1024 | 1,
    BlendIndex2 = 1024 | 2,
    BlendIndex3 = 1024 | 3,
    BlendIndexLast = BlendIndex3,

    TexCoord0 = 4096 | 0,
    TexCoord1 = 4096 | 1,
    TexCoord2 = 4096 | 2,
    TexCoord3 = 4096 | 3,
    TexCoord4 = 4096 | 4,
    TexCoord5 = 4096 | 5,
    TexCoord6 = 4096 | 6,
    TexCoord7 = 4096 | 7,
    TexCoord8 = 4096 | 8,
    TexCoord9 = 4096 | 9,
    TexCoord10 = 4096 | 10,
    TexCoord11 = 4096 | 11,
    TexCoord12 = 4096 | 12,
    TexCoord13 = 4096 | 13,
    TexCoord14 = 4096 | 14,
    TexCoord15 = 4096 | 15,
    TexCoordLast = TexCoord15,

    PSize0 = 8192 | 0,
    PSize1 = 8192 | 1,
    PSize2 = 8192 | 2,
    PSize3 = 8192 | 3,
    PSizeLast = PSize3,

    PositionT = 16384 | 0,
    PositionTLast = PositionT,

    TessFactor0 = 32768 | 0,
    TessFactor1 = 32768 | 1,
    TessFactor2 = 32768 | 2,
    TessFactor3 = 32768 | 3,
    TessFactorLast = TessFactor3,

    InstanceData = 65536,
    //InstanceData0 = 65536 | 0,
    //InstanceData1 = 65536 | 1,
    //InstanceData2 = 65536 | 2,
    //InstanceData3 = 65536 | 3,
    //InstanceData4 = 65536 | 4,
    //InstanceData5 = 65536 | 5,
    //InstanceData6 = 65536 | 6,
    //InstanceData7 = 65536 | 7,
    //InstanceDataLast = InstanceData7,
    // Don't forget update GetVertexChannelMaskBit function when add or remove new value!!
};

// get a more compact value of VertexChannel
CEMeta(Editor)
constexpr uint32_t GetVertexChannelMaskBit(VertexChannel channelValue)
{
    switch (channelValue)
    {
    case cross::VertexChannel::Unkown:
        return 0;
    case cross::VertexChannel::Position0:
        return 1;
    case cross::VertexChannel::Position1:
        return 2;
    case cross::VertexChannel::Position2:
        return 3;
    case cross::VertexChannel::Position3:
        return 4;
    case cross::VertexChannel::Color0:
        return 5;
    case cross::VertexChannel::Color1:
        return 6;
    case cross::VertexChannel::Color2:
        return 7;
    case cross::VertexChannel::Color3:
        return 8;
    case cross::VertexChannel::Normal0:
        return 9;
    case cross::VertexChannel::Normal1:
        return 10;
    case cross::VertexChannel::Normal2:
        return 11;
    case cross::VertexChannel::Normal3:
        return 12;
    case cross::VertexChannel::Tangent0:
        return 13;
    case cross::VertexChannel::Tangent1:
        return 14;
    case cross::VertexChannel::Tangent2:
        return 15;
    case cross::VertexChannel::Tangent3:
        return 16;
    case cross::VertexChannel::BiNormal0:
        return 17;
    case cross::VertexChannel::BiNormal1:
        return 18;
    case cross::VertexChannel::BiNormal2:
        return 19;
    case cross::VertexChannel::BiNormal3:
        return 20;
    case cross::VertexChannel::BlendWeight0:
        return 21;
    case cross::VertexChannel::BlendWeight1:
        return 22;
    case cross::VertexChannel::BlendWeight2:
        return 23;
    case cross::VertexChannel::BlendWeight3:
        return 24;
    case cross::VertexChannel::BlendIndex0:
        return 25;
    case cross::VertexChannel::BlendIndex1:
        return 26;
    case cross::VertexChannel::BlendIndex2:
        return 27;
    case cross::VertexChannel::BlendIndex3:
        return 28;
    case cross::VertexChannel::TexCoord0:
        return 29;
    case cross::VertexChannel::TexCoord1:
        return 30;
    case cross::VertexChannel::TexCoord2:
        return 31;
    case cross::VertexChannel::TexCoord3:
        return 32;
    case cross::VertexChannel::TexCoord4:
        return 33;
    case cross::VertexChannel::TexCoord5:
        return 34;
    case cross::VertexChannel::TexCoord6:
        return 35;
    case cross::VertexChannel::TexCoord7:
        return 36;
    case cross::VertexChannel::TexCoord8:
        return 37;
    case cross::VertexChannel::TexCoord9:
        return 38;
    case cross::VertexChannel::TexCoord10:
        return 39;
    case cross::VertexChannel::TexCoord11:
        return 40;
    case cross::VertexChannel::TexCoord12:
        return 41;
    case cross::VertexChannel::TexCoord13:
        return 42;
    case cross::VertexChannel::TexCoord14:
        return 43;
    case cross::VertexChannel::TexCoord15:
        return 44;
    case cross::VertexChannel::PSize0:
        return 45;
    case cross::VertexChannel::PSize1:
        return 46;
    case cross::VertexChannel::PSize2:
        return 47;
    case cross::VertexChannel::PSize3:
        return 48;
    case cross::VertexChannel::PositionT:
        return 49;
    case cross::VertexChannel::TessFactor0:
        return 50;
    case cross::VertexChannel::TessFactor1:
        return 51;
    case cross::VertexChannel::TessFactor2:
        return 52;
    case cross::VertexChannel::TessFactor3:
        return 53;
    case cross::VertexChannel::InstanceData:
        return 54;
    //case cross::VertexChannel::InstanceData0:
    //    return 54;
    //case cross::VertexChannel::InstanceData1:
    //    return 55;
    //case cross::VertexChannel::InstanceData2:
    //    return 56;
    //case cross::VertexChannel::InstanceData3:
    //    return 57;
    //case cross::VertexChannel::InstanceData4:
    //    return 58;
    //case cross::VertexChannel::InstanceData5:
    //    return 59;
    //case cross::VertexChannel::InstanceData6:
    //    return 60;
    //case cross::VertexChannel::InstanceData7:
    //    return 61;
    default:
        assert(false);
        break;
    }
    return 0;
}

CEMeta(Editor)
constexpr VertexSemantic GetSemantic(VertexChannel channel)
{
    return VertexSemantic(ToUnderlying(channel) & (~15));
}

CEMeta(Editor)
constexpr UInt32 GetSemanticIndex(VertexChannel channel)
{
    return ToUnderlying(channel) & (15);
}

CEMeta(Editor)
constexpr VertexSemanticSerial GetSemanticSerial(VertexChannel channel)
{
    VertexSemantic semantic = GetSemantic(channel);
    for (UInt32 i = 0; i < ToUnderlying(VertexSemanticSerial::Count); i++)
    {
        if (ToUnderlying(semantic) & (1 << (i + 5)))
        {
            return VertexSemanticSerial(i);
        }
    }
    return VertexSemanticSerial::Count;
}

CEMeta(Editor)
constexpr VertexSemantic GetSemantic(VertexSemanticSerial semantic)
{
    return VertexSemantic(1 << (ToUnderlying(semantic) + 5));
}

CEMeta(Editor)
constexpr UInt32 MaxSemanticIndex(VertexSemantic semantic)
{
    switch (semantic)
    {
    case cross::SemanticPosition:
        return ToUnderlying(VertexChannel::PositionLast) - ToUnderlying(VertexChannel::Position0);
    case cross::SemanticColor:
        return ToUnderlying(VertexChannel::ColorLast) - ToUnderlying(VertexChannel::Color0);
    case cross::SemanticNormal:
        return ToUnderlying(VertexChannel::NormalLast) - ToUnderlying(VertexChannel::Normal0);
    case cross::SemanticTangent:
        return ToUnderlying(VertexChannel::TangentLast) - ToUnderlying(VertexChannel::Tangent0);
    case cross::SemanticBiNormal:
        return ToUnderlying(VertexChannel::BiNormalLast) - ToUnderlying(VertexChannel::BiNormal0);
    case cross::SemanticBlendIndex:
        return ToUnderlying(VertexChannel::BlendIndexLast) - ToUnderlying(VertexChannel::BlendIndex0);
    case cross::SemanticBlendWeight:
        return ToUnderlying(VertexChannel::BlendWeightLast) - ToUnderlying(VertexChannel::BlendWeight0);
    case cross::SemanticTexCoord:
        return ToUnderlying(VertexChannel::TexCoordLast) - ToUnderlying(VertexChannel::TexCoord0);
    case cross::SemanticPSize:
        return ToUnderlying(VertexChannel::PSizeLast) - ToUnderlying(VertexChannel::PSize0);
    case cross::SemanticPositionT:
        return ToUnderlying(VertexChannel::PositionTLast) - ToUnderlying(VertexChannel::PositionT);
    case cross::SemanticTessFactor:
        return ToUnderlying(VertexChannel::TessFactorLast) - ToUnderlying(VertexChannel::TessFactor0);
    case cross::SemanticInstance:
        return 1;
    }
    return 0;
}

CEMeta(Editor)
constexpr VertexChannel MakeVertexChannel(VertexSemantic semantic, UInt32 index)
{
    UInt32 maxIndex = MaxSemanticIndex(semantic);
    assert(index <= maxIndex);
    index = index < 0 ? 0 : index;
    index = index > maxIndex ? maxIndex : index;
    return VertexChannel(semantic | index);
}

// Position0 + 1 -> Position1
constexpr VertexChannel operator+(VertexChannel channel, UInt32 index)
{
    auto sindex = GetSemanticIndex(channel);
    auto s = GetSemantic(channel);

    return MakeVertexChannel(s, sindex + index);
}

// Position1 - 1 -> Position0
constexpr VertexChannel operator-(VertexChannel channel, UInt32 index)
{
    auto sindex = GetSemanticIndex(channel);
    auto s = GetSemantic(channel);

    return MakeVertexChannel(s, sindex - index);
}

#define MaxVertexStreams          4
#define MaxVertexElementPerStream 12
#define MaxVertexChannels         32

};   // namespace cross
