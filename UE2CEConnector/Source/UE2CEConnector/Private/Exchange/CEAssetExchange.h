#pragma once

#include "AssetExchange/include/SDK.h"

#include "Materials/MaterialInstance.h"
#include "Exporters/CEExportOptions.h"

#include <WidgetBlueprint.h>

#include <memory>
#include <functional>
class FCEConvertBuilder;
class UStaticMesh;
class USkeletalMesh;

namespace CEAssetExchange {

struct FAssetExchangeInput
{
    UCEExportOptions const* Options{nullptr};
    IAssetExchangeSDK* Sdk{nullptr};
    char const* RelativePath{"Contents/xx.nda"};
};

std::shared_ptr<IAssetExchangeSDK> CreateAssetExchangeSDKAutoPtr(const char* Staging, const char* engineresourcePath);

bool ExportMesh(UStaticMesh* StaticMesh, FAssetExchangeInput*);

bool ExportMesh(USkeletalMesh* SkeletalMesh, FAssetExchangeInput*);

bool ExportTexture(UTexture* Texture2D,  FAssetExchangeInput*);

bool ExportWorld(FCEConvertBuilder& Builder, const UWorld& World,  FAssetExchangeInput*, const bool AsPrefab = false);

bool ExportPrefab(FCEConvertBuilder& Builder, UStaticMeshComponent* StaticMesh,  FAssetExchangeInput*);

void TryRebuildMesh(UStaticMesh* StaticMesh, bool IsUseFullPrecisionUV, const UCEExportOptions* Options);

void TryReduceMesh(UStaticMesh* StaticMesh, UStaticMesh** Out, const UCEExportOptions* Options);

void CollectAllMaterialTextures(const UMaterialInterface* MaterialInterface, TArray<UTexture*>& OutDependencyAssets);

void CollectAllMPCs(const UMaterialInterface* MaterialInterface, TArray<UMaterialParameterCollection*>& OutDependencyAssets);

bool ExportMaterialFX(FCEConvertBuilder& Builder, const UMaterialInterface& MaterialInterface, FAssetExchangeInput*, const FString& DefaultMotherMaterial);

bool IsMaterialInstance(const UMaterialInterface& MaterialInterface);

void ExportMaterialParameterCollections(FCEConvertBuilder& Builder, TConstArrayView<TObjectPtr<UMaterialExpression>> Expressions, TSharedPtr<FJsonObject> OutJson, IFxAssemble* CE, FAssetExchangeInput* Input);

bool ExportWidgetBlueprint(UWidgetBlueprint const& widgetBlueprint, const FAssetExchangeInput*);
}   // namespace CEAssetExchange
