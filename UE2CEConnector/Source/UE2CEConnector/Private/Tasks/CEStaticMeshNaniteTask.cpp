#include "Tasks/CEStaticMeshNaniteTask.h"
#include "Exporters/NaniteDataExporter.h"
#include "Engine/StaticMesh.h"
#include "Rendering/NaniteResources.h"
#include "StaticMeshResources.h"
#include "UE2CEConnectorUtils.h"

DEFINE_LOG_CATEGORY_STATIC(LogCENaniteTask, Log, All);

FCEStaticMeshNaniteTask::FCEStaticMeshNaniteTask(UStaticMesh* InStaticMesh, const FCEExportOptions* InExportOptions)
    : StaticMesh(InStaticMesh)
    , ExportOptions(InExportOptions)
{
    check(StaticMesh);
    check(ExportOptions);
}

bool FCEStaticMeshNaniteTask::Complete()
{
    if (!StaticMesh)
    {
        UE_LOG(LogCENaniteTask, Error, TEXT("StaticMesh is null"));
        return false;
    }

    // 检查是否启用了Nanite
    if (!StaticMesh->NaniteSettings.bEnabled)
    {
        UE_LOG(LogCENaniteTask, Warning, TEXT("Nanite is not enabled for mesh: %s, falling back to standard export"), *StaticMesh->GetName());
        return ExportAsStandardMesh();
    }

    // 检查导出选项
    if (ExportOptions->NaniteEnabled == ENaniteEnabled::Disabled)
    {
        UE_LOG(LogCENaniteTask, Log, TEXT("Nanite export disabled by options, exporting as standard mesh"));
        return ExportAsStandardMesh();
    }

    // 检查Nanite资源是否有效
    const FStaticMeshRenderData* RenderData = StaticMesh->GetRenderData();
    if (!RenderData || !RenderData->NaniteResources.HasValidData())
    {
        UE_LOG(LogCENaniteTask, Warning, TEXT("No valid Nanite resources found, falling back to standard export"));
        return ExportAsStandardMesh();
    }

    UE_LOG(LogCENaniteTask, Log, TEXT("Exporting Nanite mesh: %s"), *StaticMesh->GetName());

    // 导出Nanite数据
    CEAssetExchange::NaniteExportData NaniteData;
    if (!FNaniteDataExporter::ExportNaniteData(StaticMesh, NaniteData))
    {
        UE_LOG(LogCENaniteTask, Error, TEXT("Failed to export Nanite data, falling back to standard export"));
        return ExportAsStandardMesh();
    }

    // 根据导出选项决定导出策略
    switch (ExportOptions->NaniteEnabled)
    {
        case ENaniteEnabled::ExportNaniteData:
            return ExportNaniteDataOnly(NaniteData);
            
        case ENaniteEnabled::ExportWithProxy:
            return ExportNaniteWithProxy(NaniteData);
            
        case ENaniteEnabled::ExportHybrid:
            return ExportHybridFormat(NaniteData);
            
        default:
            return ExportAsStandardMesh();
    }
}

bool FCEStaticMeshNaniteTask::ExportNaniteDataOnly(const CEAssetExchange::NaniteExportData& NaniteData)
{
    UE_LOG(LogCENaniteTask, Log, TEXT("Exporting pure Nanite data"));

    // 构建输出文件路径
    FString OutputPath = FUE2CEConnectorUtils::GetExportPath(StaticMesh, TEXT("nanite"));
    
    // 保存Nanite数据
    if (!FNaniteDataExporter::SaveToFile(NaniteData, OutputPath))
    {
        UE_LOG(LogCENaniteTask, Error, TEXT("Failed to save Nanite data to file"));
        return false;
    }

    // 创建元数据文件
    if (!CreateMetadataFile(NaniteData, OutputPath))
    {
        UE_LOG(LogCENaniteTask, Warning, TEXT("Failed to create metadata file"));
    }

    UE_LOG(LogCENaniteTask, Log, TEXT("Successfully exported Nanite data to: %s"), *OutputPath);
    return true;
}

bool FCEStaticMeshNaniteTask::ExportNaniteWithProxy(const CEAssetExchange::NaniteExportData& NaniteData)
{
    UE_LOG(LogCENaniteTask, Log, TEXT("Exporting Nanite data with proxy mesh"));

    // 首先导出Nanite数据
    if (!ExportNaniteDataOnly(NaniteData))
    {
        return false;
    }

    // 然后导出代理网格（通常是LOD 0或简化版本）
    if (ExportOptions->bExportNaniteProxy)
    {
        return ExportProxyMesh();
    }

    return true;
}

bool FCEStaticMeshNaniteTask::ExportHybridFormat(const CEAssetExchange::NaniteExportData& NaniteData)
{
    UE_LOG(LogCENaniteTask, Log, TEXT("Exporting hybrid format (Nanite + traditional LODs)"));

    // 导出Nanite数据
    if (!ExportNaniteDataOnly(NaniteData))
    {
        return false;
    }

    // 导出传统LOD链作为后备
    return ExportTraditionalLODs();
}

bool FCEStaticMeshNaniteTask::ExportAsStandardMesh()
{
    UE_LOG(LogCENaniteTask, Log, TEXT("Exporting as standard mesh"));
    
    // 使用现有的标准网格导出逻辑
    // 这里应该调用原有的FCEStaticMeshTask::Complete()逻辑
    
    // 创建标准导出输入
    FAssetExchangeInput Input;
    Input.AssetName = StaticMesh->GetName();
    Input.AssetPath = FUE2CEConnectorUtils::GetExportPath(StaticMesh, TEXT("mesh"));
    
    // 调用标准网格导出
    return ExportMesh(StaticMesh, &Input);
}

bool FCEStaticMeshNaniteTask::ExportProxyMesh()
{
    UE_LOG(LogCENaniteTask, Log, TEXT("Exporting Nanite proxy mesh"));

    // 获取LOD 0作为代理网格
    const FStaticMeshRenderData* RenderData = StaticMesh->GetRenderData();
    if (!RenderData || RenderData->LODResources.Num() == 0)
    {
        UE_LOG(LogCENaniteTask, Error, TEXT("No LOD resources available for proxy export"));
        return false;
    }

    // 创建代理导出输入
    FAssetExchangeInput ProxyInput;
    ProxyInput.AssetName = StaticMesh->GetName() + TEXT("_Proxy");
    ProxyInput.AssetPath = FUE2CEConnectorUtils::GetExportPath(StaticMesh, TEXT("proxy"));
    
    // 导出代理网格（使用LOD 0）
    return ExportMesh(StaticMesh, &ProxyInput);
}

bool FCEStaticMeshNaniteTask::ExportTraditionalLODs()
{
    UE_LOG(LogCENaniteTask, Log, TEXT("Exporting traditional LOD chain"));

    const FStaticMeshRenderData* RenderData = StaticMesh->GetRenderData();
    if (!RenderData)
    {
        return false;
    }

    // 为每个LOD级别创建单独的导出
    for (int32 LODIndex = 0; LODIndex < RenderData->LODResources.Num(); ++LODIndex)
    {
        FAssetExchangeInput LODInput;
        LODInput.AssetName = FString::Printf(TEXT("%s_LOD%d"), *StaticMesh->GetName(), LODIndex);
        LODInput.AssetPath = FUE2CEConnectorUtils::GetExportPath(StaticMesh, FString::Printf(TEXT("lod%d"), LODIndex));
        
        // 这里需要修改ExportMesh函数以支持特定LOD导出
        // 或者创建临时的单LOD静态网格
        if (!ExportSpecificLOD(StaticMesh, LODIndex, &LODInput))
        {
            UE_LOG(LogCENaniteTask, Warning, TEXT("Failed to export LOD %d"), LODIndex);
        }
    }

    return true;
}

bool FCEStaticMeshNaniteTask::ExportSpecificLOD(UStaticMesh* Mesh, int32 LODIndex, FAssetExchangeInput* Input)
{
    // 这是一个简化的实现，实际需要更复杂的LOD特定导出逻辑
    UE_LOG(LogCENaniteTask, Log, TEXT("Exporting LOD %d for mesh %s"), LODIndex, *Mesh->GetName());
    
    // 这里应该实现特定LOD的导出逻辑
    // 可能需要修改现有的ExportMesh函数以接受LOD参数
    
    return ExportMesh(Mesh, Input);
}

bool FCEStaticMeshNaniteTask::CreateMetadataFile(const CEAssetExchange::NaniteExportData& NaniteData, const FString& BasePath)
{
    // 创建JSON格式的元数据文件
    FString MetadataPath = FPaths::ChangeExtension(BasePath, TEXT("json"));
    
    FString JsonContent = TEXT("{\n");
    JsonContent += FString::Printf(TEXT("  \"version\": %d,\n"), NaniteData.Mesh.Version);
    JsonContent += FString::Printf(TEXT("  \"sourceMesh\": \"%s\",\n"), *NaniteData.SourceMeshName);
    JsonContent += FString::Printf(TEXT("  \"sourceHash\": %u,\n"), NaniteData.SourceMeshHash);
    JsonContent += FString::Printf(TEXT("  \"numClusters\": %d,\n"), NaniteData.Mesh.NumClusters);
    JsonContent += FString::Printf(TEXT("  \"numHierarchyNodes\": %d,\n"), NaniteData.Mesh.NumHierarchyNodes);
    JsonContent += FString::Printf(TEXT("  \"originalTriangles\": %d,\n"), NaniteData.Stats.OriginalTriangles);
    JsonContent += FString::Printf(TEXT("  \"positionPrecision\": %d,\n"), NaniteData.Mesh.PositionPrecision);
    JsonContent += FString::Printf(TEXT("  \"normalPrecision\": %d,\n"), NaniteData.Mesh.NormalPrecision);
    JsonContent += TEXT("  \"bounds\": {\n");
    JsonContent += FString::Printf(TEXT("    \"min\": [%.6f, %.6f, %.6f],\n"), 
        NaniteData.Mesh.MeshBounds[0], NaniteData.Mesh.MeshBounds[1], NaniteData.Mesh.MeshBounds[2]);
    JsonContent += FString::Printf(TEXT("    \"max\": [%.6f, %.6f, %.6f]\n"), 
        NaniteData.Mesh.MeshBounds[3], NaniteData.Mesh.MeshBounds[4], NaniteData.Mesh.MeshBounds[5]);
    JsonContent += TEXT("  }\n");
    JsonContent += TEXT("}\n");

    return FFileHelper::SaveStringToFile(JsonContent, *MetadataPath);
}
