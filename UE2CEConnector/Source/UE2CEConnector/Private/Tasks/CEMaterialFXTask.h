#pragma once

#include "CETask.h"
#include "Builders/CEConvertBuilder.h"

class FCEMaterialFXTask : public FCETask
{
public:
    FCEMaterialFXTask(FCEConvertBuilder& Builder, const UMaterialInterface* MaterialInterface, const FString& SavePath)
        : FCETask(ECETaskPriority::Material, SavePath, Builder, MaterialInterface)
        , MaterialInterface(MaterialInterface)
    {}

    FString GetName() const override
    {
        return MaterialInterface->GetName();
    }

    bool Complete() override;

private:
    const UMaterialInterface* MaterialInterface;
};
