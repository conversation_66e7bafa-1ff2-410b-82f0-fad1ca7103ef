#pragma once
#include "CETask.h"

class UAnimSequence;

// Define a task for animation sequence conversion
class FCEAnimSequenceTask : public FCETask
{
public:
    FCEAnimSequenceTask(const FString& savePath, FCEConvertBuilder& builder, const UAnimSequence* animSequence);

    virtual FString GetName() const override;

    virtual bool Complete() override;

private:
    const UAnimSequence* AnimSequence;

    // Linear Key Reduction helper structures and functions
    struct TranslationKey
    {
        float Time;
        FVector Translation;

        static TranslationKey Zero()
        {
            return {0.0f, FVector::ZeroVector};
        }
    };

    struct RotationKey
    {
        float Time;
        FQuat Rotation;

        static RotationKey Zero()
        {
            return {0.0f, FQuat::Identity};
        }
    };

    struct ScaleKey
    {
        float Time;
        FVector Scale;

        static ScaleKey One()
        {
            return {0.0f, FVector::OneVector};
        }
    };

    // Helper function to get time from a key
    template<typename KeyType>
    static float GetKeyTime(const KeyType& Key)
    {
        return Key.Time;
    }

    // Linear interpolation for translation keys
    static TranslationKey InterpTranslationKey(const TranslationKey& A, const TranslationKey& B, float Alpha)
    {
        TranslationKey Result;
        Result.Time = FMath::Lerp(A.Time, B.Time, Alpha);
        Result.Translation = FMath::Lerp(A.Translation, B.Translation, Alpha);
        return Result;
    }

    // Distance between translation keys
    static float TranslationKeyDistance(const TranslationKey& A, const TranslationKey& B)
    {
        return FVector::Distance(A.Translation, B.Translation);
    }

    // Quaternion interpolation (Slerp) for rotation keys
    static RotationKey InterpRotationKey(const RotationKey& A, const RotationKey& B, float Alpha)
    {
        RotationKey Result;
        Result.Time = FMath::Lerp(A.Time, B.Time, Alpha);
        Result.Rotation = FQuat::Slerp(A.Rotation, B.Rotation, Alpha);
        return Result;
    }

    // Distance between rotation keys (using quaternion angle difference)
    static float RotationKeyDistance(const RotationKey& A, const RotationKey& B)
    {
        // Use angle in radians between quaternions as distance metric
        return A.Rotation.AngularDistance(B.Rotation);
    }

    // Linear interpolation for scale keys
    static ScaleKey InterpScaleKey(const ScaleKey& A, const ScaleKey& B, float Alpha)
    {
        ScaleKey Result;
        Result.Time = FMath::Lerp(A.Time, B.Time, Alpha);
        Result.Scale = FMath::Lerp(A.Scale, B.Scale, Alpha);
        return Result;
    }

    // Distance between scale keys
    static float ScaleKeyDistance(const ScaleKey& A, const ScaleKey& B)
    {
        return FVector::Distance(A.Scale, B.Scale);
    }

    // Linear Key Reduction function (finds redundant keys)
    template<typename IteratorType, typename RatioFunc, typename InterpolateFunc, typename DistanceFunc>
    std::vector<IteratorType> LinearKeyReduction(
        IteratorType Begin,
        IteratorType End,
        float Threshold,
        RatioFunc Ratio,
        InterpolateFunc Interpolate,
        DistanceFunc Distance)
    {
        std::vector<IteratorType> RedundantKeys;
        if (End - Begin <= 2)
            return RedundantKeys; // Need at least 3 keys for reduction

        IteratorType LastNotRedundant = Begin;
        for (auto It = Begin + 1; It != End - 1; ++It)
        {
            // Calculate interpolation alpha
            float RatioLastKey = Ratio(*LastNotRedundant);
            float RatioCurrentKey = Ratio(*It);
            float RatioNextKey = Ratio(*(It + 1));
            float Alpha = (RatioCurrentKey - RatioLastKey) / (RatioNextKey - RatioLastKey);

            // Interpolate between last non-redundant key and next key
            auto InterpResult = Interpolate(*LastNotRedundant, *(It + 1), Alpha);

            // If the distance is below threshold, mark as redundant
            if (Distance(InterpResult, *It) <= Threshold)
            {
                RedundantKeys.push_back(It);
            }
            else
            {
                LastNotRedundant = It;
            }
        }
        return RedundantKeys;
    }

    // Erase redundant keys from a container
    template<typename KeyType>
    void EraseRedundantKeys(std::vector<KeyType>& Keys, const std::vector<typename std::vector<KeyType>::iterator>& RedundantKeyIterators)
    {
        if (RedundantKeyIterators.empty())
            return;

        // Mark keys for removal
        std::vector<bool> IsRedundant(Keys.size(), false);
        for (auto It : RedundantKeyIterators)
        {
            size_t Index = It - Keys.begin();
            IsRedundant[Index] = true;
        }

        // Create new vector without redundant keys
        std::vector<KeyType> FilteredKeys;
        FilteredKeys.reserve(Keys.size() - RedundantKeyIterators.size());

        for (size_t i = 0; i < Keys.size(); ++i)
        {
            if (!IsRedundant[i])
            {
                FilteredKeys.push_back(Keys[i]);
            }
        }

        Keys = std::move(FilteredKeys);
    }

    // Apply LKR compression to a track
    void CompressTrack(
        std::vector<TranslationKey>& TranslationKeys,
        std::vector<RotationKey>& RotationKeys,
        std::vector<ScaleKey>& ScaleKeys)
    {
        // Compression thresholds
        constexpr float TranslationThreshold = 0.0001f;
        constexpr float RotationThreshold = 0.0001f;
        constexpr float ScaleThreshold = 0.0001f;

        // Compress translation keys
        auto RedundantTranslationKeys = LinearKeyReduction(
            TranslationKeys.begin(),
            TranslationKeys.end(),
            TranslationThreshold,
            GetKeyTime<TranslationKey>,
            InterpTranslationKey,
            TranslationKeyDistance);
        EraseRedundantKeys(TranslationKeys, RedundantTranslationKeys);

        // Compress rotation keys
        auto RedundantRotationKeys = LinearKeyReduction(
            RotationKeys.begin(),
            RotationKeys.end(),
            RotationThreshold,
            GetKeyTime<RotationKey>,
            InterpRotationKey,
            RotationKeyDistance);
        EraseRedundantKeys(RotationKeys, RedundantRotationKeys);

        // Compress scale keys
        auto RedundantScaleKeys = LinearKeyReduction(
            ScaleKeys.begin(),
            ScaleKeys.end(),
            ScaleThreshold,
            GetKeyTime<ScaleKey>,
            InterpScaleKey,
            ScaleKeyDistance);
        EraseRedundantKeys(ScaleKeys, RedundantScaleKeys);
    }
};