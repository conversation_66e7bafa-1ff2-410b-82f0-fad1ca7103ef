#include "Tasks/CEStaticMeshTask.h"
#include <stdexcept>
#include "Builders/CEConvertBuilder.h"
#include "CERenderStateDefines.h"
#include "Exporters/CEExportOptions.h"
#include "CELog.h"
#include "ResourceAsset_generated.h"
#include "ImportMeshAssetData_generated.h"
#include <Exchange/CEAssetExchange.h>
#include <stdexcept>
#include <numeric>

#include "Exchange/StringExchange.h"

static constexpr uint32 MAXUV_CE_SUPPORT = 4u;

static void RemapCECoord(FVector3f& Vec)
{
    float TmpValue = Vec.Y;
    Vec.Y = Vec.Z;
    Vec.Z = -TmpValue;
}

static void RemapBoundingBox(const FBox& box, std::unique_ptr<CrossSchema::float3>& max, std::unique_ptr<CrossSchema::float3>& min)
{
    max = std::make_unique<CrossSchema::float3>(box.Max.X, box.Max.Z, -box.Min.Y);
    min = std::make_unique<CrossSchema::float3>(box.Min.X, box.Min.Z, -box.Max.Y);
}

bool FCEStaticMeshTask::Complete()
{
    const auto ExportOptions = mBuilder.GetOptions();
    using namespace CEAssetExchange;

    FAssetExchangeInput Input;
    Input.Options = ExportOptions;
    Input.Sdk = mBuilder.AssetExchangeSdk.get();

    SDKSettings Settings;
    Settings.mBalanceMode = ExportOptions->bUseAssetExchangeSdkBalanceMode;
    Settings.mOverwriteExistingTexture = ExportOptions->bOverwriteExistingTexture;
    Settings.mOverwriteExistingMaterial = ExportOptions->bOverwriteExistingMaterial;
    Settings.mOverwriteExistingMesh = ExportOptions->bOverwriteExistingMesh;
    Settings.mOverwriteExistingWorld = ExportOptions->bOverwriteExistingLevel;
    Input.Sdk->BeginAssemble(&Settings);

    FString RelativePath = mSavePath;
    Input.RelativePath = StringMapper::Instance().FromUEToCEString(*RelativePath);

    bool NeedRebuildAndGenLOD = false;
    if (!ExportOptions->bOverwriteExistingMesh)
    {
        FString FStagingPath = UTF8_TO_TCHAR(Input.Sdk->GetStagingPath());
        if (FPaths::FileExists(FStagingPath + "/" + RelativePath))
        {
            CE_Log(TEXT("CEAssetExchange: Skip Rebuild And Gen LOD for \"%s\", due to file already exist"), *RelativePath);
            NeedRebuildAndGenLOD = false;
        }
        else
        {
            NeedRebuildAndGenLOD = true;
        }
    }

    UStaticMesh* InStaticMesh = const_cast<UStaticMesh*>(StaticMesh);

    if (NeedRebuildAndGenLOD)
    {
        TryRebuildMesh(InStaticMesh, ExportOptions->bUseFullPrecisionUV || InStaticMesh->NaniteSettings.bEnabled, ExportOptions);
        TryReduceMesh(InStaticMesh, &InStaticMesh, ExportOptions);
    }

    bool bSuccess = ExportMesh(InStaticMesh, &Input);

    return bSuccess;
}

void FCEStaticMeshTask::BuildIndexStream(const FStaticMeshLODResources& LODResource)
{
    const int32 IndexDataSize = LODResource.IndexBuffer.GetIndexDataSize();
    const bool Is32Bit = LODResource.IndexBuffer.Is32Bit();
    Resource->MeshAssetDataT->findexstream = std::make_unique<CrossSchema::IndexStreamAssetDataT>();
    Resource->MeshAssetDataT->findexstream->fcount = LODResource.IndexBuffer.GetNumIndices();
    Resource->MeshAssetDataT->findexstream->fis16bitindex = !Is32Bit;
    Resource->MeshAssetDataT->findexstream->fdata.resize(IndexDataSize);
    if (Is32Bit)
    {
        const uint32* IndexBuffer = LODResource.IndexBuffer.AccessStream32();
        std::vector<uint32> ReorderedIndice;
        ReorderIndice<uint32, uint32>(IndexBuffer, LODResource.IndexBuffer.GetNumIndices(), ReorderedIndice);
        memcpy(Resource->MeshAssetDataT->findexstream->fdata.data(), ReorderedIndice.data(), IndexDataSize);
    }
    else
    {
        const uint16* IndexBuffer = LODResource.IndexBuffer.AccessStream16();
        std::vector<uint16> ReorderedIndice;
        ReorderIndice<uint16, uint16>(IndexBuffer, LODResource.IndexBuffer.GetNumIndices(), ReorderedIndice);
        memcpy(Resource->MeshAssetDataT->findexstream->fdata.data(), ReorderedIndice.data(), IndexDataSize);
    }
}

void FCEStaticMeshTask::BuildMeshParts(const FBox& BoundingBox, uint32 IndexNum, uint32 VertexNum)
{
    Resource->MeshAssetDataT->fmeshpartinfo.clear();
    Resource->MeshAssetDataT->fmeshpartinfo.resize(0);
    // TODO Only one meshpart ??
    for (int32 i = 0; i < 1; ++i)
    {
        std::unique_ptr<CrossSchema::ImportMeshPartAssetInfoT> PartAssetInfo = std::make_unique<CrossSchema::ImportMeshPartAssetInfoT>();
        PartAssetInfo->fbindinginfo = std::make_unique<CrossSchema::MeshBoundT>();
        RemapBoundingBox(BoundingBox, PartAssetInfo->fbindinginfo->fmax, PartAssetInfo->fbindinginfo->fmin);
        PartAssetInfo->findexcount = IndexNum;
        PartAssetInfo->findexstart = 0;
        PartAssetInfo->fmaterialindex = 0;
        PartAssetInfo->fmiscflag = 0;
        PartAssetInfo->fnameindex = 0;
        PartAssetInfo->fprimitivecount = IndexNum / 3;
        PartAssetInfo->fprimitivetype = static_cast<uint32>(cross::PrimitiveTopology::TriangleList);
        PartAssetInfo->frenderpriority = 0;
        PartAssetInfo->fshadowbias = 0;
        PartAssetInfo->fshadownormalbias = 0;
        PartAssetInfo->fvertexcount = VertexNum;
        PartAssetInfo->fvertexstart = 0;
        Resource->MeshAssetDataT->fmeshpartinfo.push_back(std::move(PartAssetInfo));
    }
}

uint32 FCEStaticMeshTask::BuildVertexChannel(const FStaticMeshLODResources& LODResource)
{
    uint32 SemanticMask = 0u;
    const FStaticMeshVertexBuffer& StaticMeshVertexBuffer = LODResource.VertexBuffers.StaticMeshVertexBuffer;

    const uint32 VertexNums = LODResource.VertexBuffers.PositionVertexBuffer.GetNumVertices();
    const UCEExportOptions* Options = mBuilder.GetOptions();

    // Position Channel
    {
        const uint32 VertexStride = LODResource.VertexBuffers.PositionVertexBuffer.GetStride();
        const uint32 PostionBufferSize = VertexNums * VertexStride;
        TmpBuffer.clear();
        TmpBuffer.resize(PostionBufferSize);
        const uint8* Data = (uint8*)LODResource.VertexBuffers.PositionVertexBuffer.GetVertexData();
        //std::copy(Data, Data + PostionBufferSize, TmpBuffer.begin());

        TArray<FVector3f> Positions;
        Positions.SetNum(VertexNums);
        memcpy(Positions.GetData(), Data, Positions.Num() * sizeof(FVector3f));
        for (uint32 VertexIndex = 0; VertexIndex < VertexNums; ++VertexIndex)
        {
            RemapCECoord(Positions[VertexIndex]);
        }
        memcpy(TmpBuffer.data(), Positions.GetData(), PostionBufferSize);

        AddVertexChannelData<uint8, CrossSchema::float3>(cross::VertexChannel::Position0, cross::VertexFormat::Float3, TmpBuffer);
        SemanticMask |= cross::SemanticPosition;
        DebugDataCheck<FVector3f>(TmpBuffer, VertexNums);
    }

    // UV Channel
    {
        const void* UVData = StaticMeshVertexBuffer.GetTexCoordData();
        const uint32 TexCoordsNum = std::min<uint32>(MAXUV_CE_SUPPORT, StaticMeshVertexBuffer.GetNumTexCoords());
        const bool IsFullPrecisionsUV = StaticMeshVertexBuffer.GetUseFullPrecisionUVs();
        typedef TStaticMeshVertexUVsDatum<typename TStaticMeshVertexUVsTypeSelector<EStaticMeshVertexUVType::HighPrecision>::UVsTypeT> UVTypeHigh;
        typedef TStaticMeshVertexUVsDatum<typename TStaticMeshVertexUVsTypeSelector<EStaticMeshVertexUVType::Default>::UVsTypeT> UVTypeDefault;

        if (Options->bUseFullPrecisionUV)
        {
            if (IsFullPrecisionsUV)
            {
                typedef std::conditional<true, UVTypeHigh, UVTypeDefault>::type UVType;
                TArray<UVType> UVs;
                UVs.SetNum(VertexNums * TexCoordsNum);
                memcpy(UVs.GetData(), UVData, UVs.Num() * sizeof(UVType));

                // UVTypeHigh -> FullPrecisionUV
                for (uint32 UVChannelIndex = 0; UVChannelIndex < TexCoordsNum; ++UVChannelIndex)
                {
                    TmpBuffer.clear();
                    TmpBuffer.resize(VertexNums * sizeof(FVector2f));
                    uint32 BeginChannel = static_cast<uint32>(cross::VertexChannel::TexCoord0);
                    for (uint32 VertexIndex = 0; VertexIndex < VertexNums; ++VertexIndex)
                    {
                        int32 Index = VertexIndex * TexCoordsNum + UVChannelIndex;
                        const FVector2f UV = UVs[Index].GetUV();
                        int32 Stride = VertexIndex * sizeof(FVector2f);
                        memcpy(TmpBuffer.data() + Stride, &UV, sizeof(FVector2f));
                    }
                    AddVertexChannelData<uint8, CrossSchema::float2>(static_cast<cross::VertexChannel>(BeginChannel | UVChannelIndex), cross::VertexFormat::Float2, TmpBuffer);
                }
            }
            else
            {
                typedef std::conditional<false, UVTypeHigh, UVTypeDefault>::type UVType;
                TArray<UVType> UVs;
                UVs.SetNum(VertexNums * TexCoordsNum);
                memcpy(UVs.GetData(), UVData, UVs.Num() * sizeof(UVType));

                // UVTypeDefault -> FullPrecisionUV
                for (uint32 UVChannelIndex = 0; UVChannelIndex < TexCoordsNum; ++UVChannelIndex)
                {
                    TmpBuffer.clear();
                    TmpBuffer.resize(VertexNums * sizeof(FVector2f));
                    uint32 BeginChannel = static_cast<uint32>(cross::VertexChannel::TexCoord0);
                    for (uint32 VertexIndex = 0; VertexIndex < VertexNums; ++VertexIndex)
                    {
                        int32 Index = VertexIndex * TexCoordsNum + UVChannelIndex;
                        const FVector2f UV = FVector2f(UVs[Index].GetUV());
                        int32 Stride = VertexIndex * sizeof(FVector2f);
                        memcpy(TmpBuffer.data() + Stride, &UV, sizeof(FVector2f));
                    }
                    AddVertexChannelData<uint8, CrossSchema::float2>(static_cast<cross::VertexChannel>(BeginChannel | UVChannelIndex), cross::VertexFormat::Float2, TmpBuffer);
                }
            }
        }
        else
        {
            if (IsFullPrecisionsUV)
            {
                typedef std::conditional<true, UVTypeHigh, UVTypeDefault>::type UVType;
                TArray<UVType> UVs;
                UVs.SetNum(VertexNums * TexCoordsNum);
                memcpy(UVs.GetData(), UVData, UVs.Num() * sizeof(UVType));

                // UVTypeHigh -> HalfPrecisionUV
                for (uint32 UVChannelIndex = 0; UVChannelIndex < TexCoordsNum; ++UVChannelIndex)
                {
                    TmpBuffer.clear();
                    TmpBuffer.resize(VertexNums * sizeof(FVector2DHalf));
                    uint32 BeginChannel = static_cast<uint32>(cross::VertexChannel::TexCoord0);
                    for (uint32 VertexIndex = 0; VertexIndex < VertexNums; ++VertexIndex)
                    {
                        int32 Index = VertexIndex * TexCoordsNum + UVChannelIndex;
                        const FVector2DHalf UV = FVector2DHalf(UVs[Index].GetUV());
                        int32 Stride = VertexIndex * sizeof(FVector2DHalf);
                        memcpy(TmpBuffer.data() + Stride, &UV, sizeof(FVector2DHalf));
                    }
                    AddVertexChannelData<uint8, float>(static_cast<cross::VertexChannel>(BeginChannel | UVChannelIndex), cross::VertexFormat::Half2, TmpBuffer);
                }
            }
            else
            {
                typedef std::conditional<false, UVTypeHigh, UVTypeDefault>::type UVType;
                TArray<UVType> UVs;
                UVs.SetNum(VertexNums * TexCoordsNum);
                memcpy(UVs.GetData(), UVData, UVs.Num() * sizeof(UVType));

                // UVTypeDefault -> HalfPrecisionUV
                for (uint32 UVChannelIndex = 0; UVChannelIndex < TexCoordsNum; ++UVChannelIndex)
                {
                    TmpBuffer.clear();
                    TmpBuffer.resize(VertexNums * sizeof(FVector2DHalf));
                    uint32 BeginChannel = static_cast<uint32>(cross::VertexChannel::TexCoord0);
                    for (uint32 VertexIndex = 0; VertexIndex < VertexNums; ++VertexIndex)
                    {
                        int32 Index = VertexIndex * TexCoordsNum + UVChannelIndex;
                        const FVector2DHalf UV = UVs[Index].GetUV();
                        const FVector2D testUV = FVector2D(UVs[Index].GetUV());
                        int32 Stride = VertexIndex * sizeof(FVector2DHalf);
                        memcpy(TmpBuffer.data() + Stride, &UV, sizeof(FVector2DHalf));
                    }
                    AddVertexChannelData<uint8, float>(static_cast<cross::VertexChannel>(BeginChannel | UVChannelIndex), cross::VertexFormat::Half2, TmpBuffer);
                }
            }
        }

        SemanticMask |= cross::SemanticTexCoord;
        DebugDataCheck<FVector2f>(TmpBuffer, VertexNums);
    }

    // Normal Channel
    {
        const void* TangentData = StaticMeshVertexBuffer.GetTangentData();
        const bool IsHighPrecisionTangentBasis = StaticMeshVertexBuffer.GetUseHighPrecisionTangentBasis();

        typedef TStaticMeshVertexTangentDatum<typename TStaticMeshVertexTangentTypeSelector<EStaticMeshVertexTangentBasisType::Default>::TangentTypeT> TangentTypeHigh;
        typedef TStaticMeshVertexTangentDatum<typename TStaticMeshVertexTangentTypeSelector<EStaticMeshVertexTangentBasisType::Default>::TangentTypeT> TangentTypeDefault;
        using TangentType = std::conditional<false, TangentTypeHigh, TangentTypeDefault>::type;
        TArray<TangentType> Tangents;
        Tangents.SetNum(VertexNums);
        memcpy(Tangents.GetData(), TangentData, Tangents.Num() * sizeof(TangentType));

        // Compress Normal
        if (Options->bUsetFullPrecisionTNB)
        {
            TmpBuffer.clear();
            TmpBuffer.resize(Tangents.Num() * sizeof(FVector3f));
            for (uint32 VertexIndex = 0; VertexIndex < VertexNums; ++VertexIndex)
            {
                TangentType TangentItem = Tangents[VertexIndex];
                FVector4f TangentZ = TangentItem.GetTangentZ();
                FVector3f Normal = FVector3f(TangentZ.X, TangentZ.Y, TangentZ.Z);
                RemapCECoord(Normal);
                memcpy(TmpBuffer.data() + VertexIndex * sizeof(FVector3f), &Normal, sizeof(FVector3f));
            }
            AddVertexChannelData<uint8, CrossSchema::float3>(cross::VertexChannel::Normal0, cross::VertexFormat::Float3, TmpBuffer);
            SemanticMask |= cross::SemanticNormal;
        }
        else
        {
            std::vector<UChar3> NormalizedData;
            for (uint32 VertexIndex = 0; VertexIndex < VertexNums; ++VertexIndex)
            {
                TangentType TangentItem = Tangents[VertexIndex];
                FVector4f TangentZ = TangentItem.GetTangentZ();

                uint8 X, Y, Z;
                FloatToSNORM(TangentZ.X, X);
                FloatToSNORM(TangentZ.Z, Y);
                FloatToSNORM(-TangentZ.Y, Z);
                NormalizedData.emplace_back(X, Y, Z);
            }
            AddVertexChannelData<UChar3, UChar3>(cross::VertexChannel::Normal0, cross::VertexFormat::Byte3_Norm, NormalizedData);
            SemanticMask |= cross::SemanticNormal;
        }

        // Compress Tangent/Binormal
        if (Options->bUsetFullPrecisionTNB)
        {
            TmpBuffer.clear();
            TmpBuffer.resize(Tangents.Num() * sizeof(FVector4f));
            for (uint32 VertexIndex = 0; VertexIndex < VertexNums; ++VertexIndex)
            {
                TangentType TangentItem = Tangents[VertexIndex];
                FVector3f TangentX = TangentItem.GetTangentX();
                RemapCECoord(TangentX);
                FVector4f Tangent = FVector4f(TangentX.X, TangentX.Y, TangentX.Z, -TangentItem.GetTangentZ().W);
                memcpy(TmpBuffer.data() + VertexIndex * sizeof(FVector4f), &Tangent, sizeof(FVector4f));
            }
            AddVertexChannelData<uint8, CrossSchema::float4>(cross::VertexChannel::Tangent0, cross::VertexFormat::Float4, TmpBuffer);
            SemanticMask |= cross::SemanticTangent;
        }
        else
        {
            std::vector<UChar4> NormalizedData;
            for (uint32 VertexIndex = 0; VertexIndex < VertexNums; ++VertexIndex)
            {
                TangentType TangentItem = Tangents[VertexIndex];
                FVector3f TangentX = TangentItem.GetTangentX();

                uint8 X, Y, Z, W;
                FloatToSNORM(TangentX.X, X);
                FloatToSNORM(TangentX.Z, Y);
                FloatToSNORM(-TangentX.Y, Z);
                FloatToSNORM(-TangentItem.GetTangentZ().W, W);
                NormalizedData.emplace_back(X, Y, Z, W);
            }
            AddVertexChannelData<UChar4, UChar4>(cross::VertexChannel::Tangent0, cross::VertexFormat::Byte4_Norm, NormalizedData);
            SemanticMask |= cross::SemanticTangent;
        }
    }

    // Vertex Color Channel
    {
        const FColorVertexBuffer& ColorVertexBuffer = LODResource.VertexBuffers.ColorVertexBuffer;
        if (ColorVertexBuffer.GetVertexData())
        {
            TmpBuffer.clear();
            TmpBuffer.resize(VertexNums * sizeof(FColor));
            for (uint32 VertexIndex = 0; VertexIndex < VertexNums; ++VertexIndex)
            {
                const FColor& VertexColor = ColorVertexBuffer.VertexColor(VertexIndex);
                memcpy(TmpBuffer.data() + VertexIndex * sizeof(FColor), &VertexColor, sizeof(FColor));
            }
            AddVertexChannelData<uint8, uint32>(cross::VertexChannel::Color0, cross::VertexFormat::Color, TmpBuffer);
            SemanticMask |= cross::SemanticColor;
        }
    }

    return SemanticMask;
}

VertexChannelAssetData& FCEStaticMeshTask::GetChannelAssetData(cross::VertexChannel Channel)
{
    for (int i = 0; i < VertexChannelDatas.size(); i++)
    {
        if (VertexChannelDatas[i].VertexChannel == static_cast<uint32>(Channel))
        {
            return VertexChannelDatas[i];
        }
    }
    throw std::runtime_error("GetChannelAssetData cannot find the channel in mVertexChannelData\n");
}

bool FCEStaticMeshTask::VertexChannelExist(cross::VertexChannel Channel) const
{
    for (const auto& ChannelData : VertexChannelDatas)
    {
        if (ChannelData.VertexChannel == static_cast<uint32>(Channel))
        {
            return true;
        }
    }
    return false;
}