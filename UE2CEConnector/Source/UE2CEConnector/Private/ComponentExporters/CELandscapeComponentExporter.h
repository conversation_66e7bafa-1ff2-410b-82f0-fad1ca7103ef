#pragma once
#include "CoreMinimal.h"
#include "CEBaseComponentExporter.h"
#include "CELandscapeComponentExporter.generated.h"


UCLASS()
class UCELandscapeComponentExporter : public UCEBaseComponentExporter
{
    GENERATED_UCLASS_BODY()
public:
    virtual CEAssetExchange::IEntity* AddEntityFromSceneComponent(
        FCEConvertBuilder& InBuilder,
        CEAssetExchange::IWorldAssemble* InWorldAssemble,
        USceneComponent* InSceneComponent,
        CEAssetExchange::IEntity* InParentEntity,
        USceneComponent* InExportComponent,
        CEAssetExchange::FAssetExchangeInput* InInput) override;
};