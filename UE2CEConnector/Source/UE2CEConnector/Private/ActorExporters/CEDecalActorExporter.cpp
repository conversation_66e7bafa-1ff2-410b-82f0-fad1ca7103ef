#include "ActorExporters/CEDecalActorExporter.h"

#include "Builders/CEConvertBuilder.h"
#include "Engine/DecalActor.h"
#include "Components/DecalComponent.h"
#include "Exchange/ExportWorld.h"

UCEDecalActorExporter::UCEDecalActorExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = ADecalActor::StaticClass();
}

cesdk::cegf::SDKGameObject UCEDecalActorExporter::AddGameObjectFromActor(
    FCEConvertBuilder& InBuilder,
    ::cesdk::cegf::SDKGameWorld InGameWorld,
    ::cesdk::cegf::SDKGameObject InParentGameObject,
    AActor* InExportActor)
{
   return Super::AddGameObjectFromActor(InBuilder, InGameWorld, InParentGameObject, InExportActor);
} 