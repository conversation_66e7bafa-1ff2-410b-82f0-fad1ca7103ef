#include "Exchange/ExportUtils.h"

#include "CELog.h"
#include "StringExchange.h"
#include "Components/SceneComponent.h"
#include "GameFramework/Actor.h"

namespace CEAssetExchange {

FString FExportUtils::GetNodeName(const USceneComponent* Scene)
{
    if (!Scene)
        return FString();
        
    auto Actor = Scene->GetOwner();
    if (Scene != Actor->GetRootComponent())
        return Scene->GetName();   // This will not be duplicated

    auto Name = Actor->GetActorNameOrLabel();   // This may be duplicated but never mind
    return Name;
}

void FExportUtils::ConvertTransform(const FMatrix& UETransform, Transform& OutTransform)
{
    static const FMatrix Rotation{
        {1, 0, 0, 0},
        {0, 0, -1, 0},
        {0, 1, 0, 0},
        {0, 0, 0, 1},
    };
    static const FMatrix RotationInverse = Rotation.Inverse();

    const FTransform CeTransform{RotationInverse * UETransform * Rotation};
    const auto T = CeTransform.GetTranslation();
    const auto R = CeTransform.GetRotation();
    const auto S = CeTransform.GetScale3D();

    OutTransform.mTranslation[0] = T.X;
    OutTransform.mTranslation[1] = T.Y;
    OutTransform.mTranslation[2] = T.Z;

    OutTransform.mQuarternion[0] = R.X;
    OutTransform.mQuarternion[1] = R.Y;
    OutTransform.mQuarternion[2] = R.Z;
    OutTransform.mQuarternion[3] = R.W;

    OutTransform.mScale[0] = S.X;
    OutTransform.mScale[1] = S.Y;
    OutTransform.mScale[2] = S.Z;
}

void FExportUtils::ConvertSceneToTransform(const USceneComponent& Scene, Transform& OutTransform)
{
    const auto RT = Scene.GetRelativeTransform();
    const auto Mat = RT.ToMatrixWithScale();
    ConvertTransform(Mat, OutTransform);
}

float FExportUtils::ComputeVFOVInRad(float HFOVInDeg, float AspectRatio)
{
    float HFOVInRad = HFOVInDeg / 180 * PI;
    float VFOVInRad = 2 * FGenericPlatformMath::Atan(FGenericPlatformMath::Tan(HFOVInRad / 2) / AspectRatio);
    return VFOVInRad;
}

void FExportUtils::ConvertXToZAxisByRotateY(Transform& InOutTransform)
{
    FQuat4f Quat{InOutTransform.mQuarternion[0], InOutTransform.mQuarternion[1], 
                 InOutTransform.mQuarternion[2], InOutTransform.mQuarternion[3]};
    Quat *= FQuat4f{FVector3f::YAxisVector, PI / 2};
    InOutTransform.mQuarternion[0] = Quat.X;
    InOutTransform.mQuarternion[1] = Quat.Y;
    InOutTransform.mQuarternion[2] = Quat.Z;
    InOutTransform.mQuarternion[3] = Quat.W;
}

IEntity* FExportUtils::AddEntity(IWorldAssemble* World, const USceneComponent* Scene, IEntity* Parent)
{
    const FString NodeName = FExportUtils::GetNodeName(Scene);
    CE_Log(TEXT("CEAssetExchange: Add Entity: %s"), *NodeName);

    Transform Transform;
    FExportUtils::ConvertSceneToTransform(*Scene, Transform);

    Hierarchy Hierarchy;
    Hierarchy.mTransform = &Transform;
    Hierarchy.mParent = Parent;

    return World->AddEntity(StringMapper::Instance().FromUEToCEString(*NodeName), &Hierarchy);
}

} // namespace CEAssetExchange 