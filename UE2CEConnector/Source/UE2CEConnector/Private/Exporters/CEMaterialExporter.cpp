#include "Exporters/CEMaterialExporter.h"
#include "Engine/StaticMesh.h"
#include "Exchange/MaterialFxExchange.h"

UCEMaterialExporter::UCEMaterialExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UMaterialInterface::StaticClass();
    FormatExtension.Add(TEXT("nda"));
    FormatDescription.Add(TEXT("CE Material"));
}

bool UCEMaterialExporter::AddObject(FCEConvertBuilder& Builder, UObject* Object)
{
    CEAssetExchange::MaterialFxMapper::Instance().GetMaterialFxExported().Empty();
    return Super::AddObject(Builder, Object);
}
