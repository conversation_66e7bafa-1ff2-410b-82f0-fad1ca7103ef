#pragma once
#include "CoreMinimal.h"
#include "CEBaseComponentExporter.h"
#include "CEShapeComponentExporter.generated.h"


UCLASS()
class UCEShapeComponentExporter : public UCEBaseComponentExporter
{
    GENERATED_UCLASS_BODY()
public:
    virtual cesdk::cegf::SDKGameObject AddGameObjectFromSceneComponent(
        FCEConvertBuilder& InBuilder,
        cesdk::cegf::SDKGameWorld InGameWorld,
        cesdk::cegf::SDKGameObject InParentGameObject,
        USceneComponent* InExportComponent) override;
private:
};