#include "CETask.h"

#include "CELog.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Builders/CEConvertBuilder.h"
#include "HAL/FileManager.h"

static void WriteInt32(FArchive& Archive, uint32 Value)
{
    Archive.SerializeInt(Value, MAX_uint32);
}

static void WriteInt64(FArchive& Archive, uint64 Value)
{
    Archive.Serialize(&Value, sizeof(uint64));
}

void FCETask::WriteNDA(void* data, int64 size, int32 type)
{
    FArchive* Archive = IFileManager::Get().CreateFileWriter(*mSavePath);

    // Write NDA Header
    WriteNDAHeader(Archive, type, size);

    // Write NDA Content
    Archive->Serialize(data, size);
    delete Archive;
}

void FCETask::WriteNDA(const FString& Path, void* Data, int64 DataSize, int32 Type)
{
    FArchive* Archive = IFileManager::Get().CreateFileWriter(*Path);

    // Write NDA Header
    WriteNDAHeader(Archive, Type, DataSize);

    // Write NDA Content
    Archive->Serialize(Data, DataSize);
    delete Archive;
}

void FCETask::WriteNDAHeader(FArchive* Archive, int32 Type, int64 DataSize) 
{
    WriteInt32(*Archive, ASSET_MAGIC_NUMBER);
    WriteInt32(*Archive, NDA_VERSION);
    WriteInt64(*Archive, GUID_H);
    WriteInt64(*Archive, GUID_L);
    WriteInt32(*Archive, Type);
    WriteInt32(*Archive, DataSize);
    WriteInt32(*Archive, NDA_CONTENT_TYPE);
    std::string HeaderStr = "null";
    int32 JsonStringLength = HeaderStr.size();
    WriteInt32(*Archive, JsonStringLength);
    Archive->Serialize(&HeaderStr, JsonStringLength);
}

bool FCETask::CollectDependencyTask()
{
    if (!mExportAsset || !mExportAsset->GetOutermost())
    {
        return false;
    }
    
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");

    TArray<FName> DependencyPackageNames;
    AssetRegistryModule.Get().GetDependencies(mExportAsset->GetOutermost()->GetFName(), DependencyPackageNames, UE::AssetRegistry::EDependencyCategory::All);

    TArray<FName> SoftDependencyPackageNames;
    AssetRegistryModule.Get().GetDependencies(mExportAsset->GetOutermost()->GetFName(), SoftDependencyPackageNames, UE::AssetRegistry::EDependencyCategory::All, UE::AssetRegistry::FDependencyQuery(UE::AssetRegistry::EDependencyQuery::Soft));
    
    DependencyPackageNames.RemoveAll([&SoftDependencyPackageNames](const FName& Name) {
        return SoftDependencyPackageNames.Contains(Name);
    });
    
    mDependencies.Empty();
    for (const FName& PackageName : DependencyPackageNames)
    {
        if (PackageName.IsNone())
        {
            continue;
        }

        FString PackagePath = FPackageName::ExportTextPathToObjectPath(PackageName.ToString());
        if (UObject* DependencyObject = LoadObject<UObject>(nullptr, *PackagePath))
        {
            mDependencies.Add(DependencyObject);
        }
        else
        {
            // texture will create low version with _Low suffix, but we need 
            if (PackagePath.EndsWith(TEXT("_Low")))
            {
                PackagePath = PackagePath.Left(PackagePath.Len() - 4);
                if (UObject* DependencyObject_ = LoadObject<UObject>(nullptr, *PackagePath))
                {
                    mDependencies.Add(DependencyObject_);
                }
                else
                {
                    CE_Log_PopWarn(TEXT("Cannot Find Dependency Package %s."), *PackagePath);
                }
            }
            else
            {
                CE_Log_PopWarn(TEXT("Cannot Find Dependency Package %s."), *PackagePath);
            }
        }
    }

    for (UObject* Object : mDependencies)
    {
        mBuilder.GetOrAdd(Object);
    }

    return true;
}
