#include "CECameraActorExporter.h"
#include "Camera/CameraActor.h"
#include "Camera/CameraComponent.h"
#include "Builders/CEConvertBuilder.h"
#include "Exchange/ExportWorld.h"
#include "Components/DrawFrustumComponent.h"

UCECameraActorExporter::UCECameraActorExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = ACameraActor::StaticClass();
}

UCECameraActorExporter::AddEntityFromActorRetType UCECameraActorExporter::AddEntityFromActor(
    FCEConvertBuilder& InBuilder,
    CEAssetExchange::IWorldAssemble* InWorldAssemble,
    USceneComponent* InSceneComponent,
    CEAssetExchange::IEntity* LevelEntity,
    AActor* InExportActor,
    CEAssetExchange::FAssetExchangeInput* InInput)
{
    UCameraComponent* Camera = Cast<UCameraComponent>(InExportActor->GetComponentByClass(UCameraComponent::StaticClass()));
    return {InBuilder.AddEntityFromSceneComponent(InWorldAssemble, InSceneComponent, LevelEntity, Camera, InInput), false};
}

