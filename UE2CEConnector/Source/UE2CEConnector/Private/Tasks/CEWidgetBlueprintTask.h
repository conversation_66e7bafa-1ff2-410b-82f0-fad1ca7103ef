#pragma once
#include "CETask.h"
#include "Builders/CEConvertBuilder.h"
#include "CEResource.h"
#include "WidgetBlueprint.h"

#define DEBUG_DATA_CHECKED 0

class UWidgetBlueprint;

class FCEWidgetBlueprintTask : public FCETask
{
    using VertexChannelData = std::unique_ptr<CrossSchema::VertexChannelAssetDataT>;

public:
    FCEWidgetBlueprintTask(const FString& savePath, FCEConvertBuilder& builder, const UWidgetBlueprint* widgetBlueprint)
        : FCETask(ECETaskPriority::World, savePath, builder, widgetBlueprint)
        , widgetBlueprint(widgetBlueprint)
    {}

    virtual FString GetName() const  override;
    virtual bool Complete() override;
    virtual bool CollectDependencyTask() override;

private:
    const UWidgetBlueprint* widgetBlueprint;
    TSharedRef<FCEResource> resource = MakeShared<FCEResource>();
    std::vector<uint8> TmpBuffer;
};