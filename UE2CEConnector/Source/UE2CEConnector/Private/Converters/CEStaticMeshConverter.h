#pragma once
#include "Converters/CEBaseConverter.h"
#include "Builders/CEConvertBuilder.h"
#include "CEResource.h"
#include "Engine/EngineTypes.h"
#include "CEStaticMeshConverter.generated.h"

UCLASS()
class UCEStaticMeshConverter final : public UCEBaseConverter
{
public:
    GENERATED_UCLASS_BODY()
protected:
    virtual void Convert(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object) override;
};