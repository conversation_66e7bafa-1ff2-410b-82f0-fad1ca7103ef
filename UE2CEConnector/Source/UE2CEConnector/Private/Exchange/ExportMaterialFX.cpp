#include "CEAssetExchange.h"

#include <array>
#include <utility>

#include <Materials/MaterialExpressionFunctionInput.h>
#include <Materials/MaterialExpressionFunctionOutput.h>
#include <Materials/MaterialExpressionLandscapeLayerBlend.h>
#include <Materials/MaterialExpressionLandscapeLayerWeight.h>
#include <Materials/MaterialExpressionLandscapeLayerCoords.h>
#include "Runtime/Engine/Public/Materials/MaterialExpressionTextureObjectParameter.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionParameter.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionTextureSampleParameter.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionComposite.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionTransform.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionTransformPosition.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionClamp.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionMakeMaterialAttributes.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionSetMaterialAttributes.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionGetMaterialAttributes.h"
#include "Runtime/Engine/Public/Materials/MaterialAttributeDefinitionMap.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionNamedReroute.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionCustom.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionComment.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionStaticSwitchParameter.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionStaticSwitch.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionViewProperty.h"
#include "StringExchange.h"
#include "MaterialFxExchange.h"
#include "AssetExchange/include/SDK.h"
#include "CELog.h"
#include "Materials/MaterialExpressionTextureObject.h"
#include "Editor/UnrealEd/Classes/MaterialGraph/MaterialGraphNode_Composite.h"
#include <Editor/MaterialEditor/Private/MaterialEditor.h>

#include "Builders/CEConvertBuilder.h"
#include "Runtime/Engine/Public/Materials/MaterialExpressionConstant.h"

#include "Materials/MaterialExpressionObjectPositionWS.h"
#include "Materials/MaterialExpressionWorldPosition.h"
#define LOCTEXT_NAMESPACE "CEAssetExchange"
#define SUPPORTED_MATERIAL_ATTRIBUTES 11

namespace {
enum struct EExtensiondNodeType
    {
        CEToUEWorldPosition,
		TangentToWordPosition,
		WorldToTangentPosition,
		FunctionInputTexture,
    };
    
    struct FExtensionNode
    {
        EExtensiondNodeType ExtensionNode{EExtensiondNodeType::CEToUEWorldPosition};
        bool IsBefore{false};
    };

    using FExtensionNodes = TMap<UMaterialExpression*, std::vector<FExtensionNode>>;
}

namespace CEAssetExchange {

template<typename T>
concept SerializableFx = requires { std::is_same_v<T, UMaterial> || std::is_same_v<T, UMaterialFunction>; };

template<typename T>
concept SerializableMaterialFx = requires { SerializableFx<T> || std::is_same_v<T, UMaterialInstance>; };

struct ExportFuncs
{
    static constexpr auto MaterialPropertiesNodeName = TEXT("properties");

    static constexpr auto FxPropertiesNodeName = TEXT("property");
    static constexpr auto DefaultGroupNodeName = TEXT("Default Group");
    static constexpr auto PropertyValuesNodeName = TEXT("Value");
    static constexpr auto PropertyVisibleNodeName = TEXT("Visible");
    static constexpr auto ExpressionsNodeName = TEXT("expression");

    static constexpr auto ExpressionSubComponentPrefix = TEXT("_SubCmpt");
    static constexpr int32 ExpressionSubComponentOffsetX = 100;
    static constexpr int32 ViewPropertyMax = 6;

    /*
     * Handle Surface Shader Pin Value
     */
    template<class MaterialInputType>
    static void ProcessMaterialDefaultValue(
        TSharedRef<FJsonObject>& ExprNode,
        const FString& PinName,
        const MaterialInputType& MaterialInput,
        bool bNeedNormalized = false,
        float NormalizedFraction = 1.f,
        bool bForcedScalarValue = false,
        float ForcedScalarValue = 0.0f
    )
    {
        if constexpr (std::is_same_v<MaterialInputType, FColorMaterialInput>) {
            if (MaterialInput.UseConstant)
            {
                FColor Value = MaterialInput.Constant;
                TSharedPtr<FJsonObject> Float4Json = MakeShared<FJsonObject>();
                Float4Json->SetNumberField("x", bNeedNormalized ? Value.R / NormalizedFraction : Value.R);
                Float4Json->SetNumberField("y", bNeedNormalized ? Value.G / NormalizedFraction : Value.G);
                Float4Json->SetNumberField("z", bNeedNormalized ? Value.B / NormalizedFraction : Value.B);
                Float4Json->SetNumberField("w", bNeedNormalized ? Value.A / NormalizedFraction : Value.A);
                ExprNode->SetObjectField("m_" + PinName + "Constant", Float4Json);
            }
        }
        if constexpr (std::is_same_v<MaterialInputType, FScalarMaterialInput>)
        {
            if (bForcedScalarValue)
            {
                ExprNode->SetNumberField("m_" + PinName + "Constant", ForcedScalarValue);
            }
            else if (MaterialInput.UseConstant)
            {
                float Value = MaterialInput.Constant;
                ExprNode->SetNumberField("m_" + PinName + "Constant", Value);
            }
        }
    }

    struct ExpressionProperty
    {
        std::array<int32, 2> IdxRng;
        const TArray<TSharedPtr<FJsonValue>>* OutputMap;
    };

    using ExpressionProperties = TMap<FString, ExpressionProperty>;

    static FString GetExpressionName(FString SrcName, const MaterialFxMapper::Map& Map = {}, int32 SubId = 0)
    {
        const char* CE = StringMapper::Instance().FromUEToCEString(*SrcName);
        SrcName = StringCast<TCHAR>(CE).Get();

        // If there is a composition, keep the first one unchanged, for receiving inputs from properties
        if (Map.CompositionNum > 1 && SubId > 0)
        {
            SrcName += ExpressionSubComponentPrefix;
            SrcName += FString::FromInt(SubId);
        }
        return SrcName;
    }

    
    static FString GetUniquePropertyNameFromInfo(const FString& Info, EMaterialParameterType ParameterType)
    {
        FString BaseName = Info;

        // 清理非法Shader变量字符（只允许字母、数字、下划线）
        for (int32 i = 0; i < BaseName.Len(); ++i)
        {
            TCHAR& c = BaseName[i];
            if (!FChar::IsAlnum(c) && c != '_')
            {
                c = '_';
            }
        }
        auto GetParameterTypeString = [](EMaterialParameterType Type) {
            switch (Type)
            {
            case EMaterialParameterType::Scalar:
                return TEXT("Scalar");
            case EMaterialParameterType::Vector:
                return TEXT("Vector");
            case EMaterialParameterType::Texture:
                return TEXT("Texture");
            case EMaterialParameterType::Font:
                return TEXT("Font");
            case EMaterialParameterType::StaticSwitch:
                return TEXT("StaticSwitch");
            case EMaterialParameterType::RuntimeVirtualTexture:
                return TEXT("RuntimeVirtualTexture");
            case EMaterialParameterType::DoubleVector:
                return TEXT("DoubleVector");
            default:
                return TEXT("Invalid");
            }
        };
        FString TypeString = GetParameterTypeString(ParameterType);
        return FString::Printf(TEXT("_%s_%s"), *BaseName, *TypeString);
    }
	static int ConvertVertorCoord(TEnumAsByte<enum EMaterialVectorCoordTransformSource> source) {
        int res = 0;
        switch (source)
        {
        case EMaterialVectorCoordTransformSource::TRANSFORMSOURCE_Local:
            res = 1;
            break;
        case EMaterialVectorCoordTransformSource::TRANSFORMSOURCE_Tangent:
            res = 0;
            break;
        case EMaterialVectorCoordTransformSource::TRANSFORMSOURCE_World:
            res = 2;
            break;
        case EMaterialVectorCoordTransformSource::TRANSFORMSOURCE_Camera:
            res = 3;
            break;
        case EMaterialVectorCoordTransformSource::TRANSFORMSOURCE_Instance:
            res = 4;
            break;
        case EMaterialVectorCoordTransformSource::TRANSFORMSOURCE_View:
            res = 3;
            break;
        }
        return res;
    };

    static int ConvertVertorCoordTransform(TEnumAsByte<enum EMaterialVectorCoordTransform> source) {
        int res = 0;
        switch (source)
        {
        case EMaterialVectorCoordTransform::TRANSFORM_Local:
            res = 1;
            break;
        case EMaterialVectorCoordTransform::TRANSFORM_Tangent:
            res = 0;
            break;
        case EMaterialVectorCoordTransform::TRANSFORM_World:
            res = 2;
            break;
        case EMaterialVectorCoordTransform::TRANSFORM_View:
            res = 3;
            break;
        case EMaterialVectorCoordTransform::TRANSFORM_Camera:
            res = 3;
            break;
        case EMaterialVectorCoordTransform::TRANSFORM_Instance:
            res = 4;
            break;
        }
        return res;
    };

	static int ConvertTransformSource(TEnumAsByte<enum EMaterialPositionTransformSource> source) {
        int res = 0;
        switch (source)
        {
        case EMaterialPositionTransformSource::TRANSFORMPOSSOURCE_Local:
            res = 1;
            break;
        case EMaterialPositionTransformSource::TRANSFORMPOSSOURCE_World:
            res = 2;
            break;
        case EMaterialPositionTransformSource::TRANSFORMPOSSOURCE_View:
            res = 3;
            break;
        case EMaterialPositionTransformSource::TRANSFORMPOSSOURCE_Camera:
            res = 3;
            break;
        case EMaterialPositionTransformSource::TRANSFORMPOSSOURCE_Instance:
            res = 4;
            break;
        case EMaterialPositionTransformSource::TRANSFORMPOSSOURCE_Particle:
            res = 4;
            break;
        }
        return res;
    };

    template<typename T>
    static void RecordNodeThatNeedCoordinateConvert(const T& UE, FExtensionNodes& ExtensionNodes)
    {
        for (TObjectPtr ExprPtr : UE.GetExpressions())
        {
            UMaterialExpression* Expression = ExprPtr.Get();
            if (!Expression)
                continue;
            if (MaterialExtensionNodes::Instance().CommonCoordinateConvertNodes.Contains(Expression->GetClass()->GetName()))
            {
                auto& ExtensionNode = ExtensionNodes.FindOrAdd(Expression, std::vector<FExtensionNode>{});
                ExtensionNode.emplace_back(FExtensionNode{EExtensiondNodeType::CEToUEWorldPosition});
            }
			else if (MaterialExtensionNodes::Instance().HasInputCoordinateConvertNodes.Contains(Expression->GetClass()->GetName()))
			{
                // Record the extension node for coordinate conversion
                int sourceType{0};
                int destType{0};
                auto& ExtensionNode = ExtensionNodes.FindOrAdd(Expression, std::vector<FExtensionNode>{});
                if (auto* transptr = Cast<UMaterialExpressionTransform>(Expression); transptr)
                {
                    sourceType = ConvertVertorCoord(transptr->TransformSourceType);
                    destType = ConvertVertorCoordTransform(transptr->TransformType);
                    if (sourceType == destType)
                    {
                        continue;
                    }
					else if (sourceType == 0 /*Tangent*/ && destType != 0)
					{
						ExtensionNode.emplace_back(FExtensionNode{EExtensiondNodeType::TangentToWordPosition});
					}
					else if (sourceType != 0 && destType == 0 /*Tangent*/)
					{
						ExtensionNode.emplace_back(FExtensionNode{EExtensiondNodeType::WorldToTangentPosition});
					}
					else
					{
						ExtensionNode.emplace_back(FExtensionNode{EExtensiondNodeType::CEToUEWorldPosition});
					}
                }
                else if (auto* transposptr = Cast<UMaterialExpressionTransformPosition>(Expression); transposptr)
                {
                    sourceType = ConvertTransformSource(transposptr->TransformSourceType);
                    destType = ConvertTransformSource(transposptr->TransformType);
                    if (sourceType != destType)
                    {
	                    ExtensionNode.emplace_back(FExtensionNode{EExtensiondNodeType::CEToUEWorldPosition});
                    }
                }
			}
            else if (auto* FuncInputExpr = Cast<UMaterialExpressionFunctionInput>(Expression); FuncInputExpr)
            {
                if (FuncInputExpr->InputType == EFunctionInputType::FunctionInput_Texture2D && !FuncInputExpr->GetInput(0)->Expression)
                {
                    auto& ExtensionNode = ExtensionNodes.FindOrAdd(Expression, std::vector<FExtensionNode>{});
                    ExtensionNode.emplace_back(FExtensionNode{EExtensiondNodeType::FunctionInputTexture});
                }
            }
        }
    }

    template<typename T>
        requires std::is_same_v<T, UMaterial> || std::is_same_v<T, UMaterialInstance>
    static void ExportProperties(FCEConvertBuilder& Builder, const T& UE,  FAssetExchangeInput* Input, TSharedPtr<FJsonObject> OutJson, IFxAssemble* CE)
    {
        auto PropsNode = MakeShared<FJsonObject>();

        auto SetVector = [&]<typename VecTy>(const VecTy& Vec, TArray<TSharedPtr<FJsonValue>>& OutJson) {
            for (int32 i = 0; i < sizeof(VecTy) / sizeof(Vec.Component(0)); ++i)
                OutJson.Push(MakeShared<FJsonValueNumber>(Vec.Component(i)));
        };
        auto TestTypeThenSet = [&](EMaterialParameterType Type, const TPair<FMaterialParameterInfo, FMaterialParameterMetadata>& PairParam) {
            const auto& [Info, Meta] = PairParam;
            auto InfoName = GetExpressionName(Info.Name.ToString());
			InfoName = GetUniquePropertyNameFromInfo(InfoName, Type);
            auto SetProperty = [&]<typename ValTy>(const ValTy& Val) {
                if constexpr (std::is_same_v<T, UMaterial>)
                {
                    auto PropNode = MakeShared<FJsonObject>();
                    if constexpr (std::is_same_v<ValTy, TArray<TSharedPtr<FJsonValue>>>)
                    {
                        PropNode->SetArrayField(PropertyValuesNodeName, Val);
                    }
                    else if constexpr (std::is_same_v<ValTy, bool>)
                    {
                        PropNode->SetBoolField(PropertyValuesNodeName, Val);
                    }
                    else   // const char *
                    {
                        PropNode->SetStringField(PropertyValuesNodeName, StringCast<TCHAR>(Val).Get());
                    }
                    PropNode->SetBoolField(PropertyVisibleNodeName, true);
                    PropsNode->SetObjectField(InfoName, PropNode);
                }
                else   // UMaterialInstance
                {
                    if constexpr (std::is_same_v<ValTy, TArray<TSharedPtr<FJsonValue>>>)
                    {
                        PropsNode->SetArrayField(InfoName, Val);
                    }
                    else if constexpr (std::is_same_v<ValTy, bool>)
                    {
                        PropsNode->SetBoolField(InfoName, Val);
                    }
                    else   // const char *
                    {
                        PropsNode->SetStringField(InfoName, StringCast<TCHAR>(Val).Get());
                    }
                }
            };

            switch (Type)
            {
            case EMaterialParameterType::Scalar:
            {
                TArray<TSharedPtr<FJsonValue>> PropValuesNode;
                PropValuesNode.Push(MakeShared<FJsonValueNumber>(Meta.Value.AsScalar()));
                SetProperty(PropValuesNode);
            }
            break;
            case EMaterialParameterType::StaticSwitch:
            {
                SetProperty(Meta.Value.AsStaticSwitch());
            }
            break;
            case EMaterialParameterType::Vector:
            case EMaterialParameterType::DoubleVector:
            {
                TArray<TSharedPtr<FJsonValue>> PropValuesNode;
                if (Type == EMaterialParameterType::Vector)
                    SetVector(Meta.Value.AsLinearColor(), PropValuesNode);
                else
                    SetVector(Meta.Value.AsVector4d(), PropValuesNode);

                SetProperty(PropValuesNode);
            }
            break;
            case EMaterialParameterType::Texture:
            {
                auto Tex2D = Cast<UTexture2D>(Meta.Value.AsTextureObject());
                FString TextureRPath = Builder.GetCEContentPath(Tex2D);
                if (Tex2D)
                {
                    FAssetExchangeInput TextureInput{Input->Options, Input->Sdk, StringMapper::Instance().FromUEToCEString(*TextureRPath)};

                    SetProperty(TextureInput.RelativePath);
                    CE->AddDependencies(TextureInput.RelativePath);
                    CE_Log(TEXT("CEAssetExchange: Material set Texture parameter \"%s\" = \"%s\""), *InfoName, *TextureRPath);
                }
                else
                {
                    CE_Log(TEXT("CEAssetExchange: Material get Texture parameter \"%s\" = \"%s\", which is NOT supported"), *InfoName, *TextureRPath);
                }
            }
            break;
            }
        };
        
        TMap<FMaterialParameterInfo, FMaterialParameterMetadata> Parameters;
        for (int32 TyIdx = 0; TyIdx < static_cast<int>(EMaterialParameterType::Num); ++TyIdx)
        {
            Parameters.Empty();

            auto Type = static_cast<EMaterialParameterType>(TyIdx);
            UE.GetAllParametersOfType(Type, Parameters);
            for (const auto& Pair : Parameters)
            {
                if (std::is_same_v<T, UMaterialInstance> && !Pair.Value.bOverride)
                    continue;
                TestTypeThenSet(Type, Pair);
            }
        }

        if constexpr (std::is_same_v<T, UMaterialInstance>)
        {
            OutJson->SetObjectField(MaterialPropertiesNodeName, PropsNode);
        }
        else
        {
            auto GroupNode = MakeShared<FJsonObject>();
            OutJson->SetObjectField(FxPropertiesNodeName, GroupNode);
            GroupNode->SetObjectField(DefaultGroupNodeName, PropsNode);
        }
    }

	static void ProcessTransformNode(const UMaterialExpression* ExprPtr, TSharedPtr<FJsonObject> ExprNode, FExtensionNodes& ExtensionNodes)
	{
        if (auto* transptr = Cast<UMaterialExpressionTransform>(ExprPtr); transptr)
		{
            ExprNode->SetNumberField(TEXT("m_Source"), ConvertVertorCoord(transptr->TransformSourceType));
            ExprNode->SetNumberField(TEXT("m_Destination"), ConvertVertorCoordTransform(transptr->TransformType));
		}
		else if (auto* transposptr = Cast<UMaterialExpressionTransformPosition>(ExprPtr); transposptr)
        {
            ExprNode->SetNumberField(TEXT("m_Source"), ConvertTransformSource(transposptr->TransformSourceType));
            ExprNode->SetNumberField(TEXT("m_Destination"), ConvertTransformSource(transposptr->TransformType));
            ExprNode->SetNumberField(TEXT("m_ElementType"), 2);
		}
	}

    // Helper function to set default node properties
    static void SetDefaultNodeProperties(const UMaterialExpression* ExprPtr, const MaterialFxMapper::Map& Map, int32 SubId, TSharedPtr<FJsonObject> ExprNode)
    {
        FString ExprName = GetExpressionName(ExprPtr->GetName(), Map, SubId);
        ExprNode->SetStringField(TEXT("m_Name"), std::move(ExprName));
    }

    // Helper function to set default parameter node properties
    static void SetDefaultParameterNodeProperties(const UMaterialExpression* ExprPtr, auto* ParameterPtr, const MaterialFxMapper::Map& Map, int32 SubId, TSharedPtr<FJsonObject> ExprNode)
    {
        FString ExprName = GetExpressionName(ExprPtr->GetName(), Map, SubId);
        FString ParameterName = GetExpressionName(ParameterPtr->ParameterName.ToString(), Map, SubId);
        FString UniqueParameterName = GetUniquePropertyNameFromInfo(ParameterName, ParameterPtr->GetParameterType());
        ExprNode->SetStringField(TEXT("m_ParameterName"), std::move(UniqueParameterName));
        ExprNode->SetStringField(TEXT("m_Name"), std::move(ParameterName));
    }

    // Helper function to set texture node and dependency
    static void SetTextureNodeAndDependency(FCEConvertBuilder& Builder, auto* TexturePtr,  FAssetExchangeInput* Input, IFxAssemble* CE, TSharedPtr<FJsonObject> ExprNode, FString TextureFieldName, FString TextureTypeName)
    {
        auto Tex2D = TexturePtr->Texture;
        FString TextureRPath = Builder.GetCEContentPath(Tex2D);
        if (TextureRPath == "")
        {
            CE_Log(TEXT("Error\"%s\""), *TexturePtr->GetName());
        }
        if (Tex2D)
        {
            FAssetExchangeInput TextureInput{ Input->Options, Input->Sdk, StringMapper::Instance().FromUEToCEString(*TextureRPath) };

            CE->AddDependencies(TextureInput.RelativePath);
            CE_Log(TEXT("CEAssetExchange: Material set Texture \"%s\""), *TextureRPath);
            ExprNode->SetStringField(std::move(TextureFieldName), TextureInput.RelativePath);
            ExprNode->SetNumberField(std::move(TextureTypeName), Tex2D->CompressionSettings == TextureCompressionSettings::TC_Normalmap ? 32 : 16);
        }
        else
        {
            CE_Log(TEXT("CEAssetExchange: Material get Texture \"%s\", which is NOT supported"), *TextureRPath);
        }
    }

    // Helper function to process material function expression
    template<SerializableFx T>
    static void ProcessMaterialFunctionExpression(FCEConvertBuilder& Builder, const UMaterialExpression* ExprPtr, const MaterialFxMapper::Map& Map, const FAssetExchangeInput* Input, IFxAssemble* CE, TSharedPtr<FJsonObject> ExprNode)
    {
        check(Map.CompositionNum == 1);

        auto* Ptr = Cast<UMaterialExpressionMaterialFunctionCall>(ExprPtr);
        if (auto* FuncPtr = Cast<UMaterialFunction>(Ptr->MaterialFunction.Get()); FuncPtr)
        {
            FString MFRPath = Builder.GetCEContentPath(FuncPtr);
            FAssetExchangeInput MFInput{ Input->Options, Input->Sdk, StringMapper::Instance().FromUEToCEString(*MFRPath) };
            if (auto CEMaterialFuncPath = MaterialFxMapper::Instance().MaterialFunctionInCE(MFInput.RelativePath))
            {
                auto CEPath = StringMapper::Instance().FromUEToCEString(GetData(*CEMaterialFuncPath));
                CE->AddDependencies(CEPath);
                ExprNode->SetStringField(TEXT("m_MaterialFunction"), StringCast<TCHAR>(CEPath).Get());
            }
            else if (ExportFxOrMaterialFunction(Builder, *FuncPtr, &MFInput))
            {
                CE->AddDependencies(MFInput.RelativePath);
                ExprNode->SetStringField(TEXT("m_MaterialFunction"), StringCast<TCHAR>(MFInput.RelativePath).Get());
            }
        }
    }

    // Helper function to process function input/output expressions
    template<SerializableFx T>
    static void ProcessFunctionInOutExpressions(
        const UMaterialExpression* ExprPtr,
        const TArray<FFunctionExpressionInput>& FuncInputs, 
        const TArray<FFunctionExpressionOutput>& FuncOutputs,
        TSharedPtr<FJsonObject> ExprNode,
        FExtensionNodes& ExtensionNodes,
        TMap<UMaterialExpression*, int32> const& FuncInputPriority,
        TMap<UMaterialExpression*, int32> const& FuncOuputPriority
    )
    {
        if constexpr (std::is_same_v<T, UMaterialFunction>)
        {
            int32 FuncInoutId;
            if (auto* Ptr = Cast<UMaterialExpressionFunctionInput>(ExprPtr); Ptr)
            {
                for (FuncInoutId = 0; FuncInoutId < FuncInputs.Num(); ++FuncInoutId)
                    if (FuncInputs[FuncInoutId].ExpressionInput == Ptr)
                        break;
                check(FuncInoutId != FuncInputs.Num());

                ExprNode->SetNumberField(TEXT("m_SortPriority"), FuncInputPriority[FuncInputs[FuncInoutId].ExpressionInput]);
                if (FuncInputs[FuncInoutId].ExpressionInput->InputType == EFunctionInputType::FunctionInput_Texture2D)
                {
                    ExprNode->SetBoolField(TEXT("m_IsUETexture2DInput"), true);
                    if (!FuncInputs[FuncInoutId].ExpressionInput->GetInput(0)->Expression)
					{
                        ExprNode->SetNumberField(TEXT("m_InputType"), 4);
					}
					else if (auto* TextureSample = Cast<UMaterialExpressionTextureSample>(FuncInputs[FuncInoutId].ExpressionInput->GetInput(0)->Expression))
                    {
                        if (TextureSample->Texture && TextureSample->Texture->IsNormalMap())
                        {
                            ExprNode->SetNumberField(TEXT("m_InputType"), 5);
                        }
                        else
                        {
                            ExprNode->SetNumberField(TEXT("m_InputType"), 4);
                        }
                    }
                    else if (auto* TextureObj = Cast<UMaterialExpressionTextureBase>(FuncInputs[FuncInoutId].ExpressionInput->GetInput(0)->Expression))
                    {
                        if (TextureObj->Texture && TextureObj->Texture->IsNormalMap())
                        {
                            ExprNode->SetNumberField(TEXT("m_InputType"), 5);
                        }
                        else
                        {
                            ExprNode->SetNumberField(TEXT("m_InputType"), 4);
                        }
                    }
                }
                else if (FuncInputs[FuncInoutId].ExpressionInput->InputType == EFunctionInputType::FunctionInput_StaticBool)
                {
                    ExprNode->SetNumberField(TEXT("m_InputType"), 6);
                }
                else if (FuncInputs[FuncInoutId].ExpressionInput->InputType == EFunctionInputType::FunctionInput_MaterialAttributes)
                {
                    ExprNode->SetNumberField(TEXT("m_InputType"), 7);
                }
                else
                {
                    ExprNode->SetNumberField(TEXT("m_InputType"), FuncInputs[FuncInoutId].ExpressionInput->InputType);
                }
                ExprNode->SetStringField(TEXT("m_InputName"), FuncInputs[FuncInoutId].Input.InputName.ToString());
                if (FuncInputs[FuncInoutId].Input.InputName.ToString() == "VectorToTransform")
                {
                    CE_Log(TEXT("CEAssetExchange"));
                }
                ExprNode->SetBoolField(TEXT("m_UsePreviewAsDefault"), Ptr->bUsePreviewValueAsDefault);
                ExprNode->SetNumberField(TEXT("m_FuncInoutId"), FuncInoutId);
            }
            if (auto* Ptr = Cast<UMaterialExpressionFunctionOutput>(ExprPtr); Ptr)
            {
                for (FuncInoutId = 0; FuncInoutId < FuncOutputs.Num(); ++FuncInoutId)
                    if (FuncOutputs[FuncInoutId].ExpressionOutput == Ptr)
                        break;
                check(FuncInoutId != FuncOutputs.Num());

                ExprNode->SetNumberField(TEXT("m_SortPriority"), FuncOuputPriority[FuncOutputs[FuncInoutId].ExpressionOutput]);
                ExprNode->SetStringField(TEXT("m_OutputName"), FuncOutputs[FuncInoutId].Output.OutputName.ToString());
                ExprNode->SetNumberField(TEXT("m_FuncInoutId"), FuncInoutId);
            }
        }
    }

    // Helper function to export a single expression
    template<SerializableFx T>
    static void ExportSingleExpression(
        FCEConvertBuilder& Builder,
        const UMaterialExpression* ExprPtr,
        const MaterialFxMapper::Map& Map,
        int32 SubId,
        int32 Id, 
        FAssetExchangeInput* Input, IFxAssemble* CE, 
        const TArray<FFunctionExpressionInput>& FuncInputs, 
        const TArray<FFunctionExpressionOutput>& FuncOutputs,
        TArray<TSharedPtr<FJsonValue>>& ExprsNode,
		FExtensionNodes & ExtensionNodes,
        TMap<UMaterialExpression*, int32> const& FuncInputPriority,
        TMap<UMaterialExpression*, int32> const& FuncOuputPriority
    )
    {
        auto ExprNode = MakeShared<FJsonObject>();

        ExprNode->SetNumberField(TEXT("m_Id"), Id);
        if (ExprPtr->Material)
        {
            if (!ExprPtr->GetDescription().IsEmpty())
            {
                ExprNode->SetStringField(TEXT("m_Description"), ExprPtr->GetDescription());
            }
            else
            {
                ExprNode->SetStringField(TEXT("m_Description"), TEXT(" "));
            }
        }

        ExprsNode.Push(MakeShared<FJsonValueObject>(ExprNode));

        if (!Map.IsValid())
        {
            ExprNode->SetStringField(TEXT("Class"), FString(TEXT("cross::MaterialExpressionCustom")));
			if (std::is_same_v<T, UMaterial>)
			{
				CE_Log_PopWarn(TEXT("MaterialNode is Missing in CE:\"%s\"; Material is: \"%s\" "), *ExprPtr->GetName(), *ExprPtr->Material.GetName())
			}
			else
			{
                CE_Log_PopWarn(TEXT("MaterialNode is Missing in CE:\"%s\"; Material is: \"%s\" "), *ExprPtr->GetName(), *ExprPtr->Function.GetName())
			}
        }
        else if (Map.CompositionNum > 1)
            ExprNode->SetStringField(TEXT("Class"), FString(TEXT("cross::")) + Map.GetClassNameCE(SubId));
        else
            ExprNode->SetStringField(TEXT("Class"), FString(TEXT("cross::")) + Map.GetClassNameCE());

        ExprNode->SetNumberField(TEXT("m_EditorPositionX"), ExprPtr->MaterialExpressionEditorX + SubId * ExpressionSubComponentOffsetX);
        ExprNode->SetNumberField(TEXT("m_EditorPositionY"), ExprPtr->MaterialExpressionEditorY);

        ProcessTransformNode(ExprPtr, ExprNode, ExtensionNodes);

        auto SetTextureSamplerState = [&](auto* SamplePtr) {
            auto SamplerNode = MakeShared<FJsonObject>();
            ExprNode->SetObjectField("m_SamplerState", SamplerNode);

            SamplerNode->SetNumberField("MipValueMode", SamplePtr->MipValueMode);
            SamplerNode->SetNumberField("Filter", 3); // default to anistropic
            SamplerNode->SetNumberField("AnisotropicLevel", 8); // default to 8
            SamplerNode->SetNumberField("AddressMode", 0); // default to Warp
        };
        
        if (auto* ParamPtr = Cast<UMaterialExpressionParameter>(ExprPtr); ParamPtr)
        {
            SetDefaultParameterNodeProperties(ExprPtr, ParamPtr, Map, SubId, ExprNode);
        }
        else if (auto* TexSampleParamPtr = Cast<UMaterialExpressionTextureSampleParameter>(ExprPtr); TexSampleParamPtr)
        {
            // contain UMaterialExpressionTextureSampleParameter and UMaterialExpressionTextureObjectParameter here
            SetDefaultParameterNodeProperties(ExprPtr, TexSampleParamPtr, Map, SubId, ExprNode);
            SetTextureSamplerState(TexSampleParamPtr);

            auto Tex2D = TexSampleParamPtr->Texture;
            if (Tex2D)
            {
                ExprNode->SetNumberField(TEXT("m_TextureType"), Tex2D->CompressionSettings == TextureCompressionSettings::TC_Normalmap ? 32 : 16);
                SetTextureNodeAndDependency(Builder, TexSampleParamPtr, Input, CE, ExprNode, TEXT("m_TextureString"), TEXT("m_TextureType"));
            }
        }
        else if (auto* TexSamplePtr = Cast<UMaterialExpressionTextureSample>(ExprPtr); TexSamplePtr)
        {
            SetDefaultNodeProperties(ExprPtr, Map, SubId, ExprNode);
            SetTextureSamplerState(TexSamplePtr);
            SetTextureNodeAndDependency(Builder, TexSamplePtr, Input, CE, ExprNode, TEXT("m_DefaultTexture"), TEXT("m_DefaultTextureType"));
        }
        else if (auto* TexObjectPtr = Cast<UMaterialExpressionTextureObject>(ExprPtr); TexObjectPtr)
        {
            SetDefaultNodeProperties(ExprPtr, Map, SubId, ExprNode);

            FString ExprName = GetExpressionName(ExprPtr->GetName(), Map, SubId);
            ExprNode->SetStringField(TEXT("m_TextureObjectName"), ExprName);

            SetTextureNodeAndDependency(Builder, TexObjectPtr, Input, CE, ExprNode, TEXT("m_TextureString"), TEXT("m_TextureType"));
        }
        else if (auto* DeclarationObjectPtr = Cast<UMaterialExpressionNamedRerouteDeclaration>(ExprPtr); DeclarationObjectPtr)
		{
            ExprNode->SetStringField(TEXT("m_Name"), DeclarationObjectPtr->GetEditableName());
            ExprNode->SetStringField(TEXT("m_VariableGuid"), DeclarationObjectPtr->VariableGuid.ToString());
		}
        else if (auto* UsageObjectPtr = Cast<UMaterialExpressionNamedRerouteUsage>(ExprPtr); UsageObjectPtr)
        {
            ExprNode->SetStringField(TEXT("m_DeclarationGuid"), UsageObjectPtr->DeclarationGuid.ToString());
        }
		else if (auto* ViewPropertyPtr = Cast<UMaterialExpressionViewProperty>(ExprPtr); ViewPropertyPtr)
		{
            switch (ViewPropertyPtr->Property)
            {
				case EMaterialExposedViewProperty::MEVP_WorldSpaceCameraPosition:
				case EMaterialExposedViewProperty::MEVP_WorldSpaceViewPosition:
					ExprNode->SetNumberField(TEXT("m_Property"), 0);
                    break;
                case EMaterialExposedViewProperty::MEVP_TanHalfFieldOfView:
					ExprNode->SetNumberField(TEXT("m_Property"), 3);
                    break;
                case EMaterialExposedViewProperty::MEVP_ViewSize:
					ExprNode->SetNumberField(TEXT("m_Property"), 4);
                    break;
                case EMaterialExposedViewProperty::MEVP_ViewportOffset:
                    ExprNode->SetNumberField(TEXT("m_Property"), 5);
                    break;
                default:
                    if (std::is_same_v<T, UMaterial>)
                    {
						CE_Log_PopWarn(TEXT("ViewProperty is Missing in CE:\"%s\"; Material is: \"%s\" "), *ExprPtr->GetName(), *ExprPtr->Material.GetName())
                    }
                    else
                    {
                        CE_Log_PopWarn(TEXT("ViewProperty is Missing in CE:\"%s\"; Material is: \"%s\" "), *ExprPtr->GetName(), *ExprPtr->Function.GetName())
                    }
					ExprNode->SetNumberField(TEXT("m_Property"), ViewPropertyMax);
                    break;
			}
		}
        else
        {
            FString ExprName = GetExpressionName(ExprPtr->GetName(), Map, SubId);
            ExprNode->SetStringField(TEXT("m_Name"), ExprName);
        }

        if (auto* Ptr = Cast<UMaterialExpressionMaterialFunctionCall>(ExprPtr); Ptr)
        {
            ProcessMaterialFunctionExpression<T>(Builder, ExprPtr, Map, Input, CE, ExprNode);
        }

        ProcessFunctionInOutExpressions<T>(ExprPtr, FuncInputs, FuncOutputs, ExprNode, ExtensionNodes, FuncInputPriority, FuncOuputPriority);

        Map.SetExtraDataForNode(ExprNode, SubId);
    }

    // Helper function to add shader nodes for material
    static void AddShaderNodes(TArray<TSharedPtr<FJsonValue>>& ExprsNode, const UMaterial& UE, const UMaterialEditorOnlyData* EditorOnlyData = nullptr, bool IsSky = false)
    {
        // Temporarily add a UE2CE Converter
        auto ExprNode = MakeShared<FJsonObject>();

		ExprNode->SetNumberField(TEXT("m_Id"), ExprsNode.Num());
        ExprNode->SetStringField(TEXT("m_Description"), TEXT("UE2CE Coordinate converter"));
        ExprNode->SetStringField(TEXT("Class"), FString(TEXT("cross::MaterialExpressionCEToUEWorldPosition")));
        ExprNode->SetNumberField(TEXT("m_EditorPositionX"), UE.EditorX- 100);
        ExprNode->SetNumberField(TEXT("m_EditorPositionY"), UE.EditorY - 100);
        ExprNode->SetBoolField(TEXT("m_CEToUE"), false);
        ExprsNode.Push(MakeShared<FJsonValueObject>(ExprNode));

        // Temporarily add a Vertex Shader
		ExprNode = MakeShared<FJsonObject>();
        ExprNode->SetNumberField(TEXT("m_Id"), ExprsNode.Num());
        ExprNode->SetStringField(TEXT("m_Description"), TEXT("Vertex Shader"));
        ExprNode->SetStringField(TEXT("Class"), FString(TEXT("cross::MaterialExpressionVertexShader")));
        ExprNode->SetNumberField(TEXT("m_EditorPositionX"), UE.EditorX + 100);
        ExprNode->SetNumberField(TEXT("m_EditorPositionY"), UE.EditorY - 100);
        ExprsNode.Push(MakeShared<FJsonValueObject>(ExprNode));

        // Temporarily add a Surface Shader, and it must be the last expression
        ExprNode = MakeShared<FJsonObject>();
        ExprNode->SetNumberField(TEXT("m_Id"), ExprsNode.Num());
        ExprNode->SetStringField(TEXT("m_Description"), TEXT("Surface Shader"));
        ExprNode->SetStringField(TEXT("Class"), FString(TEXT("cross::MaterialExpressionSurfaceShader")));
        ExprNode->SetNumberField(TEXT("m_EditorPositionX"), UE.EditorX);
        ExprNode->SetNumberField(TEXT("m_EditorPositionY"), UE.EditorY);

        // Set Pin Binded Value
        if (EditorOnlyData)
        {
            ProcessMaterialDefaultValue<FColorMaterialInput>(ExprNode, "BaseColor", EditorOnlyData->BaseColor, true, 255.f);
            ProcessMaterialDefaultValue<FScalarMaterialInput>(ExprNode, "Metallic", EditorOnlyData->Metallic);
            ProcessMaterialDefaultValue<FScalarMaterialInput>(ExprNode, "Specular", EditorOnlyData->Specular);
            ProcessMaterialDefaultValue<FScalarMaterialInput>(ExprNode, "Roughness", EditorOnlyData->Roughness);
            ProcessMaterialDefaultValue<FScalarMaterialInput>(ExprNode, "Opacity", EditorOnlyData->Opacity, false, 0.0, IsSky, 1.0f);
        }

        ExprsNode.Push(MakeShared<FJsonValueObject>(ExprNode));
    }

	template<SerializableFx T>
    static void AddNodeProperties(const T& UE, const ExpressionProperties& ExprProp, TSharedPtr<FJsonObject> OutJson)
    {
        auto ExprsNode = OutJson->GetArrayField(ExpressionsNodeName);
        for (TObjectPtr ExprPtr : UE.GetExpressions())
        {
			auto Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName());
            auto IdxRng = ExprProp[ExprPtr->GetName()].IdxRng;
            UMaterialExpression* Expression = ExprPtr.Get();

            if (auto ExpressionGetAttributes = Cast<UMaterialExpressionGetMaterialAttributes>(Expression); ExpressionGetAttributes)
            {
                TArray<TSharedPtr<FJsonValue>> AttributesNode;
                const int32 NumOutputPins = ExpressionGetAttributes->AttributeGetTypes.Num();
                for (int32 i = 0; i < NumOutputPins; ++i)
                {
                    FString attributeName = FMaterialAttributeDefinitionMap::GetAttributeName(ExpressionGetAttributes->AttributeGetTypes[i]);
                    FString outputName = Map.MapKey(attributeName);
                    outputName = StringMapper::Instance().FromUEToCEString(*outputName);
                    int attributeType = FCString::Atoi(*outputName);
                    if (attributeType == 0)
                        continue;
                    if (attributeType > SUPPORTED_MATERIAL_ATTRIBUTES)
                    {
                        CE_Log_PopWarn(TEXT("MaterialAttributes is Missing in CE:\"%s\"; Material is: \"%s\" "), *attributeName, *UE.GetName())
                    }
                    auto AttributeNode = MakeShared<FJsonObject>();
                    AttributesNode.Push(MakeShared<FJsonValueObject>(AttributeNode));
                    AttributeNode->SetNumberField(TEXT("Attribute"), attributeType);
                }
                ExprsNode[IdxRng[0]]->AsObject()->SetArrayField(TEXT("m_AttributesOutputs"), AttributesNode);
            }
            else if (auto ExpressionRerouteDeclaration = Cast<UMaterialExpressionNamedRerouteDeclaration>(Expression); ExpressionRerouteDeclaration)
            {
                auto ColorNode = MakeShared<FJsonObject>();
                ColorNode->SetNumberField(TEXT("x"), ExpressionRerouteDeclaration->NodeColor.R);
                ColorNode->SetNumberField(TEXT("y"), ExpressionRerouteDeclaration->NodeColor.G);
                ColorNode->SetNumberField(TEXT("z"), ExpressionRerouteDeclaration->NodeColor.B);
                ColorNode->SetNumberField(TEXT("w"), ExpressionRerouteDeclaration->NodeColor.A);
                ExprsNode[IdxRng[0]]->AsObject()->SetObjectField(TEXT("m_NodeColor"), ColorNode);
            }
        }
        for (TObjectPtr ExprPtr : UE.GetEditorComments())
        {
			UMaterialExpressionComment* ExpressionComment = ExprPtr.Get();
            auto IdxRng = ExprProp[ExprPtr->GetName()].IdxRng;
            ExprsNode[IdxRng[0]]->AsObject()->SetNumberField(TEXT("m_Width"), ExpressionComment->SizeX);
            ExprsNode[IdxRng[0]]->AsObject()->SetNumberField(TEXT("m_Height"), ExpressionComment->SizeY);
            ExprsNode[IdxRng[0]]->AsObject()->SetStringField(TEXT("m_CommentTitle"), ExpressionComment->Text);
            auto ColorNode = MakeShared<FJsonObject>();
            ColorNode->SetNumberField(TEXT("x"), ExpressionComment->CommentColor.R);
            ColorNode->SetNumberField(TEXT("y"), ExpressionComment->CommentColor.G);
            ColorNode->SetNumberField(TEXT("z"), ExpressionComment->CommentColor.B);
            ColorNode->SetNumberField(TEXT("w"), ExpressionComment->CommentColor.A);
            ExprsNode[IdxRng[0]]->AsObject()->SetObjectField(TEXT("m_Color"), ColorNode);
        }
    }

    template<SerializableFx T>
    static void ExportExpressions(FCEConvertBuilder& Builder, const T& UE, FAssetExchangeInput* Input, ExpressionProperties& ExprProp, TSharedPtr<FJsonObject> OutJson, IFxAssemble* CE, FExtensionNodes& ExtensionNodes)
    {
        int32 Id = 0;
        TArray<TSharedPtr<FJsonValue>> ExprsNode;

        TArray<FFunctionExpressionInput> FuncInputs;
        TArray<FFunctionExpressionOutput> FuncOutputs;
        TMap<UMaterialExpression*, int32> FuncInputPriority;
        TMap<UMaterialExpression*, int32> FuncOutputPriority;
        if constexpr (std::is_same_v<T, UMaterialFunction>)
        {
            UE.GetInputsAndOutputs(FuncInputs, FuncOutputs);

            int32 SortPriorityOffset = 0;
            int32 N = 1000;
            for (auto& Input_ : FuncInputs)
            {
                //Input_.ExpressionInput->SortPriority = Input_.ExpressionInput->SortPriority * N + SortPriorityOffset++;
                FuncInputPriority.Emplace(Input_.ExpressionInput, Input_.ExpressionInput->SortPriority * N + SortPriorityOffset++);
            }
            SortPriorityOffset = 0;
            for (auto& Output : FuncOutputs)
            {
                //Output.ExpressionOutput->SortPriority = Output.ExpressionOutput->SortPriority * N + SortPriorityOffset++;
                FuncOutputPriority.Emplace(Output.ExpressionOutput, Output.ExpressionOutput->SortPriority * N + SortPriorityOffset++);
            }
        }
        
        for (TObjectPtr<UMaterialExpression> ExprPtr : UE.GetExpressions())
        {
            auto Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName());
            std::array<int32, 2> IdRng{Id};

            auto ExtensionNode = ExtensionNodes.Find(ExprPtr.Get());
            if (ExtensionNode)
            {
                for (auto& Node : *ExtensionNode)
                {
                    if (Node.ExtensionNode == EExtensiondNodeType::CEToUEWorldPosition)
                    {
                        Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName() + TEXT("CoordinateConvertExtension"));
                    }
					else if (Node.ExtensionNode == EExtensiondNodeType::TangentToWordPosition)
					{
                        Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName() + TEXT("TangentToWSExtension"));
					}
                    else if (Node.ExtensionNode == EExtensiondNodeType::WorldToTangentPosition)
                    {
                        Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName() + TEXT("WSToTangentExtension"));
                    }
					else if (Node.ExtensionNode == EExtensiondNodeType::FunctionInputTexture)
					{
                        Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName() + TEXT("TexturePreview"));
					}
                }
            }

            for (int32 SubId = 0; SubId < Map.CompositionNum; ++SubId)
            {
                ExportSingleExpression<T>(Builder, ExprPtr.Get(), Map, SubId, Id, Input, CE, FuncInputs, FuncOutputs, ExprsNode, ExtensionNodes, FuncInputPriority, FuncOutputPriority);
                ++Id;
            }

            IdRng[1] = Id - 1;
            ExprProp.Emplace(ExprPtr->GetName(), {IdRng, Map.OutputMapNode});

			// static switch parameter
			if (auto ExpressionSwitchParameter = Cast<UMaterialExpressionStaticSwitchParameter>(ExprPtr))
			{
                ExprsNode[IdRng[0]]->AsObject()->SetBoolField(TEXT("m_Value"), ExpressionSwitchParameter->DefaultValue);
                FString UniqueParameterName = GetUniquePropertyNameFromInfo(ExpressionSwitchParameter->ParameterName.ToString(), ExpressionSwitchParameter->GetParameterType());
                ExprsNode[IdRng[0]]->AsObject()->SetStringField(TEXT("m_ParameterName"), std::move(UniqueParameterName));
                ExprsNode[IdRng[0]]->AsObject()->SetStringField(TEXT("m_Name"), std::move(ExpressionSwitchParameter->ParameterName.ToString()));
			}
            else if (auto ExpressionSwitch = Cast<UMaterialExpressionStaticSwitch>(ExprPtr))
            {
                ExprsNode[IdRng[0]]->AsObject()->SetBoolField(TEXT("m_Value"), ExpressionSwitch->DefaultValue);
            }
        }

        for (TObjectPtr ExprPtr : UE.GetEditorComments())
        {
            auto Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName());
            std::array<int32, 2> IdRng{Id};

            for (int32 SubId = 0; SubId < Map.CompositionNum; ++SubId)
            {
                ExportSingleExpression<T>(Builder, ExprPtr.Get(), Map, SubId, Id, Input, CE, FuncInputs, FuncOutputs, ExprsNode, ExtensionNodes, FuncInputPriority, FuncOutputPriority);
                ++Id;
            }

            IdRng[1] = Id - 1;
            ExprProp.Emplace(ExprPtr->GetName(), {IdRng, Map.OutputMapNode});
        }

        if constexpr (std::is_same_v<T, UMaterial>)
        {
            const UMaterialEditorOnlyData* EditorOnlyData = std::is_same_v<T, UMaterial> ? UE.GetEditorOnlyData() : nullptr;
            AddShaderNodes(ExprsNode, UE, EditorOnlyData, UE.bIsSky);
        }

        OutJson->SetArrayField(ExpressionsNodeName, ExprsNode);
    }

    template<SerializableFx T>
    static void ExportExpressionInputs(FCEConvertBuilder& Builder, const T& UE, const ExpressionProperties& ExprProp, TSharedPtr<FJsonObject> OutJson, FExtensionNodes& ExtensionNodes)
    {
        auto ExprsNode = OutJson->GetArrayField(ExpressionsNodeName);

        auto FindInputExpressionId = [&](const FExpressionInput* InputPtr) -> std::pair<int32, int32> {
            int32 OriginOutputIndex = InputPtr->OutputIndex;
            int32 ExpressionId = -1, OutputIndex = -1;

            auto& Prop = ExprProp[InputPtr->Expression->GetName()];
            if (Prop.OutputMap != nullptr)
            {
                auto& Object = Prop.OutputMap->GetData()[OriginOutputIndex]->AsObject();
                ExpressionId = Prop.IdxRng[0] + Object->GetNumberField(TEXT("SubId"));
                OutputIndex = Object->GetNumberField(TEXT("OutputIndex"));
            }
            else
            {
                ExpressionId = Prop.IdxRng[1];
                OutputIndex = OriginOutputIndex;
            }

            return {ExpressionId, OutputIndex};
        };

        for (TObjectPtr ExprPtr : UE.GetExpressions())
        {
            auto IdxRng = ExprProp[ExprPtr->GetName()].IdxRng;

            if (auto* FuncInputPtr = Cast<UMaterialExpressionFunctionInput>(ExprPtr.Get()))
            {
                if (FuncInputPtr->InputType == EFunctionInputType::FunctionInput_Texture2D)
                {
                    bool bBadPreview = false;
                    if (!FuncInputPtr->bUsePreviewValueAsDefault || !FuncInputPtr->Preview.Expression)
                    {
                        bBadPreview = true;
                    }
                    else
                    {
                        const EMaterialValueType PreviewOutputType = (EMaterialValueType)FuncInputPtr->Preview.Expression->GetOutputType(FuncInputPtr->Preview.OutputIndex);
                        if (PreviewOutputType != MCT_Texture2D && PreviewOutputType != MCT_Texture)
                        {
                            bBadPreview = true;
                            CE_Log_PopWarn(TEXT("Incorrect preview for FunctionInput '%s' in '%s'. Expected Texture2D"),
                                *FuncInputPtr->InputName.ToString(), *FuncInputPtr->GetOuter()->GetName());
                        }
                    }
                    /// Process Bad Function input preview for texture 2D
                    if (bBadPreview)
                    {
                            if((FuncInputPtr->Preview.Expression))
                            {
                                auto result = FindInputExpressionId(&(FuncInputPtr->Preview));
                                auto& TextureNode = ExprsNode[result.first]->AsObject();
                                TextureNode->SetStringField(TEXT("Class"), TEXT("cross::MaterialExpressionTextureObject"));
                                TextureNode->SetStringField(TEXT("m_TextureString"), TEXT("Contents/Engine/EngineResources/Black.tex.nda"));
                                TextureNode->SetNumberField(TEXT("m_TextureType"), 16);
                                ExprsNode[IdxRng[0]]->AsObject()->SetNumberField(TEXT("m_InputType"), 4);
                            }
                        
                    }
                }
            }

            if (Cast<UMaterialExpressionMaterialFunctionCall>(ExprPtr.Get()))
            {
                check(IdxRng[0] == IdxRng[1]);
                TArray<TSharedPtr<FJsonValue>> InputsNode;
                UMaterialExpression* Expression = ExprPtr.Get();
                for (FExpressionInputIterator It{Expression}; It; ++It)
                {
                    auto InputNode = MakeShared<FJsonObject>();
                    InputsNode.Push(MakeShared<FJsonValueObject>(InputNode));

                    auto InputInputNode = MakeShared<FJsonObject>();
                    InputNode->SetObjectField(TEXT("Input"), InputInputNode);

                    if (It->Expression)
                    {
                        auto result = FindInputExpressionId(It.Input);
                        InputInputNode->SetNumberField(TEXT("LinkedExpressionId"), result.first);
                        InputInputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), result.second);
                    }
                    else
                    {
                        InputInputNode->SetNumberField(TEXT("LinkedExpressionId"), -1);
                        InputInputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), -1);
                    }
                }

                ExprsNode[IdxRng[0]]->AsObject()->SetArrayField(TEXT("m_FunctionInputs"), InputsNode);
                continue;
            }

            if (std::is_same_v<T, UMaterialFunction> && Cast<UMaterialExpressionFunctionOutput>(ExprPtr.Get()))
            {
                check(IdxRng[0] == IdxRng[1]);

                auto* InputPtr = ExprPtr->GetInput(0);
                auto InputNode = MakeShared<FJsonObject>();
                if (InputPtr->Expression)
                {
                    auto result = FindInputExpressionId(InputPtr);
                    InputNode->SetNumberField(TEXT("LinkedExpressionId"), result.first);
                    InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), result.second);
                }
                else
                {
                    InputNode->SetNumberField(TEXT("LinkedExpressionId"), -1);
                    InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), -1);
                }

                ExprsNode[IdxRng[0]]->AsObject()->SetObjectField(TEXT("m_Output"), InputNode);
                continue;
            }

            auto Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName());
            UMaterialExpression* Expression = ExprPtr.Get();

            auto ExtensionNode = ExtensionNodes.Find(ExprPtr.Get());
            if (ExtensionNode)
            {
                for (auto& Node : *ExtensionNode)
                {
                    if (Node.ExtensionNode == EExtensiondNodeType::CEToUEWorldPosition)
                    {
                        Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName() + TEXT("CoordinateConvertExtension"));
                    }
                    else if (Node.ExtensionNode == EExtensiondNodeType::TangentToWordPosition)
                    {
                        Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName() + TEXT("TangentToWSExtension"));
                    }
                    else if (Node.ExtensionNode == EExtensiondNodeType::WorldToTangentPosition)
                    {
                        Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName() + TEXT("WSToTangentExtension"));
                    }
					else if (Node.ExtensionNode == EExtensiondNodeType::FunctionInputTexture)
					{
                        Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName() + TEXT("TexturePreview"));
					}
                }
            }

            int InputIdx = 0;
            if (auto ExpressionSetMaterialAttributes = Cast<UMaterialExpressionSetMaterialAttributes>(Expression); ExpressionSetMaterialAttributes)
            {
                TArray<TSharedPtr<FJsonValue>> InputsNode;
                for (FExpressionInputIterator It{Expression}; It; ++It)
                {
                    FString OriginalInputName = Expression->GetInputName(InputIdx++).ToString();
                    OriginalInputName = OriginalInputName.Replace(TEXT(" "), TEXT(""));
                    FString InputName = Map.MapKey(OriginalInputName);
                    InputName = StringMapper::Instance().FromUEToCEString(*InputName);
                    if (InputName == TEXT("MaterialAttributes"))
                    {
                        InputName.InsertAt(0, "m_");
                        auto InputNode = MakeShared<FJsonObject>();
                        if (It->Expression)
                        {
                            auto result = FindInputExpressionId(It.Input);
                            InputNode->SetNumberField(TEXT("LinkedExpressionId"), result.first);
                            InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), result.second);
                        }
                        else
                        {
                            InputNode->SetNumberField(TEXT("LinkedExpressionId"), -1);
                            InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), -1);
                        }
                        ExprsNode[IdxRng[0]]->AsObject()->SetObjectField(InputName, InputNode);
                        continue;
                    }

                    int attributeType = FCString::Atoi(*InputName);
                    if (attributeType == 0)
                        continue;
                    if (attributeType > SUPPORTED_MATERIAL_ATTRIBUTES)
                    {
                        CE_Log_PopWarn(TEXT("MaterialAttributes is Missing in CE:\"%s\"; Material is: \"%s\" "), *OriginalInputName, *UE.GetName())
                    }

                    auto InputNode = MakeShared<FJsonObject>();
                    InputsNode.Push(MakeShared<FJsonValueObject>(InputNode));

                    auto InputInputNode = MakeShared<FJsonObject>();
                    InputNode->SetNumberField(TEXT("Attribute"), attributeType);
                    InputNode->SetObjectField(TEXT("Input"), InputInputNode);
                    if (It->Expression)
                    {
                        auto result = FindInputExpressionId(It.Input);
                        InputInputNode->SetNumberField(TEXT("LinkedExpressionId"), result.first);
                        InputInputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), result.second);
                    }
                    else
                    {
                        InputInputNode->SetNumberField(TEXT("LinkedExpressionId"), -1);
                        InputInputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), -1);
                    }
                }

                ExprsNode[IdxRng[0]]->AsObject()->SetArrayField(TEXT("m_AttributesInputs"), InputsNode);
            }
            else if (auto LandscapeLayerCoords = Cast<UMaterialExpressionLandscapeLayerCoords>(Expression); LandscapeLayerCoords)
            {
                ExprsNode[IdxRng[0]]->AsObject()->SetNumberField(TEXT("m_MapperType"), LandscapeLayerCoords->MappingType);
                ExprsNode[IdxRng[0]]->AsObject()->SetNumberField(TEXT("m_CustomUVType"), LandscapeLayerCoords->CustomUVType);
                ExprsNode[IdxRng[0]]->AsObject()->SetNumberField(TEXT("m_MappingScale"), LandscapeLayerCoords->MappingScale);
                ExprsNode[IdxRng[0]]->AsObject()->SetNumberField(TEXT("m_MappingRotation"), LandscapeLayerCoords->MappingRotation);
                ExprsNode[IdxRng[0]]->AsObject()->SetNumberField(TEXT("m_MappingPanU"), LandscapeLayerCoords->MappingPanU);
                ExprsNode[IdxRng[0]]->AsObject()->SetNumberField(TEXT("m_MappingPanV"), LandscapeLayerCoords->MappingPanV);  
            }
            else if (auto LandscapeLayerBlend = Cast<UMaterialExpressionLandscapeLayerBlend>(Expression); LandscapeLayerBlend)
            {
                auto SetInput = [&](const FExpressionInput* Input, TSharedPtr<FJsonObject> Node)
                {
                    if (Input->Expression)
                    {
                        auto Result = FindInputExpressionId(Input);
                        Node->SetNumberField(TEXT("LinkedExpressionId"), Result.first);
                        Node->SetNumberField(TEXT("LinkedExpressionOutputIndex"), Result.second);
                    }
                    else
                    {
                        Node->SetNumberField(TEXT("LinkedExpressionId"), -1);
                        Node->SetNumberField(TEXT("LinkedExpressionOutputIndex"), -1);
                    }
                };

                TArray<TSharedPtr<FJsonValue>> LayerNodes;
                for (const auto& Layer : LandscapeLayerBlend->Layers)
                {
                    auto LayerNode = MakeShared<FJsonObject>();
                    LayerNode->SetStringField(TEXT("m_LayerName"), Layer.LayerName.ToString());
                    LayerNode->SetNumberField(TEXT("m_BlendType"), Layer.BlendType);

                    TSharedPtr<FJsonObject> LayerInputJsonObject = MakeShareable(new FJsonObject);
                    SetInput(&Layer.LayerInput, LayerInputJsonObject);
                    LayerNode->SetObjectField(TEXT("m_LayerInput"), LayerInputJsonObject);

                    TSharedPtr<FJsonObject> HeightInputJsonObject = MakeShareable(new FJsonObject);
                    SetInput(&Layer.HeightInput, HeightInputJsonObject);
                    LayerNode->SetObjectField(TEXT("m_HeightInput"), HeightInputJsonObject);

                    TSharedPtr<FJsonObject> VectorJsonObject = MakeShareable(new FJsonObject);
                    VectorJsonObject->SetNumberField(TEXT("x"), Layer.ConstLayerInput.X);
                    VectorJsonObject->SetNumberField(TEXT("y"), Layer.ConstLayerInput.Y);
                    VectorJsonObject->SetNumberField(TEXT("z"), Layer.ConstLayerInput.Z);
                    VectorJsonObject->SetNumberField(TEXT("w"), 0.f);
                    LayerNode->SetObjectField(TEXT("m_ConstLayerInput"), VectorJsonObject);

                    LayerNode->SetNumberField(TEXT("m_ConstHeightInput"), Layer.ConstHeightInput);

                    LayerNodes.Add(MakeShared<FJsonValueObject>(LayerNode));
                }

                ExprsNode[IdxRng[0]]->AsObject()->SetArrayField(TEXT("m_Layers"), LayerNodes);
            }
            else if (auto ExpressionCustom = Cast<UMaterialExpressionCustom>(Expression); ExpressionCustom && Builder.GetExportOptions()->bExportCustomNodeShaderCode)
			{
                ExprsNode[IdxRng[0]]->AsObject()->SetStringField("m_Name", ExpressionCustom->Description);
                ExprsNode[IdxRng[0]]->AsObject()->SetStringField("m_Code", ExpressionCustom->Code);
                ExprsNode[IdxRng[0]]->AsObject()->SetNumberField("m_OutputType", ExpressionCustom->OutputType);
				// Inputs
                TArray<TSharedPtr<FJsonValue>> InputsNode;
                for (auto customInput : ExpressionCustom->Inputs)
                {
                    auto InputNode = MakeShared<FJsonObject>();
                    InputsNode.Push(MakeShared<FJsonValueObject>(InputNode));
                    InputNode->SetStringField(TEXT("Name"), customInput.InputName.ToString());
                    auto InputInputNode = MakeShared<FJsonObject>();
                    InputNode->SetObjectField(TEXT("Input"), InputInputNode);
                    auto result = FindInputExpressionId(&customInput.Input);
                    InputInputNode->SetNumberField(TEXT("LinkedExpressionId"), result.first);
                    InputInputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), result.second);
                }
                ExprsNode[IdxRng[0]]->AsObject()->SetArrayField(TEXT("m_CustomInputs"), InputsNode);
				// Outputs
                TArray<TSharedPtr<FJsonValue>> OutputsNode;
                for (auto customOutput : ExpressionCustom->AdditionalOutputs)
                {
                    FString OutputName = customOutput.OutputName.ToString();
                    auto OutputNode = MakeShared<FJsonObject>();
                    OutputsNode.Push(MakeShared<FJsonValueObject>(OutputNode));
                    OutputNode->SetStringField(TEXT("Name"), OutputName);
                    OutputNode->SetNumberField(TEXT("OutputType"), customOutput.OutputType);
                }
                ExprsNode[IdxRng[0]]->AsObject()->SetArrayField("m_AdditionalCustomOutputs", OutputsNode);
			}
			else
            {
                for (FExpressionInputIterator It{Expression}; It; ++It)
                {
                    FString InputName = Expression->GetInputName(InputIdx++).ToString();
                    auto SubId = Map.GetSubId(InputName);

                    InputName = Map.MapKey(InputName);
                    InputName = StringMapper::Instance().FromUEToCEString(*InputName);
                    InputName.InsertAt(0, "m_");

                    auto InputNode = MakeShared<FJsonObject>();
                    if (It->Expression)
                    {
                        auto result = FindInputExpressionId(It.Input);
                        InputNode->SetNumberField(TEXT("LinkedExpressionId"), result.first);
                        InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), result.second);
                    }
                    else
                    {
                        InputNode->SetNumberField(TEXT("LinkedExpressionId"), -1);
                        InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), -1);
                    }

                    ExprsNode[IdxRng[0] + SubId]->AsObject()->SetObjectField(InputName, InputNode);
                }
            }

            if (Map.CompositionNum == 1)
                continue;

            // Build inner links for Composite Expression
            for (const auto& [From, To] : Map.KeyMapNode->Get()->Values)
            {
                auto [FromSubId, FromOutputId] = MaterialFxMapper::Map::ParseInnerOutput(From);
                auto [ToSubId, ToInputName] = MaterialFxMapper::Map::ParseInnerInput(To->AsString());
                if (FromSubId == -1 || ToSubId == -1)
                    continue;

                auto InputNode = MakeShared<FJsonObject>();
                InputNode->SetNumberField(TEXT("LinkedExpressionId"), IdxRng[0] + FromSubId);
                InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), FromOutputId);

                ToInputName.InsertAt(0, TEXT("m_"));
                ExprsNode[IdxRng[0] + ToSubId]->AsObject()->SetObjectField(ToInputName, InputNode);
            }
        }

        if constexpr (std::is_same_v<T, UMaterial>)
        {
            // Temporarily add Surface Shader
			{
				auto ExprNode = ExprsNode.Last()->AsObject();
				for (auto MaterialPropEnum : {MP_BaseColor, MP_Metallic, MP_Specular, MP_Roughness, MP_Opacity, MP_OpacityMask, MP_Normal, MP_AmbientOcclusion, MP_EmissiveColor, MP_SubsurfaceColor, MP_MaterialAttributes})
				{
					auto* InputPtr = const_cast<UMaterial&>(UE).GetExpressionInputForProperty(MaterialPropEnum);

					auto InputNode = MakeShared<FJsonObject>();
					if (InputPtr->Expression)
					{
						auto result = FindInputExpressionId(InputPtr);
						InputNode->SetNumberField(TEXT("LinkedExpressionId"), result.first);
						InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), result.second);
					}
					else
					{
						InputNode->SetNumberField(TEXT("LinkedExpressionId"), -1);
						InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), -1);
					}

					FString InputPropName = StaticEnum<EMaterialProperty>()->GetNameStringByValue(MaterialPropEnum);
					InputPropName.ReplaceInline(TEXT("MP_"), TEXT("m_"));
					ExprNode->SetObjectField(InputPropName, InputNode);
				}
            }
            // Temporarily add Vertex Shader
			{
                ExprsNode.RangeCheck(ExprsNode.Num() - 2);
				// UE2CE Converter
                auto ExprNode = ExprsNode.GetData()[ExprsNode.Num()-3]->AsObject();
                auto* InputPtr = UE.bUseMaterialAttributes ? 
							const_cast<UMaterial&>(UE).GetExpressionInputForProperty(MP_MaterialAttributes) : 
							const_cast<UMaterial&>(UE).GetExpressionInputForProperty(MP_WorldPositionOffset);

				auto InputNode = MakeShared<FJsonObject>();
                if (InputPtr->Expression)
                {
                    auto result = FindInputExpressionId(InputPtr);
                    InputNode->SetNumberField(TEXT("LinkedExpressionId"), result.first);
                    InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), result.second);
                }
                else
                {
                    InputNode->SetNumberField(TEXT("LinkedExpressionId"), -1);
                    InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), -1);
                }
                ExprNode->SetObjectField(TEXT("m_Input"), InputNode);
				// Vertex Shader
                ExprNode = ExprsNode.GetData()[ExprsNode.Num() - 2]->AsObject();
                InputNode = MakeShared<FJsonObject>();
				if (InputPtr->Expression)
				{
					InputNode->SetNumberField(TEXT("LinkedExpressionId"), ExprsNode.Num()-3);
					InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), 0);
				}
				else
				{
                    InputNode->SetNumberField(TEXT("LinkedExpressionId"), -1);
                    InputNode->SetNumberField(TEXT("LinkedExpressionOutputIndex"), -1);
				}
                ExprNode->SetObjectField(TEXT("m_WorldPositionOffset"), InputNode);
			}
        }
    }

    template<SerializableFx T>
    static void ExportExpressionInputProperties(const T& UE, const ExpressionProperties& ExprProp, TSharedPtr<FJsonObject> OutJson, FExtensionNodes& ExtensionNodes)
    {
        auto ExprNodes = OutJson->GetArrayField(ExpressionsNodeName);

        auto ExportForOneExpression = [&](const UMaterialExpression* ExprPtr, TSharedPtr<FJsonObject> ExprNode) {
            auto Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName());

            auto ExtensionNode = ExtensionNodes.Find(ExprPtr);
            if (ExtensionNode)
            {
                for (auto& Node : *ExtensionNode)
                {
                    if (Node.ExtensionNode == EExtensiondNodeType::CEToUEWorldPosition)
                    {
                        Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName() + TEXT("CoordinateConvertExtension"));
                    }
                    else if (Node.ExtensionNode == EExtensiondNodeType::TangentToWordPosition)
                    {
                        Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName() + TEXT("TangentToWSExtension"));
                    }
                    else if (Node.ExtensionNode == EExtensiondNodeType::WorldToTangentPosition)
                    {
                        Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName() + TEXT("WSToTangentExtension"));
                    }
                    else if (Node.ExtensionNode == EExtensiondNodeType::FunctionInputTexture)
                    {
                        Map = MaterialFxMapper::Instance().GetMap(ExprPtr->GetClass()->GetName() + TEXT("TexturePreview"));
                    }
                }
            }

            for (TFieldIterator<FProperty> PropItr(ExprPtr->GetClass(), EFieldIteratorFlags::IncludeSuper, EFieldIteratorFlags::ExcludeDeprecated); PropItr; ++PropItr)
            {
                if (auto Is = PropItr->HasMetaData(TEXT("OverridingInputProperty")) || PropItr->HasMetaData(TEXT("ShowAsInputPin")); !Is)
                    continue;

                FString PropName = PropItr->GetName();
                PropName = Map.MapKey(PropName);
                PropName.InsertAt(0, "m_");

#define BREAK_IF(X)                                                                                                                                                                                                                            \
    if (X)                                                                                                                                                                                                                                     \
        break;

                static constexpr std::array ComponentNames{TEXT("x"), TEXT("y"), TEXT("z"), TEXT("w")};
                auto TestVectorThenSet = [&]<typename VecTy>(const FStructProperty* Ptr, TSharedPtr<FJsonObject> OutJson, const VecTy*) {
                    if (Ptr->Struct != TBaseStructure<VecTy>::Get())
                        return false;

                    auto* VecPtr = Ptr->ContainerPtrToValuePtr<VecTy>(ExprPtr);
                    if constexpr (std::is_same_v<VecTy, FIntVector>)
                    {
                        OutJson->SetNumberField(ComponentNames[0], VecPtr->X);
                        OutJson->SetNumberField(ComponentNames[1], VecPtr->Y);
                        OutJson->SetNumberField(ComponentNames[2], VecPtr->Z);
                    }
                    else
                        for (int32 i = 0; i < sizeof(VecTy) / sizeof(VecPtr->Component(0)); ++i)
                            OutJson->SetNumberField(ComponentNames[i], VecPtr->Component(i));

                    return true;
                };
                auto TestTypeThenSet = [&]<typename PropTy>(const PropTy*) {
                    auto* Ptr = CastField<PropTy>(*PropItr);
                    if (!Ptr)
                        return false;

                    if constexpr (std::is_same_v<PropTy, FStrProperty>)
                    {
                        auto* ValPtr = Ptr->ContainerPtrToValuePtr<FString>(ExprPtr);
                        ExprNode->SetStringField(PropName, *ValPtr);
                    }
                    else if constexpr (std::is_same_v<PropTy, FStructProperty>)
                    {
                        auto PropNode = MakeShared<FJsonObject>();
                        while (true)
                        {

                            BREAK_IF(TestVectorThenSet(Ptr, PropNode, (FIntVector*)nullptr));
                            BREAK_IF(TestVectorThenSet(Ptr, PropNode, (FLinearColor*)nullptr));
                            BREAK_IF(TestVectorThenSet(Ptr, PropNode, (FVector*)nullptr));
                            BREAK_IF(TestVectorThenSet(Ptr, PropNode, (FVector4d*)nullptr));

                            // Since TBaseStructure<FVector4f, FVector3f> is NOT defined, we need to check them manually
                            for (auto VecName : {TEXT("Vector3f"), TEXT("Vector4f")})
                                if (Ptr->Struct->GetName() == VecName)
                                {
                                    auto* VecPtr = Ptr->ContainerPtrToValuePtr<FVector4f>(ExprPtr);
                                    for (int32 i = 0; i < 4; ++i)
                                        PropNode->SetNumberField(ComponentNames[i], VecPtr->Component(i));
                                }

                            break;
                        }
                        ExprNode->SetObjectField(PropName, PropNode);
                    }
                    else
                    {
                        using ValTy = PropTy::TCppType;
                        auto Val = Ptr->GetPropertyValue_InContainer(ExprPtr);
                        if constexpr (std::is_same_v<ValTy, float> || std::is_same_v<ValTy, double> || std::is_same_v<ValTy, int32> || std::is_same_v<ValTy, uint32>)
                            ExprNode->SetNumberField(PropName, Val);
                        else if constexpr (std::is_same_v<ValTy, bool>)
                            ExprNode->SetBoolField(PropName, Val);
                    }

                    return true;
                };
                while (true)
                {
                    BREAK_IF(TestTypeThenSet((FFloatProperty*)nullptr));
                    BREAK_IF(TestTypeThenSet((FDoubleProperty*)nullptr));
                    BREAK_IF(TestTypeThenSet((FIntProperty*)nullptr));
                    BREAK_IF(TestTypeThenSet((FUInt32Property*)nullptr));
                    BREAK_IF(TestTypeThenSet((FBoolProperty*)nullptr));
                    BREAK_IF(TestTypeThenSet((FStrProperty*)nullptr));
                    BREAK_IF(TestTypeThenSet((FStructProperty*)nullptr));

                    break;
                }
#undef CONTINUE_IF
            }
        };

        for (TObjectPtr ExprPtr : UE.GetExpressions())
        {
            auto IdxRng = ExprProp[ExprPtr->GetName()].IdxRng;
            for (auto Id = IdxRng[0]; Id <= IdxRng[1]; ++Id)
            {
                auto ExprNode = ExprNodes[Id]->AsObject();
                ExportForOneExpression(ExprPtr.Get(), ExprNode);
            }

            // Texture value in UE is specified at Material's parameters, while
            // it is specified at Expression's parameters in CE
            if (auto* Ptr = Cast<UMaterialExpressionTextureSampleParameter>(ExprPtr.Get()); Ptr)
            {
                // contain UMaterialExpressionTextureSampleParameter and UMaterialExpressionTextureObjectParameter here
                auto PropertiesNode = OutJson->GetObjectField(FxPropertiesNodeName)->GetObjectField(DefaultGroupNodeName);
                FString ExprName = GetExpressionName(Ptr->ParameterName.ToString());
                if (!PropertiesNode->HasField(ExprName))
                    continue;

                for (auto Id = IdxRng[0]; Id <= IdxRng[1]; ++Id)
                {
                    auto ExprNode = ExprNodes[Id]->AsObject();
                    // Only UMaterial has properties
                    if constexpr (std::is_same_v<T, UMaterial>)
                        ExprNode->SetStringField(TEXT("m_TextureString"), PropertiesNode->GetObjectField(ExprName)->GetStringField(PropertyValuesNodeName));
                    else
                    {
                        // TODO
                    }
                }
            }
        }
    }

    static void ExportMaterialDefines(const UMaterial& UE, TSharedPtr<FJsonObject> OutJson)
    {
        auto DefinesNode = MakeShared<FJsonObject>();

        // Export Material Domain
        int32 Domain = 0;
        switch (UE.MaterialDomain)
        {
        case MD_Surface:
            Domain = 0;   // Surface
            break;
        case MD_DeferredDecal: // MeshDecal
            Domain = 1;
            break;
        case MD_PostProcess:
            Domain = 2;   // PostProcess
            break;
        // TODO: Foliage
        default:
            Domain = 0;   // Default to Surface
            break;
        }
        DefinesNode->SetNumberField(TEXT("Domain"), Domain);

        // Export Blend Mode
        int32 BlendMode = 0;
        switch (UE.BlendMode)
        {
        case BLEND_Opaque:
            BlendMode = 0;   // Opaque
            break;
        case BLEND_Masked:
            BlendMode = 1;   // Masked
            break;
        case BLEND_Translucent:
            BlendMode = 2;   // Translucent
            break;
        case BLEND_Additive:
            BlendMode = 3;   // Additive
            break;
        case BLEND_Modulate:
            BlendMode = 4;   // Modulate
            break;
        case BLEND_AlphaComposite:
            BlendMode = 5;   // AlphaComposite
            break;
        default:
            BlendMode = 0;   // Default to Opaque
            break;
        }
        if (UE.bIsSky)
        {
            BlendMode = 2; // SkyBox is Translucent in CE
        }
        DefinesNode->SetNumberField(TEXT("BlendMode"), BlendMode);

        // Export Shading Model
        int32 ShadingModel = 0;
        auto ShadingModels = UE.GetShadingModels();
        if (ShadingModels.IsLit())
        {
            ShadingModel = 0;
        }
        else if (ShadingModels.IsUnlit())
        {
            ShadingModel = 1;
        }
        // TODO: other
        DefinesNode->SetNumberField(TEXT("ShadingModel"), ShadingModel);

        // Export Two Sided
        DefinesNode->SetBoolField(TEXT("TwoSided"), UE.TwoSided);

        // Export Use Material Attributes
        DefinesNode->SetBoolField(TEXT("UseMaterialAttributes"), UE.bUseMaterialAttributes);

        // Export Render Group Bias
        //DefinesNode->SetNumberField(TEXT("RenderGroupBias"), UE.RenderGroupBias);

        // Export Translucency properties
        //DefinesNode->SetBoolField(TEXT("ForceDisableExponentialFog"), UE.bDisableDepthTest);
        //DefinesNode->SetBoolField(TEXT("ForceDisableVolumetricFog"), UE.bDisableDepthTest);
        //DefinesNode->SetBoolField(TEXT("ForceDisableCloudFog"), UE.bDisableDepthTest);
        //DefinesNode->SetBoolField(TEXT("ForceDisableSSR"), UE.bDisableDepthTest);
        DefinesNode->SetBoolField(TEXT("EnableSeparateTranslucency"), true/*UE.TranslucencyPass == MTP_AfterDOF*/);

        // Export Usage properties
        DefinesNode->SetBoolField(TEXT("UsedWithSkeletalMesh"), UE.bUsedWithSkeletalMesh);
        //DefinesNode->SetBoolField(TEXT("UsedWithLocalSpaceParticle"), UE.bUsedWithParticleSprites);
        //DefinesNode->SetBoolField(TEXT("UsedWithGlobalSpaceParticle"), UE.bUsedWithBeamTrails);
        //DefinesNode->SetBoolField(TEXT("UsedWithTerrain"), UE.bUsedWithLandscape);

        for (const auto& Expression : UE.GetExpressions())
        {
            if (Expression.IsA(UMaterialExpressionLandscapeLayerBlend::StaticClass()) ||
                Expression.IsA(UMaterialExpressionLandscapeLayerWeight::StaticClass()))
            {
                DefinesNode->SetBoolField(TEXT("UsedWithTerrain"), true);
                DefinesNode->SetNumberField(TEXT("TerrainMode"), 2);
                break;
            }
        }

        // Export PostProcess properties
        int32 BlendableLocation = 0;
        switch (UE.BlendableLocation)
        {
        case BL_AfterTonemapping:
            BlendableLocation = 0;   // AfterToneMapping
            break;
        case BL_BeforeTonemapping:
            BlendableLocation = 1;   // BeforeToneMapping
            break;
        case BL_ReplacingTonemapper:
            BlendableLocation = 2;   // ReplacingToneMapper
            break;
        case BL_BeforeTranslucency:
            BlendableLocation = 3;   // BeforeTranslucency
            break;
        case BL_SSRInput:
            BlendableLocation = 4;   // SSRInput
            break;
        default:
            BlendableLocation = 0;   // Default to AfterToneMapping
            break;
        }
        DefinesNode->SetNumberField(TEXT("PostProcessBlendableLocation"), BlendableLocation);
        DefinesNode->SetBoolField(TEXT("PostProcessEnableOutputAlpha"), UE.BlendableOutputAlpha);
        DefinesNode->SetNumberField(TEXT("PostProcessBlendablePriority"), UE.BlendablePriority);
        DefinesNode->SetBoolField(TEXT("PostProcessIsBlendable"), UE.bIsBlendable);

        //// Export Debug properties
        //DefinesNode->SetBoolField(TEXT("EnableDebugSymbol"), UE.bEnableSeparateTranslucency);

        //// Export Advanced Mode
        //DefinesNode->SetBoolField(TEXT("EnableAdvancedMode"), UE.bEnableSeparateTranslucency);

        // Add DefinesNode to OutJson
        OutJson->SetObjectField(TEXT("defines"), DefinesNode);
    }

	template<SerializableFx T>
    static FMaterialEditor* GetOpenMaterialEditor(T* Material)
    {
        if (!Material || !GIsEditor || !GEditor)
            return nullptr;

        // 获取资产编辑器子系统
        UAssetEditorSubsystem* AssetEditorSubsystem = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>();
        if (!AssetEditorSubsystem)
            return nullptr;

        // 检查材质是否已打开
        TArray<IAssetEditorInstance*> Editors = AssetEditorSubsystem->FindEditorsForAsset(Material);
        if (Editors.Num() == 0)
        {
            // 材质未打开，打开它
            AssetEditorSubsystem->OpenEditorForAsset(Material);
            TArray<IAssetEditorInstance*> EditorsNew = AssetEditorSubsystem->FindEditorsForAsset(Material);
            if (!EditorsNew[0])
                return nullptr;

            // 将编辑器添加到列表中
            Editors.Add(EditorsNew[0]);
        }

        // 获取第一个编辑器实例并确保它是材质编辑器
        if (Editors.Num() > 0)
        {
            // 尝试将其转换为材质编辑器接口
            FMaterialEditor* MaterialEditor = static_cast<FMaterialEditor*>(Editors[0]);
            return MaterialEditor;
        }

        return nullptr;
    }
    template<SerializableFx T>
    static bool ExportFxOrMaterialFunction(FCEConvertBuilder& Builder, const T& UE,  FAssetExchangeInput* Input)
    {
		if(MaterialFxMapper::Instance().GetMaterialFxExported().Contains(UE.GetPathName()))
		{
            return true;
		}
		else
		{
            MaterialFxMapper::Instance().GetMaterialFxExported().Add(UE.GetPathName());
		}

        FString RelativePathCEFStr = UTF8_TO_TCHAR(Input->RelativePath);
        if (!Input->Options->bOverwriteExistingMaterial)
        {
            FString StagingPathCEFStr(UTF8_TO_TCHAR(Input->Sdk->GetStagingPath()));
            if (FPaths::FileExists(StagingPathCEFStr + "/" + RelativePathCEFStr))
            {
                if constexpr (std::is_same_v<T, UMaterial>)
                {
                    CE_Log(TEXT("CEAssetExchange: Skip Fx \"%s\", due to file already exist"), *RelativePathCEFStr);
                }
                else
                {
                    CE_Log(TEXT("CEAssetExchange: Skip Material Function \"%s\", due to file already exist"), *RelativePathCEFStr);
                }

                return true;
            }
        }

        auto* Fx = Input->Sdk->CreateFxAssemble(Input->RelativePath);
        if (Fx->HasBeenDestroyed() || Fx->HasEndAssemble())
            return true;
        {
            if constexpr (std::is_same_v<T, UMaterial>)
            {
                CE_Log(TEXT("CEAssetExchange: Export Fx \"%s\""), *RelativePathCEFStr);
            }
            else
            {
                CE_Log(TEXT("CEAssetExchange: Export Material Function \"%s\""), *RelativePathCEFStr);
            }

            FFormatNamedArguments Args;
            Args.Add(TEXT("ResourceName"), FText::FromString(RelativePathCEFStr));
            FScopedSlowTask SlowTask(0, FText::Format(LOCTEXT("Export", "Export Resource \"{ResourceName}\" ..."), Args), true);
            SlowTask.MakeDialog();
        }

        auto ExtensionNodes = FExtensionNodes{};
        RecordNodeThatNeedCoordinateConvert(UE, ExtensionNodes);

        auto JsonOutput = MakeShared<FJsonObject>();
        for (TObjectPtr<UMaterialExpression> ExprPtr : UE.GetExpressions())
        {
            if (UMaterialExpressionComposite* CompositeExpr = Cast<UMaterialExpressionComposite>(ExprPtr))
            {
                FString WarningMessage = FString::Printf(TEXT("警告: 材质 '%s' 包含复合表达式节点!\n\n") TEXT("复合节点需要先手动展开才能正确导出。请按照以下步骤操作:\n") TEXT("1. 关闭此窗口\n") TEXT("2. 在内容浏览器中双击打开材质\n")
                                                                TEXT("3. 在材质编辑器中选择所有复合节点\n") TEXT("4. 右键点击并选择 '展开节点' 或使用快捷键\n") TEXT("5. 保存材质\n") TEXT("6. 重新运行导出\n\n")
                                                                    TEXT("是否继续尝试导出? (可能无法正确处理复合节点)"),
                                                            *UE.GetName());

                CE_Log_PopWarn(TEXT("请修改材质蓝图: %s"), *WarningMessage);

                FMaterialEditor* MaterialEditor = GetOpenMaterialEditor(const_cast<T*>(&UE));
                break;
            }
			if (UMaterialExpressionCustom* CustomExpr = Cast<UMaterialExpressionCustom>(ExprPtr))
			{
                CE_Log_PopWarn(TEXT("Custom expression '%s' in Material '%s'"), *CustomExpr->Description, *UE.GetName());
                //FMaterialEditor* MaterialEditor = GetOpenMaterialEditor(const_cast<T*>(&UE));
                break;
			}
        }

        if constexpr (std::is_same_v<T, UMaterial>)
        {
            JsonOutput->SetNumberField(TEXT("version"), 2);
            ExportProperties(Builder, UE, Input, JsonOutput, Fx);
            ExportMaterialDefines(UE, JsonOutput);
        }

        ExpressionProperties ExprNameToIdxRng;
        ExportExpressions(Builder, UE, Input, ExprNameToIdxRng, JsonOutput, Fx, ExtensionNodes);
		AddNodeProperties(UE, ExprNameToIdxRng, JsonOutput);
        ExportExpressionInputs(Builder, UE, ExprNameToIdxRng, JsonOutput, ExtensionNodes);
        ExportExpressionInputProperties(UE, ExprNameToIdxRng, JsonOutput, ExtensionNodes);
        ExportMaterialParameterCollections(Builder, UE.GetExpressions(), JsonOutput, Fx, Input);

        FString JsonStr;
        auto JsonWriter = TJsonWriterFactory<>::Create(&JsonStr);
        FJsonSerializer::Serialize(JsonOutput, JsonWriter);

        if constexpr (std::is_same_v<T, UMaterial>)
            Fx->SetupFromJsonString(StringCast<ANSICHAR>(*JsonStr).Get(), IFxAssemble::Type::Fx);
        else if constexpr (std::is_same_v<T, UMaterialFunction>)
            Fx->SetupFromJsonString(StringCast<ANSICHAR>(*JsonStr).Get(), IFxAssemble::Type::MaterialFunction);

        Fx->EndAssemble();
        Fx->SaveToFile();
        FString error = FString(UTF8_TO_TCHAR(Fx->GetCompileErrorMessage()));
        if (std::is_same_v<T, UMaterial> && error != TEXT(""))
		{
			CE_Log_PopFxCompileError(TEXT("============== Fx: \"%s\" CompilingError==============\n %s \n"), *UE.GetName(), *error);
		}
        return true;
    }

    static void ExportMaterialInstanceDefines(const UMaterialInstance& UE, TSharedPtr<FJsonObject> OutJson, const char* ParentRPathCE)
    {
        auto DefinesNode = MakeShared<FJsonObject>();

        // Export Blend Mode
        if (UE.BasePropertyOverrides.bOverride_BlendMode)
        {
            DefinesNode->SetBoolField(TEXT("BlendModeEnable"), true);
            int32 BlendMode = 0;
            switch (UE.BasePropertyOverrides.BlendMode)
            {
            case BLEND_Opaque:
                BlendMode = 0;   // Opaque
                break;
            case BLEND_Masked:
                BlendMode = 1;   // Masked
                break;
            case BLEND_Translucent:
                BlendMode = 2;   // Translucent
                break;
            case BLEND_Additive:
                BlendMode = 3;   // Additive
                break;
            case BLEND_Modulate:
                BlendMode = 4;   // Modulate
                break;
            case BLEND_AlphaComposite:
                BlendMode = 5;   // AlphaComposite
                break;
            default:
                BlendMode = 0;   // Default to Opaque
                break;
            }
            DefinesNode->SetNumberField(TEXT("BlendMode"), BlendMode);
        }
        else
        {
            DefinesNode->SetBoolField(TEXT("BlendModeEnable"), false);
        }

        // Export Shading Model
        if (UE.BasePropertyOverrides.bOverride_ShadingModel)
        {
            DefinesNode->SetBoolField(TEXT("ShadingModelEnable"), true);
            int32 ShadingModel = 0;
            auto ShadingModels = UE.BasePropertyOverrides.ShadingModel;
            if (ShadingModels == MSM_DefaultLit)
            {
                ShadingModel = 0;   // Standard
            }
            else if (ShadingModels == MSM_Unlit)
            {
                ShadingModel = 1;   // Unlit
            }
            // TODO: other
            DefinesNode->SetNumberField(TEXT("ShadingModel"), ShadingModel);
        }
        else
        {
            DefinesNode->SetBoolField(TEXT("ShadingModelEnable"), false);
        }

        // Export Two Sided
        if (UE.BasePropertyOverrides.bOverride_TwoSided)
        {
            DefinesNode->SetBoolField(TEXT("TwoSidedEnable"), true);
            DefinesNode->SetBoolField(TEXT("TwoSided"), UE.BasePropertyOverrides.TwoSided);
        }
        else
        {
            DefinesNode->SetBoolField(TEXT("TwoSidedEnable"), false);
        }

        // Export Render Group Bias
        //DefinesNode->SetNumberField(TEXT("RenderGroupBias"), UE.RenderGroupBias);

        // Add DefinesNode to OutJson
        OutJson->SetObjectField(TEXT("defines"), DefinesNode);
    }

    static bool ExportMaterialInstance(FCEConvertBuilder& Builder, const UMaterialInstance& UE,  FAssetExchangeInput* Input)
    {
        const char* ParentRPathCE = nullptr;
        if (auto* Parent = Cast<UMaterialInstance>(UE.Parent))
        {
            FString ParentRPath = Builder.GetCEContentPath(Parent);
            FAssetExchangeInput ParentInput{Input->Options, Input->Sdk, StringMapper::Instance().FromUEToCEString(*ParentRPath)};
            if (ExportMaterialInstance(Builder, *Parent, &ParentInput))
                ParentRPathCE = ParentInput.RelativePath;
        }
        if (auto* Parent = Cast<UMaterial>(UE.Parent))
        {
            FString ParentRPath = Builder.GetCEContentPath(Parent);
            FAssetExchangeInput ParentInput{Input->Options, Input->Sdk, StringMapper::Instance().FromUEToCEString(*ParentRPath)};
            if (ExportFxOrMaterialFunction(Builder, *Parent, &ParentInput))
                ParentRPathCE = ParentInput.RelativePath;
        }

        FString RelativePathCEFStr = UTF8_TO_TCHAR(Input->RelativePath);
        if (!Input->Options->bOverwriteExistingMaterial)
        {
            FString StagingPathCEFStr(UTF8_TO_TCHAR(Input->Sdk->GetStagingPath()));
            if (FPaths::FileExists(StagingPathCEFStr + "/" + RelativePathCEFStr))
            {
                CE_Log(TEXT("CEAssetExchange: Skip Material Material \"%s\", due to file already exist"), *RelativePathCEFStr);
                return true;
            }
        }

        auto* Fx = Input->Sdk->CreateFxAssemble(Input->RelativePath);
        if (Fx->HasBeenDestroyed() || Fx->HasEndAssemble())
            return true;
        {
            CE_Log(TEXT("CEAssetExchange: Export Material \"%s\""), *RelativePathCEFStr);

            FFormatNamedArguments Args;
            Args.Add(TEXT("ResourceName"), FText::FromString(RelativePathCEFStr));
            FScopedSlowTask SlowTask(0, FText::Format(LOCTEXT("Export", "Export Resource \"{ResourceName}\" ..."), Args), true);
            SlowTask.MakeDialog();
        }

        auto JsonOutput = MakeShared<FJsonObject>();

        JsonOutput->SetNumberField(TEXT("version"), 2);
        {
            auto ParentNode = MakeShared<FJsonObject>();
            ParentNode->SetNumberField(TEXT("ASSET_MAGIC_NUMBER"), 778986593);
            ParentNode->SetStringField(TEXT("Path"), StringCast<TCHAR>(ParentRPathCE).Get());

            JsonOutput->SetObjectField(TEXT("parent"), ParentNode);
            if (ParentRPathCE)
                Fx->AddDependencies(ParentRPathCE);

            ExportMaterialInstanceDefines(UE, JsonOutput, ParentRPathCE);
        }

        ExportProperties(Builder, UE, Input, JsonOutput, Fx);

        FString JsonStr;
        auto JsonWriter = TJsonWriterFactory<>::Create(&JsonStr);
        FJsonSerializer::Serialize(JsonOutput, JsonWriter);

        Fx->SetupFromJsonString(StringCast<ANSICHAR>(*JsonStr).Get(), IFxAssemble::Type::Material);
        Fx->EndAssemble();
        Fx->SaveToFile();
        return true;
    }
};

bool IsMaterialInstance(const UMaterialInterface& MaterialInterface)
{
    if (auto InstancePtr = Cast<UMaterialInstance>(&MaterialInterface); InstancePtr)
    {
        return true;
    }
    return false;
}

bool ExportMaterialFX(FCEConvertBuilder& Builder, const UMaterialInterface& MaterialInterface, FAssetExchangeInput* Input, const FString& DefaultMaterial)
{
    //MaterialFxMapper::Instance().Initialize();   // Reload config every time

    if (auto InstancePtr = Cast<UMaterialInstance>(&MaterialInterface); InstancePtr)
        return ExportFuncs::ExportMaterialInstance(Builder, *InstancePtr, Input);
    else if (auto MaterialPtr = Cast<UMaterial>(&MaterialInterface); MaterialPtr)
    {
        // FString FxRPath(Input->RelativePath);
        // FxRPath.ReplaceInline(TEXT(".mtl"), TEXT(".fx"));

        // FAssetExchangeInput InputNew{Input->Options, Input->Sdk, StringMapper::Instan
		// ce().FromUEToCEString(*FxRPath)};
        // return ExportFuncs::ExportFxOrMaterialFunction(*MaterialPtr, &InputNew);

        return ExportFuncs::ExportFxOrMaterialFunction(Builder, *MaterialPtr, Input);
    }

    return false;
}
static void CollectTexturesFromMaterialFunction(UMaterialFunction* FuncPtr, TArray<UTexture*>& OutDependencyAssets)
{
    // Process all expressions in the function
    for (TObjectPtr FuncExprPtr : FuncPtr->GetExpressions())
    {
        if (auto* FuncTexSamplePtr = Cast<UMaterialExpressionTextureSample>(FuncExprPtr.Get()))
        {
            auto Tex2D = (FuncTexSamplePtr->Texture);
            if (Tex2D)
            {
                OutDependencyAssets.AddUnique(Tex2D);
            }
        }
        else if (auto* FuncTexObjectPtr = Cast<UMaterialExpressionTextureObject>(FuncExprPtr.Get()))
        {
            auto Tex2D = (FuncTexObjectPtr->Texture);
            if (Tex2D)
            {
                OutDependencyAssets.AddUnique(Tex2D);
            }
        }
        else if (auto* FuncTexSampleParamPtr = Cast<UMaterialExpressionTextureSampleParameter>(FuncExprPtr.Get()))
        {
            auto Tex2D = (FuncTexSampleParamPtr->Texture);
            if (Tex2D)
            {
                OutDependencyAssets.AddUnique(Tex2D);
            }
        }
        // Handle nested material function calls
        else if (auto* NestedFuncCallPtr = Cast<UMaterialExpressionMaterialFunctionCall>(FuncExprPtr.Get()))
        {
            if (auto* NestedFuncPtr = Cast<UMaterialFunction>(NestedFuncCallPtr->MaterialFunction.Get()))
            {
                // Recursively collect textures from nested material functions
                CollectTexturesFromMaterialFunction(NestedFuncPtr, OutDependencyAssets);
            }
        }
    }
}
// Function to collect all textures used in a material
void CollectAllMaterialTextures(const UMaterialInterface* MaterialInterface, TArray<UTexture*>& OutDependencyAssets)
{
    // If it's a material instance, check its parent and parameter values
    if (auto* MaterialInstance = Cast<UMaterialInstance>(MaterialInterface))
    {
        // Recursively collect textures from parent material
        if (MaterialInstance->Parent)
        {
            CollectAllMaterialTextures(MaterialInstance->Parent, OutDependencyAssets);
        }

        // Collect textures from parameter values
        TMap<FMaterialParameterInfo, FMaterialParameterMetadata> Parameters;
        MaterialInstance->GetAllParametersOfType(EMaterialParameterType::Texture, Parameters);

        for (const auto& [Info, Meta] : Parameters)
        {
            auto Tex2D = Cast<UTexture>(Meta.Value.AsTextureObject());
            if (Tex2D)
            {
                OutDependencyAssets.AddUnique(Tex2D);
            }
        }
    }
    // If it's a base material, check all expressions
    else if (auto* Material = Cast<UMaterial>(MaterialInterface))
    {
        for (TObjectPtr ExprPtr : Material->GetExpressions())
        {
            // Check for texture sample expressions
            if (auto* TexSamplePtr = Cast<UMaterialExpressionTextureSample>(ExprPtr.Get()))
            {
                auto Tex2D = (TexSamplePtr->Texture);
                if (Tex2D)
                {
                    OutDependencyAssets.AddUnique(Tex2D);
                }
            }
            // Check for texture object expressions
            else if (auto* TexObjectPtr = Cast<UMaterialExpressionTextureObject>(ExprPtr.Get()))
            {
                auto Tex2D = (TexObjectPtr->Texture);
                if (Tex2D)
                {
                    OutDependencyAssets.AddUnique(Tex2D);
                }
            }
            // Check for texture sample parameter expressions
            else if (auto* TexSampleParamPtr = Cast<UMaterialExpressionTextureSampleParameter>(ExprPtr.Get()))
            {
                auto Tex2D = (TexSampleParamPtr->Texture);
                if (Tex2D)
                {
                    OutDependencyAssets.AddUnique(Tex2D);
                }
            }
            // Check for material function calls that might contain textures
            else if (auto* FuncCallPtr = Cast<UMaterialExpressionMaterialFunctionCall>(ExprPtr.Get()))
            {
                if (auto* FuncPtr = Cast<UMaterialFunction>(FuncCallPtr->MaterialFunction.Get()))
                {
                    CollectTexturesFromMaterialFunction(FuncPtr, OutDependencyAssets);
                }
            }
        }
    }
}


}   // namespace CEAssetExchange

#undef LOCTEXT_NAMESPACE