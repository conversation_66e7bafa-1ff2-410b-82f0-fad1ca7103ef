#include "CELandscapeActorExporter.h"
#include "Landscape.h"
#include "Builders/CEConvertBuilder.h"
#include "Exchange/ExportWorld.h"
#include "AssetExchange/Include/SDKComponent/SDKTerrainComponent.h"
#include "AssetExchange/Include/SDKComponent/SDKPhysicsComponent.h"
#include "UE2CEConnectorUtils/UE2CEConnectorUtils.h"
#include "Exchange/StringExchange.h"
#include "ImageUtils.h"
#include "IImageWrapper.h"

UCELandscapeActorExporter::UCELandscapeActorExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = ALandscape::StaticClass();
}

UCELandscapeActorExporter::AddEntityFromActorRetType UCELandscapeActorExporter::AddEntityFromActor(
    FCEConvertBuilder& InBuilder,
    CEAssetExchange::IWorldAssemble* InWorldAssemble,
    USceneComponent* InSceneComponent,
    CEAssetExchange::IEntity* LevelEntity,
    AActor* InExportActor,
    CEAssetExchange::FAssetExchangeInput* InInput)
{
    ULandscapeComponent* Landscape = Cast<ULandscapeComponent>(InExportActor->GetComponentByClass(ULandscapeComponent::StaticClass()));
    return {InBuilder.AddEntityFromSceneComponent(InWorldAssemble, InSceneComponent, LevelEntity, Landscape, InInput), false};
}

cesdk::cegf::SDKGameObject UCELandscapeActorExporter::AddGameObjectFromActor(FCEConvertBuilder& InBuilder,
                                                                             ::cesdk::cegf::SDKGameWorld InGameWorld,
                                                                             ::cesdk::cegf::SDKGameObject InParentGameObject,
                                                                             AActor* InExportActor)
{
    auto RootComponent = InExportActor->GetRootComponent();
    auto LandscapeActor = Cast<ALandscape>(InExportActor);
    if (!RootComponent || !LandscapeActor)
    {
        return InParentGameObject;
    }

    auto LandscapeInfo = LandscapeActor->GetLandscapeInfo();
    if (!LandscapeInfo)
    {
        return InParentGameObject;
    }

    const auto Options = InBuilder.GetOptions();
    auto SelectTerrainGridSize = [Options](int32 Size)
    {
        int32 Target = 1;
        int32 Dist = MAX_int32;
        for (int32 Index = FMath::Clamp(Options->MaxTerrainGridSize, 1, 8); Index != 0; Index--)
        {
            auto D = FMath::Abs(Index * StaticCast<int32>(FMath::RoundUpToPowerOfTwo(Size / Index)) - Size);
            if (D < Dist)
            {
                Target = Index;
                Dist = D;
            }
        }
        return Target;
    };

    FIntRect LandscapeExtent;
    LandscapeInfo->GetLandscapeExtent(LandscapeExtent);

    FIntRect LandscapeXYComponentBounds;
    LandscapeInfo->GetLandscapeXYComponentBounds(LandscapeXYComponentBounds);

    auto LandscapeXYComponentOffset = LandscapeXYComponentBounds.Min;
    auto LandscapeSizeSections = FIntVector2(LandscapeExtent.Width(), LandscapeExtent.Height()) / LandscapeInfo->SubsectionSizeQuads;
    auto LandscapeGridSize = FIntVector2(SelectTerrainGridSize(LandscapeSizeSections.X), SelectTerrainGridSize(LandscapeSizeSections.Y));
    auto LandscapeBlockSize = FMath::Max(FMath::RoundUpToPowerOfTwo(LandscapeSizeSections.X / LandscapeGridSize.X), FMath::RoundUpToPowerOfTwo(LandscapeSizeSections.Y / LandscapeGridSize.Y));
    auto LandscapeTileSize = LandscapeInfo->SubsectionSizeQuads + 1;
    auto LandscapeSizeBlocks = LandscapeGridSize * LandscapeBlockSize;
    auto LandscapeLevels = FMath::FloorLog2NonZero(LandscapeBlockSize) + 1;
    auto ComponentNumSubsections = LandscapeInfo->ComponentNumSubsections;

    FString GameObjectName = FUE2CEConnectorUtils::GetSceneComponentName(RootComponent);
    FTransform CETransform = FUE2CEConnectorUtils::ConvertUETransformToCE(RootComponent->GetRelativeTransform());
    auto T = CETransform.GetTranslation();
    auto R = CETransform.GetRotation();
    auto S = CETransform.GetScale3D();

    T.Z -= (LandscapeSizeBlocks.Y + LandscapeXYComponentBounds.Min.Y) * LandscapeTileSize * 100.f;

    cesdk::cegf::SDKGameObject TerrainGameObject = InGameWorld.CreateGameObject(CEAssetExchange::StringMapper::Instance().FromUEToCEString(*GameObjectName),
        InParentGameObject,
        { T.X, T.Y, T.Z },
        { R.X, R.Y, R.Z, R.W },
        { S.X, S.Y, S.Z });

    constexpr auto MaxNumWeightTextures = 2;
    TArray<TArray64<uint8>> LevelHeightData;
    TStaticArray<TArray<TArray64<uint8>>, MaxNumWeightTextures> LevelWeightData;
    for (auto Level = 0; Level != LandscapeLevels; Level++)
    {
        auto Width = LandscapeSizeBlocks.X * LandscapeTileSize / (1 << Level) + 1;
        auto Height = LandscapeSizeBlocks.Y * LandscapeTileSize / (1 << Level) + 1;
        LevelHeightData.AddDefaulted();
        LevelHeightData.Last().Init(0, Width * Height * 4);
        for (auto W = 0; W != MaxNumWeightTextures; W++)
        {
            LevelWeightData[W].AddDefaulted();
            LevelWeightData[W].Last().Init(0, Width * Height * 4);
        }
    }

    //auto LandscapeVertexSize = LandscapeSizeBlocks * LandscapeTileSize;
    //Transform.mScale[0] *= StaticCast<float>(LandscapeVertexSize.X) / LandscapeExtent.Width();
    //Transform.mScale[2] *= StaticCast<float>(LandscapeVertexSize.Y) / LandscapeExtent.Height();

    FImage HeightSourceData;
    TStaticArray<FImage, MaxNumWeightTextures> WeightSourceData;
    int32 NumWeightmapLayers = 0;
    for (const auto& Pair : LandscapeInfo->XYtoComponentMap)
    {
        auto XY = Pair.Key - LandscapeXYComponentOffset;
        auto Component = Pair.Value;

        auto Heightmap = Component->GetHeightmap();
        auto& HeightSource = Heightmap->Source;
        const auto HeightScaleBias = Component->HeightmapScaleBias;
        const auto HeightSourceSizeX = HeightSource.GetSizeX();
        const auto HeightSourceSizeY = HeightSource.GetSizeY();
        const auto HeightSourceOffsetX = FMath::RoundToInt(HeightSourceSizeX * HeightScaleBias.Z);
        const auto HeightSourceOffsetY = FMath::RoundToInt(HeightSourceSizeY * HeightScaleBias.W);
        HeightSource.GetMipImage(HeightSourceData, 0);

        const auto& WeightMaps = Component->GetWeightmapTextures();
        for (auto W = 0; W != FMath::Min(MaxNumWeightTextures, WeightMaps.Num()); W++)
        {
            auto& WeightSource = WeightMaps[W]->Source;
            WeightSource.GetMipImage(WeightSourceData[W], 0);
        }

        const auto& WeightmapLayerAllocations = Component->GetWeightmapLayerAllocations();
        NumWeightmapLayers = FMath::Max(NumWeightmapLayers, WeightmapLayerAllocations.Num());

        auto ComponentSize = (LandscapeInfo->SubsectionSizeQuads + 1) * ComponentNumSubsections;
        for (auto Y = 0; Y != ComponentSize; Y++)
        {
            for (auto X = 0; X != ComponentSize; X++)
            {
                auto HeightData = FColor(*StaticCast<uint32*>(HeightSourceData.GetPixelPointer(HeightSourceOffsetX + X, HeightSourceOffsetY + Y)));
                Swap(HeightData.R, HeightData.G);
                auto HeightDest = ((LandscapeSizeBlocks.Y * LandscapeTileSize - XY.Y * (ComponentSize - 1) - Y) * (LandscapeSizeBlocks.X * LandscapeTileSize + 1)
                    + XY.X * (ComponentSize - 1) + X) * HeightSourceData.GetBytesPerPixel();
                FPlatformMemory::Memcpy(LevelHeightData[0].GetData() + HeightDest, &HeightData, HeightSourceData.GetBytesPerPixel());

                for (auto W = 0; W != WeightmapLayerAllocations.Num(); W++)
                {
                    auto TextureIndex = WeightmapLayerAllocations[W].WeightmapTextureIndex;
                    auto ChannelIndex = WeightmapLayerAllocations[W].WeightmapTextureChannel;
                    TStaticArray<uint8, 4> ChannelMap = { 2, 1, 0, 3 };
                    auto WeightData = StaticCast<uint8*>(WeightSourceData[TextureIndex].GetPixelPointer(X, Y))[ChannelMap[ChannelIndex]];
                    auto WeightDest = ((LandscapeSizeBlocks.Y * LandscapeTileSize - XY.Y * (ComponentSize - 1) - Y) * (LandscapeSizeBlocks.X * LandscapeTileSize + 1)
                        + XY.X * (ComponentSize - 1) + X) * WeightSourceData[TextureIndex].GetBytesPerPixel();
                    auto Weight = LevelWeightData[W / 4][0].GetData() + WeightDest;
                    Weight[W % 4] = WeightData;
                }
            }
        }
    }

    for (auto Level = 1; Level != LandscapeLevels; Level++)
    {
        auto Width = LandscapeSizeBlocks.X * LandscapeTileSize / (1 << Level) + 1;
        auto Height = LandscapeSizeBlocks.Y * LandscapeTileSize / (1 << Level) + 1;

        for (auto Y = 0; Y != Height; Y++)
        {
            for (auto X = 0; X != Width; X++)
            {
                FPlatformMemory::Memcpy(LevelHeightData[Level].GetData() + (Y * Width + X) * HeightSourceData.GetBytesPerPixel(), LevelHeightData[Level - 1].GetData() +
                    (Y * (2 * Width - 1) + X) * 2 * HeightSourceData.GetBytesPerPixel(), HeightSourceData.GetBytesPerPixel());
                for (auto W = 0; W != MaxNumWeightTextures; W++)
                {
                    FPlatformMemory::Memcpy(LevelWeightData[W][Level].GetData() + (Y * Width + X) * WeightSourceData[W].GetBytesPerPixel(),
                        LevelWeightData[W][Level - 1].GetData() + (Y * (2 * Width - 1) + X) * 2 * WeightSourceData[W].GetBytesPerPixel(),
                        WeightSourceData[W].GetBytesPerPixel());
                }
            }
        }
    }

    auto SaveToFile = [&](FStringView OutputPath, const FImage& TileData, UTexture2D* Texture)
    {
        TArray<TArray<uint8>> MipSourceData;
        MipSourceData.Add(TArray<uint8>(TileData.RawData.GetData(), TileData.RawData.Num()));

        TArray<CEAssetExchange::MipMapData> MipDataArray;
        {
            CEAssetExchange::MipMapData MipData;
            MipData.SizeX = TileData.SizeX;
            MipData.SizeY = TileData.SizeY;
            MipData.mData = MipSourceData[0].GetData();
            MipData.mDataNum = MipSourceData[0].Num();
            MipDataArray.Add(MoveTemp(MipData));
        }

        auto NumMips = FMath::FloorLog2NonZero(LandscapeTileSize + 1) + 1;
        for (auto Mip = 1, ParentMipSize = LandscapeTileSize + 1; Mip != NumMips; Mip++)
        {
            TArray<uint8> ThisMipData;
            auto ThisMipSize = ParentMipSize / 2;
            const auto& ParentMipData = MipSourceData[Mip - 1];
            for (auto Y = 0; Y != ThisMipSize; Y++)
            {
                for (auto X = 0; X != ThisMipSize; X++)
                {
                    auto TCoords = ThisMipSize == 0 ? FVector2f(0) : FVector2f(X, Y) / (ThisMipSize - 1.f);
                    auto PCoords = TCoords * (ParentMipSize - 1.f);
                    auto ICoords = PCoords.IntPoint();
                    ThisMipData.Add(ParentMipData[(ICoords.Y * ParentMipSize + ICoords.X) * 4 + 0]);
                    ThisMipData.Add(ParentMipData[(ICoords.Y * ParentMipSize + ICoords.X) * 4 + 1]);
                    ThisMipData.Add(ParentMipData[(ICoords.Y * ParentMipSize + ICoords.X) * 4 + 2]);
                    ThisMipData.Add(ParentMipData[(ICoords.Y * ParentMipSize + ICoords.X) * 4 + 3]);
                }
            }
            MipSourceData.Add(MoveTemp(ThisMipData));

            CEAssetExchange::MipMapData MipData;
            MipData.SizeX = ThisMipSize;
            MipData.SizeY = ThisMipSize;
            MipData.mData = MipSourceData[Mip].GetData();
            MipData.mDataNum = MipSourceData[Mip].Num();
            MipDataArray.Add(MoveTemp(MipData));

            ParentMipSize = ThisMipSize;
        }

        CEAssetExchange::MipMapArray MipMapArray;
        MipMapArray.Format = CEAssetExchange::EPixelFormat::PF_R8G8B8A8;
        MipMapArray.MipMap = MipDataArray.GetData();
        MipMapArray.MipMapNum = static_cast<uint32_t>(MipDataArray.Num());
        MipMapArray.ColorSpace = CEAssetExchange::ImportColorSpace::Linear;

        auto SDK = InBuilder.AssetExchangeSdk.get();
        auto CETexture = SDK->CreateTextureAssemble(CEAssetExchange::StringMapper::Instance().FromUEToCEString(OutputPath.GetData()));
        CEAssetExchange::AdjustmentSettings AdjustSettings;
        AdjustSettings.brightness = Texture->AdjustBrightness;
        AdjustSettings.brightnesscurve = Texture->AdjustBrightnessCurve;
        AdjustSettings.vibrance = Texture->AdjustVibrance;
        AdjustSettings.saturation = Texture->AdjustSaturation;
        AdjustSettings.hue = Texture->AdjustHue;
        AdjustSettings.minalpha = Texture->AdjustMinAlpha;
        AdjustSettings.maxalpha = Texture->AdjustMaxAlpha;
        CETexture->SetAdjustmentSettings(&AdjustSettings);

        CEAssetExchange::TextureImportSettingWrapper ImportSetting;
        ImportSetting.ColorSpace = CEAssetExchange::ImportColorSpace::Linear;
        ImportSetting.Type = CEAssetExchange::TextureType::ImageTexture;
        CETexture->SetTextureImportSetting(ImportSetting);
        CETexture->AsMipMapArray(&MipMapArray);
        CETexture->EndAssemble();
        CETexture->SaveToFile();
    };

    FString LandscapeActorName;
    LandscapeActor->GetName(LandscapeActorName);
    auto TerrainOutputPath = FPaths::Combine(TEXT("Contents"), LandscapeActorName);
    for (auto Level = 0; Level != LandscapeLevels; Level++)
    {
        auto BlockSizeX = LandscapeSizeBlocks.X / (1 << Level);
        auto BlockSizeY = LandscapeSizeBlocks.Y / (1 << Level);
        for (auto BlockIdY = 0; BlockIdY != BlockSizeY; BlockIdY++)
        {
            for (auto BlockIdX = 0; BlockIdX != BlockSizeX; BlockIdX++)
            {
                auto GX = BlockIdX / (LandscapeBlockSize / (1 << Level));
                auto GY = BlockIdY / (LandscapeBlockSize / (1 << Level));
                auto BX = BlockIdX % (LandscapeBlockSize / (1 << Level));
                auto BY = BlockIdY % (LandscapeBlockSize / (1 << Level));

                FImage TileData;
                auto ProcessTile = [&](const FImage& SourceData, const TArray<TArray64<uint8>>& LevelData, FStringView OutputPath, bool Reorder)
                {
                    TileData.Init(LandscapeTileSize + 1, LandscapeTileSize + 1, SourceData.Format);
                    for (auto TY = 0; TY != LandscapeTileSize + 1; TY++)
                    {
                        for (auto TX = 0; TX != LandscapeTileSize + 1; TX++)
                        {
                            auto Src = ((BlockIdY * LandscapeTileSize + TY) * (BlockSizeX * LandscapeTileSize + 1) + BlockIdX * LandscapeTileSize + TX) * SourceData.GetBytesPerPixel();
                            FPlatformMemory::Memcpy(TileData.GetPixelPointer(TX, TY), LevelData[Level].GetData() + Src, SourceData.GetBytesPerPixel());
                        }
                    }
                    //TileData.ChangeFormat(ERawImageFormat::BGRA8, EGammaSpace::Linear);

                    if (Options->bExportTextureSource)
                    {
                        auto SDK = InBuilder.AssetExchangeSdk.get();
                        auto StagingPath = SDK->GetStagingPath();
                        auto SourceOutputPath = FPaths::ChangeExtension(FPaths::ConvertRelativePathToFull(StagingPath, OutputPath.GetData()), "png");
                        FImageUtils::SaveImageAutoFormat(*SourceOutputPath, TileData, StaticCast<int32>(EImageCompressionQuality::Uncompressed));
                    }

                    if (Reorder)
                    {
                        for (auto Y = 0; Y != LandscapeTileSize + 1; Y++)
                        {
                            for (auto X = 0; X != LandscapeTileSize + 1; X++)
                            {
                                auto Data = StaticCast<uint8*>(TileData.GetPixelPointer(X, Y));
                                Swap(Data[0], Data[2]);
                            }
                        }
                    }
                };

                // TODO move to Texture Exporter?
                auto XY = FIntPoint(BlockIdX, BlockIdY) / ComponentNumSubsections + LandscapeXYComponentOffset;
                auto Component = LandscapeInfo->XYtoComponentMap[FInt32Point(FMath::Clamp(XY.X, LandscapeXYComponentBounds.Min.X, LandscapeXYComponentBounds.Max.X),
                                                                             FMath::Clamp(XY.Y, LandscapeXYComponentBounds.Min.Y, LandscapeXYComponentBounds.Max.Y))];
                {
                    auto Filename = FString::Format(TEXT("HM_{0}_{1}_{2}_{3}_{4}"), { GX, GY, Level, BX, BY });
                    auto OutputPath = FPaths::Combine(InBuilder.mRootPath, TerrainOutputPath, TEXT("HM"), Filename + TEXT(".nda"));
                    ProcessTile(HeightSourceData, LevelHeightData, OutputPath, true);
                    SaveToFile(OutputPath, TileData, Component->GetHeightmap());
                }

                for (auto W = 0; W != FMath::Min(MaxNumWeightTextures, Component->GetWeightmapTextures().Num()); W++)
                {
                    auto Filename = FString::Format(TEXT("WT{0}_{1}_{2}_{3}_{4}_{5}"), { W, GX, GY, Level, BX, BY });
                    auto OutputPath = FPaths::Combine(InBuilder.mRootPath, TerrainOutputPath, TEXT("WT"), Filename + TEXT(".nda"));
                    ProcessTile(WeightSourceData[W], LevelWeightData[W], OutputPath, false);
                    SaveToFile(OutputPath, TileData, Component->GetWeightmapTextures()[W]);
                }
            }
        }
    }

    FString TerrainMaterialPath;
    if (auto MaterialInstance = LandscapeActor->GetLandscapeMaterial())
    {
        TerrainMaterialPath = InBuilder.GetCEContentPath(MaterialInstance);
    }

    auto CETerrainResource = cesdk::resource::SDKTerrainResource(TCHAR_TO_UTF8(*FPaths::Combine(InBuilder.mRootPath, TerrainOutputPath, TEXT("Terrain.nda"))));
    CETerrainResource.SetTerrainSize(LandscapeGridSize.X, LandscapeGridSize.Y, LandscapeBlockSize, LandscapeTileSize, 1.f);
    CETerrainResource.SetTerrainPath(TCHAR_TO_UTF8(*TerrainOutputPath), TCHAR_TO_UTF8(TEXT("HM/HM")), TCHAR_TO_UTF8(TEXT("WT/WT")), TCHAR_TO_UTF8(*TerrainMaterialPath), NumWeightmapLayers);
    CETerrainResource.Serialize();

    auto CEComponent = TerrainGameObject.AddComponent("cegf::TerrainComponent");
    cesdk::cegf::SDKTerrainComponent CETerrainComponent(CEComponent.componentInstance);
    CETerrainComponent.SetTerrainPath(TCHAR_TO_UTF8(*FPaths::Combine(InBuilder.mRootPath, TerrainOutputPath, TEXT("Terrain.nda"))));
    if (LandscapeActor->BodyInstance.GetCollisionEnabled() != ECollisionEnabled::NoCollision)
    {
        cesdk::cegf::SDKPhysicsComponent physicsComp(TerrainGameObject.AddComponent("cegf::PhysicsComponent").componentInstance);
        physicsComp.SetCollisionType(cesdk::cegf::CollisionType::WorldStatic);
        physicsComp.SetCollisionMask(0xffff);
    }
    return TerrainGameObject;
}