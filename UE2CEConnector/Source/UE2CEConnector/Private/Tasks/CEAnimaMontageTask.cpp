#include "CEAnimaMontageTask.h"

#include "CELog.h"
#include "Builders/CEConvertBuilder.h"
#include "Converters/CEAnimationCommon.h"


FCEAnimaMontageTask::FCEAnimaMontageTask(const FString& savePath, FCEConvertBuilder& builder, const UAnimMontage* AnimMontage)
    : FCETask(ECETaskPriority::Animation, savePath, builder, AnimMontage)
      , AnimMontage(AnimMontage) {}

FString FCEAnimaMontageTask::GetName() const
{
    return FString::Printf(TEXT("Exporting Animation Mongtage: %s"), *AnimMontage->GetName());
}

bool FCEAnimaMontageTask::CollectDependencyTask()
{
    if (!AnimMontage)
    {
        return false;
    }

    // Get AnimSeq
    for (auto& AnimTrack : AnimMontage->SlotAnimTracks)
    {
        for (auto& Segment : AnimTrack.AnimTrack.AnimSegments)
        {
            mDependencies.Add(Segment.AnimReference);
            mBuilder.GetOrAdd(Segment.AnimReference.Get());
        }
    }
    return true;
}

bool FCEAnimaMontageTask::Complete()
{
    if (!AnimMontage)
    {
        CE_Log(TEXT("Invalid Animation Mongtage for export"));
        return false;
    }

    // Get the reference skeleton from the animation
    USkeleton* Skeleton = AnimMontage->GetSkeleton();
    if (!Skeleton)
    {
        CE_Log(TEXT("Animation Sequence %s has no valid skeleton"), *AnimMontage->GetName());
        return false;
    }
    // Get animation information
    const FString AnimName = AnimMontage->GetName();
    const float Duration = AnimMontage->GetPlayLength();
    const int32 NumFrames = AnimMontage->GetNumberOfFrames();

    //TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    FCEAnimMontage CEAnimMontage;
    CEAnimMontage.GroupName = AnimMontage->GetGroupName().ToString();

    // Get Anim Sections
    TArray<FCEAnimSlotTrackSection> CEAnimMontageSections;
    for (auto& Section : AnimMontage->CompositeSections)
    {
        FCEAnimSlotTrackSection CESection;
        CESection.Name = Section.SectionName.ToString();
        CESection.Loop = false;
        CESection.SegmentStartIndex = Section.GetSegmentIndex();
        CESection.SegmentEndIndex = -1;

        if (!CEAnimMontageSections.IsEmpty())
        {
            CEAnimMontageSections.Last().SegmentEndIndex = CESection.SegmentStartIndex;
        }
        CEAnimMontageSections.Push(CESection);
    }

    // Get Slot AnimTracks
    for (auto& AnimTrack : AnimMontage->SlotAnimTracks)
    {
        FCEAnimSlotTrack CETrack;
        CETrack.SlotName = AnimTrack.SlotName.ToString();
        for (auto& Segment : AnimTrack.AnimTrack.AnimSegments)
        {
            FCEAnimSlotTrackSegment CESegement;
            CESegement.ReltvPath = mBuilder.GetCEContentPath(Segment.AnimReference.Get());
            CESegement.Name = FPaths::GetBaseFilename(CESegement.ReltvPath);
            CESegement.StartPos = Segment.AnimStartTime;
            CESegement.EndPos = Segment.AnimEndTime;
            CESegement.PlayRate = Segment.AnimPlayRate;
            CESegement.LoopingCount = Segment.LoopingCount;
            CETrack.Segments.Push(CESegement);
        }
        CETrack.Sections = CEAnimMontageSections;
        CEAnimMontage.AnimTracks.Push(CETrack);
    }

    // Get Anim Notify
    for (auto& NotifyTrack : AnimMontage->AnimNotifyTracks)
    {
        FCEAnimNotifyTrack CENotifyTrack;
        CENotifyTrack.Name = NotifyTrack.TrackName.ToString();
        for (auto* Notify : NotifyTrack.Notifies)
        {
            FCEAnimNotify CENotify;
            CENotify.Name = Notify->NotifyName.ToString();
            CENotify.TriggerTimeInSec = Notify->GetTriggerTime();
            CENotify.EndTriggerTimeInSec = Notify->GetEndTriggerTime();
            CENotify.TriggerWeightThreshold = Notify->TriggerWeightThreshold;
            CENotifyTrack.Notifies.Push(std::move(CENotify));
        }
        CEAnimMontage.NotifyTracks.Push(std::move(CENotifyTrack));
    }

    // Get curve data
    AnimMontage->GetCurveData();

    TSharedRef<FJsonObject> JsonObject = CEAnimMontage.ToJsonObject();
    FString JsonCEAnimMontage;
    auto JsonWriter = TJsonWriterFactory<TCHAR, TPrettyJsonPrintPolicy<TCHAR>>::Create(&JsonCEAnimMontage, 0);
    bool bSuccess = FJsonSerializer::Serialize(JsonObject, JsonWriter);
    JsonWriter->Close();
    FString AnimMontageExportPath = FPaths::Combine(mBuilder.mRootPath, mBuilder.GetCEContentPath(AnimMontage));

    //FFileHelper::SaveStringToFile(JsonCEAnimMontage, *(AnimMontageExportPath.Replace(TEXT(".nda"), TEXT(".animatrix"))));

    auto res = cesdk::resource::SDKAnimatrixResource(TCHAR_TO_UTF8(*AnimMontageExportPath));
    res.Init(TCHAR_TO_UTF8(*JsonCEAnimMontage));
    res.Serialize();
    return true;
}