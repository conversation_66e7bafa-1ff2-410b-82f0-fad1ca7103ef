#pragma once
#include "CEBaseActorExporter.h"
#include "CELandscapeActorExporter.generated.h"

UCLASS()
class UCELandscapeActorExporter : public UCEBaseActorExporter
{
public:
    GENERATED_UCLASS_BODY()
    virtual AddEntityFromActorRetType AddEntityFromActor(FCEConvertBuilder& InBuilder,
                                                         CEAssetExchange::IWorldAssemble* InWorldAssemble,
                                                         USceneComponent* InSceneComponent,
                                                         CEAssetExchange::IEntity* LevelEntity,
                                                         AActor* InExportActor,
                                                         CEAssetExchange::FAssetExchangeInput* InInput) override;

    virtual cesdk::cegf::SDKGameObject AddGameObjectFromActor(FCEConvertBuilder& InBuilder,
                                                              ::cesdk::cegf::SDKGameWorld InGameWorld,
                                                              ::cesdk::cegf::SDKGameObject InParentGameObject,
                                                              AActor* InExportActor) override;
};