#pragma once
#include "CoreMinimal.h"
#include "UObject/GCObject.h"
#include "CEBaseConverter.generated.h"

class FCEConvertBuilder;

UCLASS(abstract)
class UE2CECONNECTOR_API UCEBaseConverter : public UObject
{
    GENERATED_UCLASS_BODY()
public:
    /**
     * Increments the conversion counter and calls the Convert function.
     * 
     * @param Builder - The conversion builder to use
     * @param SavePath - The path where the converted file will be saved
     * @param Object - The object to convert
     */
    void GetOrAdd(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object)
    {
        ConvertNum++;
        Convert(Builder, SavePath, Object);
    }

    /**
     * Checks if this converter supports the given object.
     * 
     * @param Object - The object to check support for
     * @return True if this converter supports the object, false otherwise
     */
    bool SupportsObject(const UObject* Object) const
    {
        return (SupportedClass && Object->IsA(SupportedClass));
    }
    
    UPROPERTY()
    TSubclassOf<class UObject>  SupportedClass;

    /**
     * Gets the content path for the given object in the CE format.
     * 
     * @param Object - The UObject to get the CE content path for
     * @return The CE content path as a string
     */
    FString GetCEContentPath(const UObject* Object);

    /**
     * Gets the number of objects that have been converted by this converter.
     * 
     * @return The number of conversions performed
     */
    int32 GetConvertNum() const {return ConvertNum;}
protected:
    int32 ConvertNum;
    
    /**
     * Gets the file extension for the CE file that will be generated from the UObject.
     * 
     * @param Object - The source UObject to convert
     * @return The file extension string (defaults to ".nda")
     */
    virtual FString GetCEFileExtension(const UObject* Object) { return TEXT(".nda"); }
    /**
     * Converts a UE object to CE format and adds it to the builder.
     * This is the main conversion function that derived classes must implement.
     * 
     * @param Builder - The conversion builder that manages the conversion process
     * @param SavePath - The path where the converted files will be saved
     * @param Object - The UObject to be converted
     */
    virtual void Convert(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object) {}
    friend class FCEConvertBuilder;
};
