#include "Converters/CEWidgetBlueprintConverter.h"
#include <Tasks/CEWidgetBlueprintTask.h>

UCEWidgetBlueprintConverter::UCEWidgetBlueprintConverter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UWidgetBlueprint::StaticClass();
}

void UCEWidgetBlueprintConverter::Convert(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object)
{
    UWidgetBlueprint* WidgetBlueprint = Cast<UWidgetBlueprint>(Object);
    if (WidgetBlueprint)
    {
        Builder->SetupTask<FCEWidgetBlueprintTask>(SavePath, *Builder, WidgetBlueprint); 
    }
}

FString UCEWidgetBlueprintConverter::GetCEFileExtension(const UObject* Object)
{
    return TEXT(".json");
}
