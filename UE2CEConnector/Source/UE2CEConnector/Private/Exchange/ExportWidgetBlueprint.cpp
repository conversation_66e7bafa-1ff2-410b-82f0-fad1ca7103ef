#include "CEAssetExchange.h"

#include <Exchange/StringExchange.h>
#include <Components/Widget.h>
#include <Components/PanelWidget.h>
#include <Components/CanvasPanelSlot.h>
#include <Components/HorizontalBoxSlot.h>
#include <Components/VerticalBoxSlot.h>
#include <Components/GridSlot.h>
#include <Components/BorderSlot.h>
#include <Components/OverlaySlot.h>
#include <Components/SizeBoxSlot.h>
#include <Components/ScaleBoxSlot.h>
#include <Components/StackBoxSlot.h>
#include <CELog.h>

#include <string>
#include <cmath>
#include <vector>


#define LOCTEXT_NAMESPACE "CEAssetExchange"

static FString GenerateSlotString(UPanelSlot const*const slot)
{
	FString slotString = TEXT("{}");

	if(slot == nullptr)
	{
		slotString = TEXT("{\"type\": \"Null\"}");
	}
	else if (UCanvasPanelSlot const* const cpSlot = Cast<UCanvasPanelSlot>(slot))
	{
		slotString = TEXT("{\"type\": \"CanvasPanel\"}"); // HERE
	}
	else if (UHorizontalBoxSlot const* const hSlot = Cast<UHorizontalBoxSlot>(slot))
	{
		FMargin const & padding = hSlot->GetPadding();
		EHorizontalAlignment const & hAlignment = hSlot->GetHorizontalAlignment();
		EVerticalAlignment const & vAlignment = hSlot->GetVerticalAlignment();
		slotString = FString::Printf(TEXT
				(
				"{"
				"\"type\": \"HorizontalBox\","
				"\"padding\": {\"left\": %f, \"right\": %f, \"top\": %f, \"bottom\": %f},"
				"\"horizontal_alignment\": %u,"
				"\"vertical_alignment\": %u"
				"}"
				)
				, padding.Left, padding.Right, padding.Bottom, padding.Top
				, hAlignment, vAlignment
			);
	}
	else if (UVerticalBoxSlot const* const vSlot = Cast<UVerticalBoxSlot>(slot))
	{
		FMargin const & padding = vSlot->GetPadding();
		EHorizontalAlignment const & hAlignment = vSlot->GetHorizontalAlignment();
		EVerticalAlignment const & vAlignment = vSlot->GetVerticalAlignment();
		slotString = FString::Printf(TEXT
				(
				"{"
				"\"type\": \"VerticalBoxSlot\","
				"\"padding\": {\"left\": %f, \"right\": %f, \"top\": %f, \"bottom\": %f},"
				"\"horizontal_alignment\": %u,"
				"\"vertical_alignment\": %u"
				"}"
				)
				, padding.Left, padding.Right, padding.Bottom, padding.Top
				, hAlignment, vAlignment
			);
	}
	else if (UGridSlot const* const gSlot = Cast<UGridSlot>(slot))
	{
		FMargin const & padding = gSlot->GetPadding();
		EHorizontalAlignment const & hAlignment = gSlot->GetHorizontalAlignment();
		EVerticalAlignment const & vAlignment = gSlot->GetVerticalAlignment();
		slotString = FString::Printf(TEXT
				(
				"{"
				"\"type\": \"GridSlot\","
				"\"padding\": {\"left\": %f, \"right\": %f, \"top\": %f, \"bottom\": %f},"
				"\"horizontal_alignment\": %u,"
				"\"vertical_alignment\": %u,"
				"\"grid\": {\"column\": %d, \"row\": %d},"
				"\"span\": {\"column\": %d, \"row\": %d}"
				"}"
				)
				, padding.Left, padding.Right, padding.Bottom, padding.Top
				, hAlignment, vAlignment
				, gSlot->GetColumn(), gSlot->GetRow()
				, gSlot->GetColumnSpan(), gSlot->GetRowSpan()
			);
	}
	else if (UBorderSlot const* const bSlot = Cast<UBorderSlot>(slot))
	{
		FMargin const & padding = bSlot->GetPadding();
		EHorizontalAlignment const & hAlignment = bSlot->GetHorizontalAlignment();
		EVerticalAlignment const & vAlignment = bSlot->GetVerticalAlignment();
		slotString = FString::Printf(TEXT
				(
				"{"
				"\"type\": \"BorderSlot\","
				"\"padding\": {\"left\": %f, \"right\": %f, \"top\": %f, \"bottom\": %f},"
				"\"horizontal_alignment\": %u,"
				"\"vertical_alignment\": %u"
				"}"
				)
				, padding.Left, padding.Right, padding.Bottom, padding.Top
				, hAlignment, vAlignment
			);
	}
	else if (UOverlaySlot const* const olSlot = Cast<UOverlaySlot>(slot))
	{
		FMargin const & padding = olSlot->GetPadding();
		EHorizontalAlignment const & hAlignment = olSlot->GetHorizontalAlignment();
		EVerticalAlignment const & vAlignment = olSlot->GetVerticalAlignment();
		slotString = FString::Printf(TEXT
				(
				"{"
				"\"type\": \"OverlaySlot\","
				"\"padding\": {\"left\": %f, \"right\": %f, \"top\": %f, \"bottom\": %f},"
				"\"horizontal_alignment\": %u,"
				"\"vertical_alignment\": %u"
				"}"
				)
				, padding.Left, padding.Right, padding.Bottom, padding.Top
				, hAlignment, vAlignment
			);
	}
	else if (USizeBoxSlot const* const szbSlot = Cast<USizeBoxSlot>(slot))
	{
		FMargin const & padding = szbSlot->GetPadding();
		EHorizontalAlignment const & hAlignment = szbSlot->GetHorizontalAlignment();
		EVerticalAlignment const & vAlignment = szbSlot->GetVerticalAlignment();
		slotString = FString::Printf(TEXT
				(
				"{"
				"\"type\": \"SizeBoxSlot\","
				"\"padding\": {\"left\": %f, \"right\": %f, \"top\": %f, \"bottom\": %f},"
				"\"horizontal_alignment\": %u,"
				"\"vertical_alignment\": %u"
				"}"
				)
				, padding.Left, padding.Right, padding.Bottom, padding.Top
				, hAlignment, vAlignment
			);
	}
	else if (UScaleBoxSlot const* const scbSlot = Cast<UScaleBoxSlot>(slot))
	{
		EHorizontalAlignment const & hAlignment = scbSlot->GetHorizontalAlignment();
		EVerticalAlignment const & vAlignment = scbSlot->GetVerticalAlignment();
		slotString = FString::Printf(TEXT
				(
				"{"
				"\"type\": \"ScaleBoxSlot\","
				"\"horizontal_alignment\": %u,"
				"\"vertical_alignment\": %u"
				"}"
				)
				, hAlignment, vAlignment
			);
	}
	else if (UStackBoxSlot const* const stbSlot = Cast<UStackBoxSlot>(slot))
	{
		FMargin const & padding = stbSlot->GetPadding();
		EHorizontalAlignment const & hAlignment = stbSlot->GetHorizontalAlignment();
		EVerticalAlignment const & vAlignment = stbSlot->GetVerticalAlignment();
		slotString = FString::Printf(TEXT
				(
				"{"
				"\"type\": \"StackBoxSlot\","
				"\"padding\": {\"left\": %f, \"right\": %f, \"top\": %f, \"bottom\": %f},"
				"\"horizontal_alignment\": %u,"
				"\"vertical_alignment\": %u"
				"}"
				)
				, padding.Left, padding.Right, padding.Bottom, padding.Top
				, hAlignment, vAlignment
			);
	}
	//else if (UPanelSlot const* const pSlot = Cast<UPanelSlot>(slot)) //HERE
	//{
	//	slotString = FString::Printf(TEXT
	//			(
	//			"{"
	//			"\"type\": \"PanelSlot\","
	//			"}"
	//			)
	//		);
	//}
	else
	{
		slotString = TEXT("{\"type\": \"UnknownSlot\"}");
	}
	return slotString;
}

bool CEAssetExchange::ExportWidgetBlueprint(UWidgetBlueprint const& widgetBlueprint, const FAssetExchangeInput* input)
{
	IAssetExchangeSDK *sdk = input->Sdk;
	UCEExportOptions const* options = input->Options;

	FString outputFilePath;
	{
		char const* relativePath = input->RelativePath;
		FString frelativePath = UTF8_TO_TCHAR (relativePath);
		//FString fstagingPath = UTF8_TO_TCHAR (sdk->GetStagingPath());
		FString fstagingPath = UTF8_TO_TCHAR(sdk->GetStagingPath());
		outputFilePath = FPaths::Combine(fstagingPath, frelativePath);
	}

	if (!options->bOverwriteExistingWidgetBlueprint)
	{
		if (FPaths::FileExists(outputFilePath))
		{
			CE_Log(TEXT("CEAssetExchange: Skip Export \"%s\", due to file already exist"), *outputFilePath);
			return true;
		}
	}

	TArray<FString> widgetStrings;

	widgetStrings.Add("{\"widgets\":[");
	TArray<const class UWidget*> widgets = widgetBlueprint.GetAllSourceWidgets();
    int32 widgetIndex = 0;
	for(const class UWidget* widget : widgets)
	{
		FString const name = widget->GetName();
		FString const path = widget->GetPathName();
		FString const clazz = widget->GetClass()->GetName();
		FString const classPath = widget->GetClass()->GetPathName();

		class UPanelWidget const * const parent = widget->GetParent();
		FString const parentName = parent ? parent->GetName() : TEXT("NONE");

		FWidgetTransform const& transform = widget->GetRenderTransform();
		FVector2D const& pivot = widget->GetRenderTransformPivot();

		FString const slotString = GenerateSlotString(widget->Slot);

		FString const endingComma = widgetIndex < widgets.Num() - 1 ? TEXT(",") : TEXT("");

		widgetStrings.Add(FString::Printf(TEXT
			("{"
			"\"name\":\"%s\","
			//"\"path\":\"%s\","
			"\"type\":\"%s\","
			//"\"classPath\":\"%s\","
			"\"parent\":\"%s\","
			"\"slot\": %s,"
			"\"translation\": {" "\"x\": %f," "\"y\": %f" "},"
			"\"scale\": {" "\"x\": %f," "\"y\": %f" "},"
			"\"shear\": {" "\"x\": %f," "\"y\": %f" "},"
			"\"angle\": %f,"
			"\"pivot\": {" "\"x\": %f," "\"y\": %f" "}"
			"}%s")
			, *name
			//, *path
			, *clazz
			//, *classPath
			, *parentName
			, *slotString
			, transform.Translation.X, transform.Translation.Y
			, transform.Scale.X, transform.Scale.Y
			, transform.Shear.X, transform.Shear.Y
			, transform.Angle
			, pivot.X, pivot.Y
			, *endingComma
			));
		++widgetIndex;
	}
	widgetStrings.Add("]}");

	if(widgetStrings.IsEmpty())
	{
		CE_Log(TEXT("CEAssetExchange: Skip Export \"%s\", due to no widget found"), *outputFilePath);
		return true;
	}
	else
	{
		FFileHelper::SaveStringArrayToFile(widgetStrings, *outputFilePath);
	}

	return true;
}

#undef LOCTEXT_NAMESPACE
