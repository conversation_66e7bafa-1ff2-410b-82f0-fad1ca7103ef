#pragma once
#include "CETask.h"

class UAnimComposite;

class FCEAnimCompositeTask : public FCETask
{
private:
    const UAnimComposite* AnimComposite;

public:
    FCEAnimCompositeTask(const FString& savePath, FCEConvertBuilder& builder, const UAnimComposite* animComposite);

    virtual FString GetName() const override;

    virtual bool CollectDependencyTask() override;

    virtual bool Complete() override;
};