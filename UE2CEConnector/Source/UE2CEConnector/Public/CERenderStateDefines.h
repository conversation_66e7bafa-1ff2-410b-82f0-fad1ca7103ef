#pragma once

namespace cross
{

using SInt8 = std::int8_t;
using UInt8 = std::uint8_t;
using SInt16 = std::int16_t;
using UInt16 = std::uint16_t;
using SInt32 = std::int32_t;
using UInt32 = std::uint32_t;
using SInt64 = std::int64_t;
using UInt64 = std::uint64_t;
using SizeType = std::size_t;
using SSizeType = SInt64;

enum class FillMode
{
	Unknown,
	WireFrame,
	Solid,
};

enum class CullMode
{
	None,
	Front,
	Back,
	CM_None = None,
	CM_Front = Front,
	CM_Back = Back,
};

enum class FaceOrder
{
	CW = 1,
	CCW,
};

enum class ComparisonOp
{
	Unknown,
	Never,
	Less,
	Equal,
	LessEqual,
	Greater,
	NotEqual,
	GreaterEqual,
	Always,

	CMP_Never = Never,
	CMP_Less = Less,
	CMP_Equal = Equal,
	CMP_LessEqual = LessEqual,
	CMP_Greater = Greater,
	CMP_NotEqual = NotEqual,
	CMP_GreaterEqual = GreaterEqual,
	CMP_Always = Always,
};

enum class StencilOp
{
	Unknown,
	Keep,
	Zero,
	Replace,
	IncrementSaturate,
	DecrementSaturate,
	Invert,
	IncrementWarp,
	DecrementWarp,
};

enum class BlendOp
{
	Unknown,
	Add,
	Subtract,
	ReverseSubtract,
	Min,
	Max,
};

enum class BlendFactor
{
	Zero = 1,
	One = 2,
	SrcColor = 3,
	InvSrcColor = 4,
	SrcAlpha = 5,
	InvSrcAlpha = 6,
	DestAlpha = 7,
	InvDestAlpha = 8,
	DestColor = 9,
	InvDestColor = 10,
	SrcAlphaSaturate = 11, // (min(As0,1-Ad), 1)
	Constant = 12,
	InvConstant = 13,
	Src1Color = 14,
	InvSrc1Color = 15,
	Src1Alpha = 16,
	InvSrc1Alpha = 17
};

enum class LogicOp
{
	Clear = 1,
	Set = 2,
	Copy = 3,
	CopyInverted = 4,
	NoOp = 5,
	Invert = 6,
	And = 7,
	NAnd = 8,
	Or = 9,
	NOr = 10,
	XOr = 11,
	Equiv = 12,
	AndReverse = 13,
	AndInverted = 14,
	OrReverse = 15,
	OrInverted = 16
};

enum ColorComponentBit
{
	COLOR_COMPONENT_R = 1,
	COLOR_COMPONENT_G = 2,
	COLOR_COMPONENT_B = 4,
	COLOR_COMPONENT_A = 8,
	COLOR_COMPONENT_ALL = (((COLOR_COMPONENT_R | COLOR_COMPONENT_G) | COLOR_COMPONENT_B) | COLOR_COMPONENT_A)
};

enum PrimitiveType
{
	PrimType_Point = 1 << 0,
	PrimType_Line = 1 << 1,
	PrimType_Triangle = 1 << 2,
	PrimType_Patch = 1 << 3,
};


enum PrimitiveIndex
{
	PrimIndex_List = 1 << 6,
	PrimIndex_Strip = 1 << 7,
};

enum PrimitiveAdjacency
{
	PrimAdj_NoAdjacency = 0,
	PrimAdj_Adjacency = 1 << 9,
};

enum class PrimitiveTopology : UInt32
{
	Invalid,

	Point = PrimType_Point | PrimAdj_NoAdjacency,

	LineList = PrimType_Line | PrimIndex_List | PrimAdj_NoAdjacency,
	LineStrip = PrimType_Line | PrimIndex_Strip | PrimAdj_NoAdjacency,
	LineListAdj = PrimType_Line | PrimIndex_List | PrimAdj_Adjacency,
	LineStripAdj = PrimType_Line | PrimIndex_Strip | PrimAdj_Adjacency,

	TriangleList = PrimType_Triangle | PrimIndex_List | PrimAdj_NoAdjacency,
	TriangleStrip = PrimType_Triangle | PrimIndex_Strip | PrimAdj_NoAdjacency,
	TriangleListAdj = PrimType_Triangle | PrimIndex_List | PrimAdj_Adjacency,
	TriangleStripAdj = PrimType_Triangle | PrimIndex_Strip | PrimAdj_Adjacency,

	PatchList = PrimType_Patch | PrimIndex_List | PrimAdj_NoAdjacency,
};

enum class BuildinBlendState
{
	Opaque,
	Transparent,
	Overlay,

	Darken,
	Multiply,
	Lighten,
	Linear_Dodge,
	Subtract,

	// Keep Last
	Count
};

enum class BuildinColorWriteMask
{
	All,
	None,
	RGB,
	R,
	G,
	B,
	A,

	// Keep Last
	Count
};

enum class BuildinRasterizerState
{
	FrontFace,
	BackFace,
	DoubleFace,

	// Keep Last
	Count
};

enum class BuildinDepthState
{
	NearOrEqual_ZWrite,
	FarOrEqual_ZWrite,
	NearOrEqual_ZDiscard,
	FarOrEqual_ZDiscard,

	Near_ZWrite,
	Far_ZWrite,
	Near_ZDiscard,
	Far_ZDiscard,

	Always_ZWrite,
	Always_ZDiscard,

	Equal_ZWrite,
	Equal_ZDiscard,

	// Keep Last
	Count
};

enum class BuildinStencilState
{
	Disable,

	WriteZero,
	WriteReplace,
	WriteInvert,
	WriteIncrementClamp,
	WriteDecrementClamp,
	WriteIncrementWarp,
	WriteDecrementWarp,

	PassEqual,
	PassNotEqual,
	PassLess,
	PassLessEqual,
	PassGreater,
	PassGreaterEqual,

	// Keep Last
	Count
};

enum class BuildinSamplerState
{
	Clamp_Point,
	Clamp_Linear_Mip_Point,
	Clamp_Linear,

	Warp_Point,
	Warp_Linear_Mip_Point,
	Warp_Linear,

	Mirror_Point,
	Mirror_Linear_Mip_Point,
	Mirror_Linear,

	// Keep Last
	Count
};

//Unlike desc in rhi, those struct is simple and general..
//Do not change the order of the variables for the moment..
struct BlendStateDesc
{
	bool EnableBlend;
	bool EnableAlphaToCoverage;

	BlendOp BlendOpColor;
	BlendOp BlendOpAlpha;

	BlendFactor SrcBlendColor;
	BlendFactor DestBlendColor;
	BlendFactor SrcBlendAlpha;
	BlendFactor DestBlendAlpha;

	UInt32 ColorWriteMask;
};

struct RasterizerStateDesc
{
	FillMode RasterizerFillMode;
	CullMode FaceCullMode;
	FaceOrder FrontFaceOrder;

	bool EnableDepthBias;
	SInt16 Bias;
	float SlopeBias;
};

struct StencilOperation
{
	ComparisonOp StencilCompareOp;
	// The stencil operation to perform when stencil testing fails.
	StencilOp StencilFailOp;
	// The stencil operation to perform when stencil testing and depth testing both pass.
	StencilOp StencilPassOp;
	// The stencil operation to perform when stencil testing passes and depth testing fails.
	StencilOp StencilDepthFailOp;
};

struct DepthStencilStateDesc
{
	bool EnableDepth;
	bool EnableDepthWrite;
	ComparisonOp DepthCompareOp;

	bool EnableStencil;
	UInt8 StencilReadMask;
	UInt8 StencilWriteMask;
	StencilOperation FrontFaceStencil;
	StencilOperation BackFaceStencil;
};

}
