#include "Converters/CEMaterialFunctionConverter.h"
#include "Tasks/CEMaterialFunctionTask.h"
UCEMaterialFunctionConverter::UCEMaterialFunctionConverter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UMaterialFunction::StaticClass();
}

void UCEMaterialFunctionConverter::Convert(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object)
{
    UMaterialFunction* function = Cast<UMaterialFunction>(Object);
    if (function)
    {
        Builder->SetupTask<FCEMaterialFunctionTask>(*Builder, function, SavePath);
    }
}

FString UCEMaterialFunctionConverter::GetCEFileExtension(const UObject* Object)
{
    return TEXT(".materialfunction");
}
