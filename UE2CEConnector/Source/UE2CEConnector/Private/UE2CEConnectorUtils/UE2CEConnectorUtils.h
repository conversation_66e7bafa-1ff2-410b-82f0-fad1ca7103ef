#pragma once
#include "CoreMinimal.h"

class USceneComponent;
namespace cesdk::cegf{
    enum class CollisionType : uint16_t;
}

class FUE2CEConnectorUtils
{
public:
    static FString GetSceneComponentName(const USceneComponent* SceneComponent);
    static FTransform ConvertUETransformToCE(const FTransform& InFTransform);
    static cesdk::cegf::CollisionType ConvertUECollisionTypeToCE(ECollisionChannel ueCollisionChannel);
    static uint16_t ConvertUECollisionMaskToCE(const FBodyInstance* BodyInstance);
    static bool IsUECollisionTrigger(FName collisionProfile);
};
