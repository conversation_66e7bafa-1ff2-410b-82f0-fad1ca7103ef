<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>SDL 2.0</Name>
  <Location>/Engine/Source/ThirdParty/SDL2/</Location>
  <Date>2016-06-15T18:33:17.2339311-04:00</Date>
  <Function>Cross-platform library abstracting audio, video and input</Function>
  <Justification>Required for Linux client</Justification>
  <Eula>http://www.libsdl.org/license.php</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/SDL2.0_License.txt</LicenseFolder>
</TpsData>