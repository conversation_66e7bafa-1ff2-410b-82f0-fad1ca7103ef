#pragma once

#include "CoreMinimal.h"

/**
 * Utility functions for coordinate system conversion and other common operations
 * used by various converter classes.
 */
namespace CEConverterUtils
{
    /**
     * Converts a transform from Unreal Engine coordinate system to another coordinate system and vice versa.
     * This function applies a rotation to convert between coordinate systems while preserving the transform's properties.
     *
     * @param InTransform The input transform in UE coordinate system
     * @return The converted transform in target coordinate system
     */
    FTransform ConvertCoordinateSystem(const FTransform& InTransform);
} 