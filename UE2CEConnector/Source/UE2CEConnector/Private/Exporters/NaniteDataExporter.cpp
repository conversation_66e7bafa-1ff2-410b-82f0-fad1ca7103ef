#include "Exporters/NaniteDataExporter.h"
#include "Engine/StaticMesh.h"
#include "Rendering/NaniteResources.h"
#include "StaticMeshResources.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"

DEFINE_LOG_CATEGORY_STATIC(LogNaniteExporter, Log, All);

bool FNaniteDataExporter::ExportNaniteData(const UStaticMesh* StaticMesh, CEAssetExchange::NaniteExportData& OutExportData)
{
    if (!StaticMesh)
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("StaticMesh is null"));
        return false;
    }

    // 检查是否启用了Nanite
    if (!StaticMesh->NaniteSettings.bEnabled)
    {
        UE_LOG(LogNaniteExporter, Warning, TEXT("Nanite is not enabled for mesh: %s"), *StaticMesh->GetName());
        return false;
    }

    // 获取Nanite资源
    const FStaticMeshRenderData* RenderData = StaticMesh->GetRenderData();
    if (!RenderData || !RenderData->NaniteResources.HasValidData())
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("No valid Nanite resources found for mesh: %s"), *StaticMesh->GetName());
        return false;
    }

    const Nanite::FResources& NaniteResources = RenderData->NaniteResources;
    
    // 初始化导出数据
    OutExportData.SourceMeshName = StaticMesh->GetName();
    OutExportData.SourceMeshHash = GetTypeHash(StaticMesh->GetPathName());
    
    // 填充基本信息
    CEAssetExchange::SimplifiedNaniteMesh& Mesh = OutExportData.Mesh;
    Mesh.Version = 1;
    Mesh.NumInputTriangles = NaniteResources.NumInputTriangles;
    Mesh.NumInputVertices = NaniteResources.NumInputVertices;
    Mesh.NumClusters = NaniteResources.NumClusters;
    Mesh.NumHierarchyNodes = NaniteResources.HierarchyNodes.Num();
    Mesh.PositionPrecision = NaniteResources.PositionPrecision;
    Mesh.NormalPrecision = NaniteResources.NormalPrecision;
    Mesh.TangentPrecision = NaniteResources.TangentPrecision;

    // 计算网格边界
    if (StaticMesh->GetRenderData()->LODResources.Num() > 0)
    {
        const FBoxSphereBounds& Bounds = StaticMesh->GetBounds();
        const FBox& Box = Bounds.GetBox();
        Mesh.MeshBounds[0] = Box.Min.X;
        Mesh.MeshBounds[1] = Box.Min.Y;
        Mesh.MeshBounds[2] = Box.Min.Z;
        Mesh.MeshBounds[3] = Box.Max.X;
        Mesh.MeshBounds[4] = Box.Max.Y;
        Mesh.MeshBounds[5] = Box.Max.Z;
    }

    // 提取集群数据
    if (!ExtractClusterData(NaniteResources, Mesh))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to extract cluster data"));
        return false;
    }

    // 提取层次结构数据
    if (!ExtractHierarchyData(NaniteResources, Mesh))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to extract hierarchy data"));
        return false;
    }

    // 提取顶点数据
    if (!ExtractVertexData(NaniteResources, Mesh))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to extract vertex data"));
        return false;
    }

    // 计算统计信息
    CalculateStatistics(NaniteResources, OutExportData);

    UE_LOG(LogNaniteExporter, Log, TEXT("Successfully exported Nanite data for mesh: %s"), *StaticMesh->GetName());
    UE_LOG(LogNaniteExporter, Log, TEXT("  Clusters: %d, Hierarchy Nodes: %d"), Mesh.NumClusters, Mesh.NumHierarchyNodes);
    UE_LOG(LogNaniteExporter, Log, TEXT("  Original Triangles: %d, Vertices: %d"), Mesh.NumInputTriangles, Mesh.NumInputVertices);

    return true;
}

bool FNaniteDataExporter::ExtractClusterData(const Nanite::FResources& NaniteResources, CEAssetExchange::SimplifiedNaniteMesh& OutMesh)
{
    // 注意：这里需要访问Nanite的内部数据结构
    // 由于Nanite数据是高度压缩和优化的，我们需要通过特殊方式解析
    
    UE_LOG(LogNaniteExporter, Warning, TEXT("Cluster data extraction requires access to Nanite internal structures"));
    UE_LOG(LogNaniteExporter, Warning, TEXT("This is a simplified implementation - full extraction needs deeper integration"));
    
    // 分配集群数组
    OutMesh.ClusterDataSize = OutMesh.NumClusters * sizeof(CEAssetExchange::SimplifiedCluster);
    
    // 这里应该解析RootData和StreamablePages中的集群信息
    // 由于Nanite数据格式复杂，这里提供框架代码
    
    return true;
}

bool FNaniteDataExporter::ExtractHierarchyData(const Nanite::FResources& NaniteResources, CEAssetExchange::SimplifiedNaniteMesh& OutMesh)
{
    const TArray<Nanite::FPackedHierarchyNode>& HierarchyNodes = NaniteResources.HierarchyNodes;
    
    OutMesh.HierarchyDataSize = HierarchyNodes.Num() * sizeof(CEAssetExchange::SimplifiedHierarchyNode);
    
    // 这里需要解包Nanite的层次结构数据
    // FPackedHierarchyNode包含了压缩的边界和引用信息
    
    UE_LOG(LogNaniteExporter, Log, TEXT("Processing %d hierarchy nodes"), HierarchyNodes.Num());
    
    return true;
}

bool FNaniteDataExporter::ExtractVertexData(const Nanite::FResources& NaniteResources, CEAssetExchange::SimplifiedNaniteMesh& OutMesh)
{
    // Nanite的顶点数据是高度压缩的，存储在页面中
    // 需要通过Nanite的解码器来获取实际的顶点数据
    
    UE_LOG(LogNaniteExporter, Warning, TEXT("Vertex data extraction requires Nanite decompression"));
    UE_LOG(LogNaniteExporter, Warning, TEXT("Consider using fallback LOD data for initial implementation"));
    
    return true;
}

void FNaniteDataExporter::CalculateStatistics(const Nanite::FResources& NaniteResources, CEAssetExchange::NaniteExportData& ExportData)
{
    CEAssetExchange::NaniteExportData::Statistics& Stats = ExportData.Stats;
    
    Stats.OriginalTriangles = NaniteResources.NumInputTriangles;
    Stats.NaniteTriangles = 0; // 需要从集群数据计算
    Stats.CompressionRatio = 0; // 需要计算压缩比
    Stats.MaxHierarchyDepth = 0; // 需要遍历层次结构计算
    Stats.BuildTime = 0.0f; // 构建时间（如果可获取）
}

bool FNaniteDataExporter::SaveToFile(const CEAssetExchange::NaniteExportData& ExportData, const FString& FilePath)
{
    TArray<uint8> SerializedData;
    FMemoryWriter Writer(SerializedData);
    
    // 序列化头部信息
    Writer << const_cast<FString&>(ExportData.SourceMeshName);
    Writer << const_cast<uint32&>(ExportData.SourceMeshHash);
    
    // 序列化网格数据
    const CEAssetExchange::SimplifiedNaniteMesh& Mesh = ExportData.Mesh;
    Writer << const_cast<uint32&>(Mesh.Version);
    Writer << const_cast<uint32&>(Mesh.NumInputTriangles);
    Writer << const_cast<uint32&>(Mesh.NumInputVertices);
    Writer << const_cast<uint32&>(Mesh.NumClusters);
    Writer << const_cast<uint32&>(Mesh.NumHierarchyNodes);
    
    // 序列化边界信息
    for (int32 i = 0; i < 6; i++)
    {
        Writer << const_cast<float&>(Mesh.MeshBounds[i]);
    }
    
    // 序列化统计信息
    Writer << const_cast<uint32&>(ExportData.Stats.OriginalTriangles);
    Writer << const_cast<uint32&>(ExportData.Stats.NaniteTriangles);
    Writer << const_cast<uint32&>(ExportData.Stats.CompressionRatio);
    Writer << const_cast<float&>(ExportData.Stats.BuildTime);
    
    // 保存到文件
    if (!FFileHelper::SaveArrayToFile(SerializedData, *FilePath))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to save Nanite data to file: %s"), *FilePath);
        return false;
    }
    
    UE_LOG(LogNaniteExporter, Log, TEXT("Saved Nanite data to: %s (%d bytes)"), *FilePath, SerializedData.Num());
    return true;
}

bool FNaniteDataExporter::LoadFromFile(const FString& FilePath, CEAssetExchange::NaniteExportData& OutExportData)
{
    TArray<uint8> FileData;
    if (!FFileHelper::LoadFileToArray(FileData, *FilePath))
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Failed to load file: %s"), *FilePath);
        return false;
    }
    
    FMemoryReader Reader(FileData);
    
    // 反序列化数据
    Reader << OutExportData.SourceMeshName;
    Reader << OutExportData.SourceMeshHash;
    
    CEAssetExchange::SimplifiedNaniteMesh& Mesh = OutExportData.Mesh;
    Reader << Mesh.Version;
    Reader << Mesh.NumInputTriangles;
    Reader << Mesh.NumInputVertices;
    Reader << Mesh.NumClusters;
    Reader << Mesh.NumHierarchyNodes;
    
    // 反序列化边界信息
    for (int32 i = 0; i < 6; i++)
    {
        Reader << Mesh.MeshBounds[i];
    }
    
    // 反序列化统计信息
    Reader << OutExportData.Stats.OriginalTriangles;
    Reader << OutExportData.Stats.NaniteTriangles;
    Reader << OutExportData.Stats.CompressionRatio;
    Reader << OutExportData.Stats.BuildTime;
    
    UE_LOG(LogNaniteExporter, Log, TEXT("Loaded Nanite data from: %s"), *FilePath);
    return true;
}

bool FNaniteDataExporter::ValidateData(const CEAssetExchange::NaniteExportData& ExportData)
{
    const CEAssetExchange::SimplifiedNaniteMesh& Mesh = ExportData.Mesh;
    
    // 基本验证
    if (Mesh.Version == 0 || Mesh.NumClusters == 0)
    {
        UE_LOG(LogNaniteExporter, Error, TEXT("Invalid mesh data: Version=%d, Clusters=%d"), Mesh.Version, Mesh.NumClusters);
        return false;
    }
    
    if (ExportData.SourceMeshName.IsEmpty())
    {
        UE_LOG(LogNaniteExporter, Warning, TEXT("Source mesh name is empty"));
    }
    
    UE_LOG(LogNaniteExporter, Log, TEXT("Nanite data validation passed"));
    return true;
}
