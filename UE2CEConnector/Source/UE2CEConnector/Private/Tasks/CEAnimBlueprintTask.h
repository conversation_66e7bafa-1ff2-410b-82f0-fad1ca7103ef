#pragma once
#include "CETask.h"

class UCEAnimGraphConverter;
class UAnimationGraph;

class FCEAnimBlueprintTask : public FCETask
{
private:
    const UAnimBlueprint* AnimBlueprint;
    UAnimationGraph* AnimGraph = nullptr;
    TUniquePtr<UCEAnimGraphConverter> CEAnimGraphConverter;

protected:


public:
    FCEAnimBlueprintTask(const FString& savePath, FCEConvertBuilder& builder, const UAnimBlueprint* animBlueprint);

    virtual FString GetName() const override;

    virtual bool CollectDependencyTask() override;

    virtual bool Complete() override;
};