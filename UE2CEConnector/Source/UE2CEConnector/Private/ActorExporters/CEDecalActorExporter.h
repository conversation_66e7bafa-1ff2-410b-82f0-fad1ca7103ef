#pragma once
#include "CEBaseActorExporter.h"
#include "CEDecalActorExporter.generated.h"

UCLASS()
class UE2CECONNECTOR_API UCEDecalActorExporter : public UCEBaseActorExporter
{
    GENERATED_BODY()

public:
    UCEDecalActorExporter(const FObjectInitializer& ObjectInitializer);

    virtual cesdk::cegf::SDKGameObject AddGameObjectFromActor(
        FCEConvertBuilder& InBuilder,
        ::cesdk::cegf::SDKGameWorld InGameWorld,
        ::cesdk::cegf::SDKGameObject InParentGameObject,
        AActor* InExportActor) override;
}; 