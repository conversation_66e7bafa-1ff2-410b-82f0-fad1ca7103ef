#include "Converters/CEBlendSpaceConverter.h"
#include "Animation/BlendSpace.h"
#include "Exporters/CEExportOptions.h"
#include "Builders/CEConvertBuilder.h"
#include "Tasks/CEBlendSpaceTask.h"

UCEBlendSpaceConverter::UCEBlendSpaceConverter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UBlendSpace::StaticClass();
}

void UCEBlendSpaceConverter::Convert(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object)
{
    UBlendSpace* BlendSpace = Cast<UBlendSpace>(Object);
    if (!BlendSpace)
    {
        return;
    }

    UCEExportOptions* Options = Builder->GetExportOptions();
    if (!Options)
    {
        return;
    }

    // Create and add the task
    Builder->SetupTask<FCEBlendSpaceTask>(Save<PERSON><PERSON>, *<PERSON><PERSON><PERSON>, BlendSpace);
}