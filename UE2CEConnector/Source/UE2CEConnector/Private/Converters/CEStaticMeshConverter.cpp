#include "Converters/CEStaticMeshConverter.h"
#include "Tasks/CEStaticMeshTask.h"

UCEStaticMeshConverter::UCEStaticMeshConverter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UStaticMesh::StaticClass();
}

void UCEStaticMeshConverter::Convert(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object)
{
    UStaticMesh* StaticMesh = Cast<UStaticMesh>(Object);
    if (StaticMesh)
    {
        Builder->SetupTask<FCEStaticMeshTask>(SavePath, *Builder, StaticMesh); 
    }
}
