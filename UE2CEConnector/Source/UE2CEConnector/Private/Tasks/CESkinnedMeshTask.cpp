#include "Tasks/CESkinnedMeshTask.h"
#include <stdexcept>
#include "Builders/CEConvertBuilder.h"
#include "CERenderStateDefines.h"
#include "Exporters/CEExportOptions.h"
#include "CELog.h"
#include "ResourceAsset_generated.h"
#include "ImportMeshAssetData_generated.h"
#include <Exchange/CEAssetExchange.h>
#include <stdexcept>
#include <numeric>
#include "Exchange/StringExchange.h"

bool FCESkinnedMeshTask::Complete()
{
    const auto ExportOptions = mBuilder.GetOptions();
    using namespace CEAssetExchange;

    FAssetExchangeInput Input;
    Input.Options = ExportOptions;
    Input.Sdk = mBuilder.AssetExchangeSdk.get();

    SDKSettings Settings;
    Settings.mBalanceMode = ExportOptions->bUseAssetExchangeSdkBalanceMode;
    Settings.mOverwriteExistingTexture = ExportOptions->bOverwriteExistingTexture;
    Settings.mOverwriteExistingMaterial = ExportOptions->bOverwriteExistingMaterial;
    Settings.mOverwriteExistingMesh = ExportOptions->bOverwriteExistingMesh;
    Settings.mOverwriteExistingWorld = ExportOptions->bOverwriteExistingLevel;
    Input.Sdk->BeginAssemble(&Settings);

    FString ExportPath = FPaths::Combine(mBuilder.mRootPath, mBuilder.GetCEContentPath(SkeletalMesh));
    Input.RelativePath = StringMapper::Instance().FromUEToCEString(*mBuilder.GetCEContentPath(SkeletalMesh));

    USkeletalMesh* InSkeletalMesh = const_cast<USkeletalMesh*>(SkeletalMesh);

    bool bSuccess = ExportMesh(InSkeletalMesh, &Input);

    return bSuccess;
}