#include "Exporters/CEWidgetBlueprintExporter.h"
#include "Converters/CEWidgetBlueprintConverter.h"
#include <WidgetBlueprint.h>

UCEWidgetBlueprintExporter::UCEWidgetBlueprintExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UWidgetBlueprint::StaticClass();
    FormatExtension.Add(TEXT("json"));
    FormatDescription.Add(TEXT("CE WidgetBlueprint"));
}
