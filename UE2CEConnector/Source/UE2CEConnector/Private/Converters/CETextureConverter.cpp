#include "Converters/CETextureConverter.h"
#include "Tasks/CETextureTask.h"

UCETextureConverter::UCETextureConverter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UTexture2D::StaticClass();
}

void UCETextureConverter::Convert(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object)
{
    UTexture2D* Texture2D = Cast<UTexture2D>(Object);
    if (Texture2D)
    {
        Builder->SetupTask<FCETextureTask>(*Builder, Texture2D, SavePath);
    }
}

FString UCETextureConverter::GetCEFileExtension(const UObject* Object)
{
    const UTexture* InTexture = Cast<UTexture>(Object);
    if (!InTexture)
        return Super::GetCEFileExtension(Object);

    auto GetIsVirtualTextureStreaming = [](const UTexture* InTexture)-> bool {
        const UTexture2D* Tex2D = Cast<UTexture2D>(InTexture);
        if (!Tex2D)
            return InTexture->VirtualTextureStreaming != 0;

        return InTexture->VirtualTextureStreaming != 0 && Tex2D->GetSizeX() >= VIRTUAL_TEX2D_MIN_BLOCK_SIZE && Tex2D->GetSizeY() >= VIRTUAL_TEX2D_MIN_BLOCK_SIZE;
    };

    if (GetIsVirtualTextureStreaming(InTexture))
    {
        return ".udim.nda";
    }
    else
    {
        return ".tex.nda";
    }
}