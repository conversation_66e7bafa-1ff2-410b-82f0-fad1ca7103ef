#include "CEBaseActorExporter.h"

#include "Builders/CEConvertBuilder.h"

UCEBaseActorExporter::UCEBaseActorExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = nullptr;
}

UCEBaseActorExporter::AddEntityFromActorRetType UCEBaseActorExporter::AddEntityFromActor(
    FCEConvertBuilder& InBuilder,
    CEAssetExchange::IWorldAssemble* InWorldAssemble,
    USceneComponent* InSceneComponent,
    CEAssetExchange::IEntity* LevelEntity,
    AActor* InExportActor,
    CEAssetExchange::FAssetExchangeInput* InInput)
{
    return {};
}

cesdk::cegf::SDKGameObject UCEBaseActorExporter::AddGameObjectFromActor(
    FCEConvertBuilder& InBuilder,
    cesdk::cegf::SDKGameWorld InGameWorld,
    cesdk::cegf::SDKGameObject InParentGameObject,
    AActor* InExportActor)
{
    USceneComponent* RootComp = InExportActor->GetRootComponent();
    if (!RootComp)
        return InParentGameObject;
    cesdk::cegf::SDKGameObject RootGameObject = InBuilder.AddGameObjectFromSceneComponent(InGameWorld, InParentGameObject, RootComp);
    TraverseSceneComponent(InBuilder, InGameWorld, RootGameObject, RootComp);
    return RootGameObject;
}

void UCEBaseActorExporter::TraverseSceneComponent(FCEConvertBuilder& InBuilder, cesdk::cegf::SDKGameWorld InGameWorld, cesdk::cegf::SDKGameObject InParentGameObject, USceneComponent* InRootComponent)
{
    if (!InRootComponent)
        return;

    TArray<USceneComponent*> Children = InRootComponent->GetAttachChildren();
    
    for (USceneComponent* Child : Children)
    {
        if (!Child->IsEditorOnly())
        {
            cesdk::cegf::SDKGameObject ChildGameObject = InBuilder.AddGameObjectFromSceneComponent(InGameWorld, InParentGameObject, Child);
            TraverseSceneComponent(InBuilder, InGameWorld, ChildGameObject, Child);    
        }
    }
}


void UCEBaseActorExporter::CollectDependencyAssets(FCEConvertBuilder& InBuilder, AActor* InExportActor) {}


bool UCEBaseActorExporter::SupportsObject(const UObject* Object) const
{
    return (SupportedClass && Object->IsA(SupportedClass));
}