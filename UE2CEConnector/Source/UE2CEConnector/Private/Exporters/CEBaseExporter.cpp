#include "Exporters/CEBaseExporter.h"
#include "Exporters/CEExportOptions.h"
#include "CoreMinimal.h"
#include "Builders/CEConvertBuilder.h"
#include "UI/CEExportOptionsWindow.h"
#include "UObject/GCObjectScopeGuard.h"
#include "AssetExportTask.h"
#include "CELog.h"

UCEBaseExporter::UCEBaseExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = nullptr;
    bText = false;
    PreferredFormatIndex = 0;
}

bool UCEBaseExporter::ExportBinary(UObject* Object, const TCHAR* Type, FArchive& Archive, FFeedbackContext* Warn, int32 FileIndex, uint32 PortFlags)
{
    UCEExportOptions* Options = GetExportOptions();
    if (Options == nullptr)
    {
        return false;
    }

    FString ExtWithDot = FPaths::GetExtension(CurrentFilename, true);
	FString ObjectPackageName = Object->GetOuter()->GetFName().ToString();
    if (CurrentFilename.RemoveFromEnd(ObjectPackageName + ExtWithDot)) 
	{
        FString ObjectName;
        Object->GetName(ObjectName);
        CurrentFilename += "/" + ObjectName + ExtWithDot;
	}

    FCEConvertBuilder Builder(CurrentFilename, Options);
    if (Builder.AssetExchangeSdk)
    {
        if (AddObject(Builder, Object))
        {
            // Clear previous logs before starting a new export
            FCELogCollector::Get().ClearWarningLogs();
            FCELogCollector::Get().ClearFxCompileErrorLogs();
            Builder.CollectAllDependencyTasks(Warn);
            Builder.CompleteAllTasks(Warn);
            return true;
        }
    }
    return false;
}

UCEExportOptions* UCEBaseExporter::GetExportOptions()
{
    UCEExportOptions* Options = nullptr;
    bool bAutomatedTask = GIsAutomationTesting || FApp::IsUnattended();

    if (ExportTask != nullptr)
    {
        Options = Cast<UCEExportOptions>(ExportTask->Options);

        if (ExportTask->bAutomated)
        {
            bAutomatedTask = true;
        }
    }

    if (Options == nullptr)
    {
        Options = NewObject<UCEExportOptions>();
    }

    if (GetShowExportOption() && !bAutomatedTask)
    {
        bool bExportAll = GetBatchMode();
        bool bOperationCanceled = false;

        FGCObjectScopeGuard OptionsGuard(Options);
        SCEExportOptionsWindow::ShowDialog(Options, CurrentFilename, GetBatchMode(), bOperationCanceled, bExportAll);

        if (bOperationCanceled)
        {
            SetCancelBatch(GetBatchMode());
            return nullptr;
        }

        SetShowExportOption(!bExportAll);
        Options->SaveConfig();
    }

    return Options;
}

bool UCEBaseExporter::AddObject(FCEConvertBuilder& Builder, UObject* Object)
{
    Builder.GetOrAdd(FPaths::ConvertRelativePathToFull(CurrentFilename), Object);
    return true;
}
