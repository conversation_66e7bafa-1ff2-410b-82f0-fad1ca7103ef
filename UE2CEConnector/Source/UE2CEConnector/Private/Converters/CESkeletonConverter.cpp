#include "Converters/CESkeletonConverter.h"
#include "Animation/Skeleton.h"
#include "Exporters/CEExportOptions.h"
#include "Engine/SkeletalMesh.h"
#include "ReferenceSkeleton.h"
#include "Animation/Skeleton.h"
#include "BoneContainer.h"
#include "Tasks/CESkeletonTask.h"

UCESkeletonConverter::UCESkeletonConverter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = USkeleton::StaticClass();
}

void UCESkeletonConverter::Convert(FCEConvertBuilder* Builder, const FString& SavePath, UObject* Object)
{
    USkeleton* Skeleton = Cast<USkeleton>(Object);
    if (!Skeleton)
    {
        return;
    }

    UCEExportOptions* Options = Builder->GetExportOptions();
    if (!Options)
    {
        return;
    }

    // Create and add the task
    Builder->SetupTask<FCESkeletonTask>(<PERSON><PERSON><PERSON>, *<PERSON><PERSON><PERSON>, Skeleton);
}   
