#pragma once
#include "CoreMinimal.h"
#include "CEBaseComponentExporter.h"
#include "CELightComponentExporter.generated.h"

class USkyLightComponent;
class ULightComponent;

UCLASS()
class UCELightComponentExporter : public UCEBaseComponentExporter
{
    GENERATED_UCLASS_BODY()
public:
    virtual CEAssetExchange::IEntity* AddEntityFromSceneComponent(
        FCEConvertBuilder& InBuilder,
        CEAssetExchange::IWorldAssemble* InWorldAssemble,
        USceneComponent* InSceneComponent,
        CEAssetExchange::IEntity* InParentEntity,
        USceneComponent* InExportComponent,
        CEAssetExchange::FAssetExchangeInput* InInput) override;

    virtual cesdk::cegf::SDKGameObject AddGameObjectFromSceneComponent(
        FCEConvertBuilder& InBuilder,
        cesdk::cegf::SDKGameWorld InGameWorld,
        cesdk::cegf::SDKGameObject InParentGameObject,
        USceneComponent* InExportComponent) override;
private:
    void ExportLightComponent(ULightComponent* LightC, cesdk::cegf::SDKGameObject LightGameObject);
    void ExportSkyLightComponent(USkyLightComponent* LightC, cesdk::cegf::SDKGameObject LightGameObject);
};