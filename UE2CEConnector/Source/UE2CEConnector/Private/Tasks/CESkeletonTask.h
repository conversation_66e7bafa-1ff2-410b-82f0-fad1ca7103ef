#pragma once
#include "CETask.h"

class USkeleton;

// Define a task for skeleton conversion
class FCESkeletonTask : public FCETask
{
public:
    FCESkeletonTask(const FString& savePath, FCEConvertBuilder& builder, const USkeleton* skeleton);
    virtual bool CollectDependencyTask() override;
    virtual FString GetName() const override;
    virtual bool Complete() override;

private:
    const USkeleton* Skeleton;
};