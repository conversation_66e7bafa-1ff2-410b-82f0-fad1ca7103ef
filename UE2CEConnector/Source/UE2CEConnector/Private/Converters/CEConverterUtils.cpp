#include "Converters/CEConverterUtils.h"

namespace CEConverterUtils
{
    FTransform ConvertCoordinateSystem(const FTransform& InTransform)
    {
        // Define the rotation matrix for coordinate system conversion
        // This example converts between UE (Z-up, X-forward) and another system (Y-up, Z-forward)
        static const FMatrix CoordinateConversion{
            {1, 0, 0, 0},
            {0, 0, -1, 0},
            {0, 1, 0, 0},
            {0, 0, 0, 1},
        };
        static const FMatrix CoordinateConversionInverse = CoordinateConversion.Inverse();

        // Apply conversion: ConversionInverse * OriginalTransform * Conversion
        FTransform ConvertedTransform = FTransform(CoordinateConversionInverse) * InTransform * FTransform(CoordinateConversion);
        
        return ConvertedTransform;
    }
} 