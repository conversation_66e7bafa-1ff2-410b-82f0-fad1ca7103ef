#include "Tasks/CEWidgetBlueprintTask.h"
#include <stdexcept>
#include "Builders/CEConvertBuilder.h"
#include "CERenderStateDefines.h"
#include "Exporters/CEExportOptions.h"
#include "CELog.h"
#include "ResourceAsset_generated.h"
#include <Exchange/StringExchange.h>
#include <Exchange/CEAssetExchange.h>
#include <stdexcept>
#include <numeric>

bool FCEWidgetBlueprintTask::Complete()
{
    const auto exportOptions = mBuilder.GetOptions();
    using namespace CEAssetExchange;

    FAssetExchangeInput input;
    input.Options = exportOptions;
    input.Sdk = mBuilder.AssetExchangeSdk.get();

	SDKSettings settings;
    input.Sdk->BeginAssemble(&settings);

    FString relativePath = mBuilder.GetCEContentPath(widgetBlueprint);
	input.RelativePath = StringMapper::Instance().FromUEToCEString(*relativePath);

    ExportWidgetBlueprint(*widgetBlueprint, &input);

    return true;
}

FString FCEWidgetBlueprintTask::GetName() const
{
    return widgetBlueprint->GetName();
}

bool FCEWidgetBlueprintTask::CollectDependencyTask()
{
    FCETask::CollectDependencyTask();
    mDependencies.Empty();
    return true;
}
