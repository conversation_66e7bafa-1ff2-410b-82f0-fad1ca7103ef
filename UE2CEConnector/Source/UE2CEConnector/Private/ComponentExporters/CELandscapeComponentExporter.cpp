#include "CELandscapeComponentExporter.h"
#include "LandscapeComponent.h"
#include "Builders/CEConvertBuilder.h"
#include "Exchange/ExportWorld.h"

UCELandscapeComponentExporter::UCELandscapeComponentExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = ULandscapeComponent::StaticClass();
}

CEAssetExchange::IEntity* UCELandscapeComponentExporter::AddEntityFromSceneComponent(
    FCEConvertBuilder& InBuilder,
    CEAssetExchange::IWorldAssemble* InWorldAssemble,
    USceneComponent* InSceneComponent,
    CEAssetExchange::IEntity* InParentEntity,
    USceneComponent* InExportComponent,
    CEAssetExchange::FAssetExchangeInput* InInput)
{
    return CEAssetExchange::AddLandscape(InBuilder, InWorldAssemble, InSceneComponent, InParentEntity, InExportComponent, InInput);
}

