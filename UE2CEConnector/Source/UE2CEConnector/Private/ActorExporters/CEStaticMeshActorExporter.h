#pragma once
#include "CEBaseActorExporter.h"
#include "CEStaticMeshActorExporter.generated.h"

UCLASS()
class UCEStaticMeshActorExporter : public UCEBaseActorExporter
{
public:
    GENERATED_UCLASS_BODY()
    virtual AddEntityFromActorRetType AddEntityFromActor(FCEConvertBuilder& InBuilder,
                                                         CEAssetExchange::IWorldAssemble* InWorldAssemble,
                                                         USceneComponent* InSceneComponent,
                                                         CEAssetExchange::IEntity* LevelEntity,
                                                         AActor* InExportActor,
                                                         CEAssetExchange::FAssetExchangeInput* InInput) override;

      virtual cesdk::cegf::SDKGameObject AddGameObjectFromActor(
                                                            FCEConvertBuilder& InBuilder,
                                                            ::cesdk::cegf::SDKGameWorld InGameWorld,
                                                            ::cesdk::cegf::SDKGameObject InParentGameObject,
                                                            AActor* InExportActor) override;
                                                                                             

};