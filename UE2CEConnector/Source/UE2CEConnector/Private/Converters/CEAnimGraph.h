#pragma once
#include "CoreMinimal.h"
#include "UObject/GCObject.h"

class FCETask;
class UAnimationGraph;
class UEdGraphPin;

class CESubGraph;
class CEAnimGraph;
class UCEAnimGraphConverter;

class CEGraphLink
{
public:
    FString Name = "";
    FString Type = "";
    FString TargetNode;
    int TargetSlot = 0;
    FString SourceNode;
    int SourceSlot = 0;

    CEGraphLink() = default;
    virtual ~CEGraphLink() = default;

public:
    virtual TSharedRef<FJsonObject> ToJsonObject() const;
};

// =======

class CEGraphNode
{
public:
    FString Name = "";
    FString Type = "";
    int PosX = 0;
    int PosY = 0;

public:
    CEGraphNode(FString Type)
        : Type(Type)
    {}
    virtual ~CEGraphNode() = default;

public:
    virtual TSharedRef<FJsonObject> ToJsonObject() const;
    virtual int GetInputSlotIndex(UEdGraphPin* Pin) { return 0; }
    virtual void SetInputSlot(UEdGraphPin* Pin, FString LinkId) {}
};

class CEAnimGraphNode_Root : public CEGraphNode
{
public:
    TArray<FString> InPoseLinks;

public:
    CEAnimGraphNode_Root()
        : CEGraphNode("RootNode")
    {}

public:
    TSharedRef<FJsonObject> ToJsonObject() const override;
    int GetInputSlotIndex(UEdGraphPin* Pin) override;
    void SetInputSlot(UEdGraphPin* Pin, FString LinkId) override;
};

// =======

class CEAnimGraphNode_Unknown : public CEGraphNode
{
public:
    TArray<FString> InPoseLinks;
    TArray<FString> InParamLinks;
    TArray<FString> InParamTypes;

public:
    CEAnimGraphNode_Unknown()
        : CEGraphNode("UnknownNode")
    {}

public:
    TSharedRef<FJsonObject> ToJsonObject() const override;
    int GetInputSlotIndex(UEdGraphPin* Pin) override;
    void SetInputSlot(UEdGraphPin* Pin, FString LinkId) override;
};

class CEAnimGraphNode_Variable : public CEGraphNode
{
public:
    FString ReturnType = "Bool";
    FString DefaultValue = "true";
    TArray<FString> InParams;

public:
    CEAnimGraphNode_Variable()
        : CEGraphNode("ParamImplNode")
    {}

public:
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
};

class CEAnimGraphNode_PlaySequence : public CEGraphNode
{
public:
    TArray<FString> InParamLinks;
    FString CompositePath;
    bool Loop;
    FString SyncMethod;
    FString GroupName;
    FString GroupRole;

public:
    CEAnimGraphNode_PlaySequence()
        : CEGraphNode("PlayCompositeNode")
    {}

public:
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
};

class CEAnimGraphNode_SwitchPosesByBool : public CEGraphNode
{
public:
    TArray<FString> InPoseLinks;
    TArray<FString> InParamLinks;

public:
    CEAnimGraphNode_SwitchPosesByBool()
        : CEGraphNode("SwitchPosesByBoolNode")
    {}

public:
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
    int GetInputSlotIndex(UEdGraphPin* Pin) override;
    void SetInputSlot(UEdGraphPin* Pin, FString LinkId) override;
};

class CEAnimGraphNode_Slot : public CEGraphNode
{
public:
    FString SlotName;
    TArray<FString> InPoseLinks;

public:
    CEAnimGraphNode_Slot()
        : CEGraphNode("SlotNode")
    {}

public:
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
    int GetInputSlotIndex(UEdGraphPin* Pin) override;
    void SetInputSlot(UEdGraphPin* Pin, FString LinkId) override;
};

class CEAnimGraphNode_BlendSpacePlayer : public CEGraphNode
{
public:
    int InParamType = 1;

    TArray<FString> InParamLinks;
    FString BlendSpacePath;
    FString SyncMethod;
    FString GroupName;
    FString GroupRole;

public:
    CEAnimGraphNode_BlendSpacePlayer()
        : CEGraphNode("PlayBlendSpaceNode")
    {}

public:
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
    virtual int GetInputSlotIndex(UEdGraphPin* Pin) override;
    virtual void SetInputSlot(UEdGraphPin* Pin, FString LinkId) override;
};

class CEAnimGraphNode_FSM : public CEGraphNode
{
public:
    struct FSMStateInfo
    {
        FString Name;
        FString DisplayName;
        int PosX = 0;
        int PosY = 0;
        TSharedPtr<CESubGraph> StateGraph;
    };

    struct FSMTransitionInfo
    {
        FString Name;
        FString From;
        FString To;
        FString Rule;
        float BlendTime = 0.2f;
        FString BlendMode = "CrossFade";
        TArray<TSharedPtr<CESubGraph>> RuleGraphs;
    };

    FString FSMName;
    FString InitStateName;
    TArray<FSMStateInfo> States;
    TArray<FSMTransitionInfo> Transitions;

public:
    CEAnimGraphNode_FSM()
        : CEGraphNode("FSMNode")
    {}
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
};

class CEAnimTransitionGraphNode_Result : public CEGraphNode
{
public:
    bool DefaultResult = false;

public:
    CEAnimTransitionGraphNode_Result()
        : CEGraphNode("FlowNode_TransitionResult")
    {}

public:
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
};

class CEAnimTransitionGraphNode_Unknown : public CEGraphNode
{
public:
    CEAnimTransitionGraphNode_Unknown()
        : CEGraphNode("FlowNode_Unknown")
    {}
};

class CEAnimTransitionGraphNode_AnimParam : public CEGraphNode
{
public:
    FString Parameter;

public:
    CEAnimTransitionGraphNode_AnimParam()
        : CEGraphNode("FlowNode_AnimParam")
    {}

public:
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
};

class CEAnimTransitionGraphNode_BinaryArithOp : public CEGraphNode
{
public:
    int BinaryArithOp;

    CEAnimTransitionGraphNode_BinaryArithOp(int OpType)
        : CEGraphNode("FlowNode_BinaryArithOp")
        , BinaryArithOp(OpType)
    {}

public:

    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
};

class CEAnimTransitionGraphNode_BinaryLogicOp : public CEGraphNode
{
public:
    int BinaryLogicOp;

    CEAnimTransitionGraphNode_BinaryLogicOp(int OpType)
        : CEGraphNode("FlowNode_BinaryLogicOp")
        , BinaryLogicOp(OpType)
    {}
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
};

class CEAnimTransitionGraphNode_UnaryArithOp : public CEGraphNode
{
public:
    int UnaryArithOp;

    CEAnimTransitionGraphNode_UnaryArithOp(int OpType)
        : CEGraphNode("FlowNode_UnaryArithOp")
        , UnaryArithOp(OpType)
    {}
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
};

class CEAnimTransitionGraphNode_UnaryLogicOp : public CEGraphNode
{
public:
    int UnaryLogicOp;
    CEAnimTransitionGraphNode_UnaryLogicOp(int OpType)
        : CEGraphNode("FlowNode_UnaryLogicOp")
        , UnaryLogicOp(OpType)
    {}
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
};

class CEAnimTransitionGraphNode_Compare : public CEGraphNode
{
public:
    int Relation;
    CEAnimTransitionGraphNode_Compare(int OpType)
        : CEGraphNode("FlowNode_Compare")
        , Relation(OpType)
    {}
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
};

class UCEAnimTransitionGraphNode_CallFunction : public CEGraphNode
{
public:
    FString FunctionName;

public:
    UCEAnimTransitionGraphNode_CallFunction()
        : CEGraphNode("FlowNode_CallFunction")
    {}

public:
    virtual TSharedRef<FJsonObject> ToJsonObject() const override;
};


// =======

class CEAnimGraphParamater
{
public:
    FString ValueStr;
    FString Type;
    FString Name;
public:
    TSharedRef<FJsonObject> ToJsonObject() const;
};

class CERootMotionMode
{
public:
    FString ExtractMode;
    FString ApplyMode;

public:
    CERootMotionMode();
    TSharedRef<FJsonObject> ToJsonObject() const;
};

// ======

class CESubGraph
{
public:
    FString Name;
    TArray<TSharedPtr<CEGraphNode>> Nodes;
    TArray<TSharedPtr<CEGraphLink>> Links;

public:
    TSharedRef<FJsonObject> ToJsonObject() const;
};

class CEAnimGraph
{
public:
    FString Name;
    TSharedPtr<CESubGraph> SubGraph;
    TArray<CEAnimGraphParamater> Parameters;
    CERootMotionMode RootMotionMode;

public:
    CEAnimGraph();

    TSharedRef<FJsonObject> ToJsonObject() const;
    FString ToJsonString() const;
};

// ======

class UCEGraphNodeConverter
{
public:
    UCEGraphNodeConverter() = default;
    virtual ~UCEGraphNodeConverter() = default;

    virtual bool CanConvert(UClass* Class) const = 0;
    virtual TSharedPtr<CEGraphNode> Convert(UEdGraphNode* InNode) = 0;
    virtual TArray<UObject*> GetDependencies(UEdGraphNode* InNode) = 0;
};

template<typename T>
class UCEGraphNodeConverter_Impl : public UCEGraphNodeConverter
{
protected:
    using UENodeType = T;
public:
    bool CanConvert(UClass* Class) const override
    {
        return Class == UENodeType::StaticClass();
    }

    TSharedPtr<CEGraphNode> Convert(UEdGraphNode* InNode) override final
    {
        auto OutNode = ConvertImpl(Cast<UENodeType>(InNode));
        OutNode->PosX = InNode->NodePosX;
        OutNode->PosY = InNode->NodePosY;

        return OutNode;
    }
    
    TArray<UObject*> GetDependencies(UEdGraphNode* InNode) override final
    {
        return GetDependenciesImpl(Cast<UENodeType>(InNode));
    }

protected:
    virtual TSharedPtr<CEGraphNode> ConvertImpl(UENodeType* InNode) = 0;
    virtual TArray<UObject*> GetDependenciesImpl(UENodeType* InNode)
    {
        return {};
    }
};

class UCEAnimTransitionGraphConverter
{
private:
    TArray<TUniquePtr<UCEGraphNodeConverter>> CENodeConverters;
    TUniquePtr<UCEGraphNodeConverter> CENodeConverter_Default;

    UCEGraphNodeConverter* FindGraphNodeConverter(UClass* Class)
    {
        for (auto& Converter : CENodeConverters)
        {
            if (Converter->CanConvert(Class))
                return Converter.Get();
        }
        return CENodeConverter_Default.Get();
    }

public:
    UCEAnimTransitionGraphConverter();

    TSharedPtr<CESubGraph> Convert(class UAnimationTransitionGraph* Graph);
};

class UCEAnimGraphConverter
{
private:
    TMap<UEdGraphNode*, TSharedPtr<CEGraphNode>> NodeMap;

    TArray<TUniquePtr<UCEGraphNodeConverter>> CENodeConverters;
    TUniquePtr<UCEGraphNodeConverter> CENodeConverter_Default;

    UCEGraphNodeConverter* FindGraphNodeConverter(UClass* Class)
    {
        for (auto& Converter : CENodeConverters)
        {
            if (Converter->CanConvert(Class))
                return Converter.Get();
        }
        return CENodeConverter_Default.Get();
    }

    void TraverseGraph(UEdGraph* Graph, std::function<void(UEdGraphNode*)> Func);

    FCETask* AnimBPConverterTask;

public:
    UCEAnimGraphConverter();
    TArray<UObject*> GetDependencies(UAnimationGraph* InNode);
    TSharedPtr<CEAnimGraph> ConvertWithParamater(UAnimationGraph* Graph, ERootMotionMode::Type RootMotionMode);
    TSharedPtr<CESubGraph> ConvertSubGraph(UAnimationGraph* Graph);

    friend class UCEGraphLinkConverter;
};
