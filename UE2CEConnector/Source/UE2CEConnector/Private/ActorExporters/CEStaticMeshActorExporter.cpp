#include "CEStaticMeshActorExporter.h"

#include "Builders/CEConvertBuilder.h"
#include "Engine/StaticMeshActor.h"
#include "Exchange/ExportWorld.h"

UCEStaticMeshActorExporter::UCEStaticMeshActorExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = AStaticMeshActor::StaticClass();
}

UCEStaticMeshActorExporter::AddEntityFromActorRetType UCEStaticMeshActorExporter::AddEntityFromActor(
    FCEConvertBuilder& InBuilder,
    CEAssetExchange::IWorldAssemble* InWorldAssemble,
    USceneComponent* InSceneComponent,
    CEAssetExchange::IEntity* LevelEntity,
    AActor* InExportActor,
    CEAssetExchange::FAssetExchangeInput* InInput)
{
    auto* StaticMeshComponent = Cast<UStaticMeshComponent>(InExportActor->GetComponentByClass(UStaticMeshComponent::StaticClass()));
    return {InBuilder.AddEntityFromSceneComponent(InWorldAssemble, InSceneComponent, LevelEntity, StaticMeshComponent, InInput), false};
}
cesdk::cegf::SDKGameObject UCEStaticMeshActorExporter::AddGameObjectFromActor(
    FCEConvertBuilder& InBuilder,
    ::cesdk::cegf::SDKGameWorld InGameWorld,
    ::cesdk::cegf::SDKGameObject InParentGameObject,
    AActor* InExportActor)
{
   return Super::AddGameObjectFromActor(InBuilder, InGameWorld, InParentGameObject, InExportActor);
}        