#include "CEAnimationComponentExporter.h"

#include "Builders/CEConvertBuilder.h"
#include "Components/SkeletalMeshComponent.h"
#include "Exchange/ExportWorld.h"
#include "Engine/SkeletalMesh.h"
#include "Rendering/SkeletalMeshRenderData.h"
#include "Rendering/SkeletalMeshModel.h"
#include "GameFramework/Character.h"

#include "Exchange/StringExchange.h"

#include "UE2CEConnectorUtils/UE2CEConnectorUtils.h"
#include "SDKComponent/SDKPhysicsComponent.h"
#include "SDKComponent/SDKCapsuleComponent.h"
#include "SDKComponent/SDKModelComponent.h"
#include "SDKComponent/SDKAnimationComponent.h"
#include "SDKComponent/SDKCharacterMovementComponent.h"
#include "SDKGameObject/SDKCharacterObject.h"

#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"

static constexpr std::string_view PlayerControllerScript = "Contents/TypeScript/ActionRPG/PlayerController.mts";

UCEAnimationComponentExporter::UCEAnimationComponentExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = USkeletalMeshComponent::StaticClass();
}

cesdk::cegf::SDKGameObject UCEAnimationComponentExporter::AddGameObjectFromSceneComponent(FCEConvertBuilder& InBuilder, ::cesdk::cegf::SDKGameWorld InGameWorld, ::cesdk::cegf::SDKGameObject InParentGameObject,
                                                                                          USceneComponent* InExportComponent)
{

    cesdk::cegf::SDKGameObject GameObject(nullptr);

    auto* AttachActor = InExportComponent->GetAttachParentActor();

    FTransform CETransform = FUE2CEConnectorUtils::ConvertUETransformToCE(InExportComponent->GetRelativeTransform());
    const auto T = CETransform.GetTranslation();
    const auto R = CETransform.GetRotation();
    const auto S = CETransform.GetScale3D();

    USkeletalMeshComponent* Mesh = Cast<USkeletalMeshComponent>(InExportComponent);

    if (auto* Character = Cast<ACharacter>(AttachActor))
    {

        GameObject = CreateCharacterObject(InBuilder, InGameWorld, InParentGameObject, InExportComponent);
        SetCapsulelComponent(InBuilder, GameObject, Character->GetCapsuleComponent(), Mesh);
        SetCharacterMovementComponent(InBuilder, GameObject, Character->GetCharacterMovement());

        GameObject.SetScriptPath(PlayerControllerScript.data());
    }
    else
    {
        GameObject = CreateAnimationObject(InBuilder, InGameWorld, InParentGameObject, InExportComponent);
    }

    SetSkeletalMeshComponent(InBuilder, GameObject, Mesh);

    return GameObject;
}

cesdk::cegf::SDKGameObject UCEAnimationComponentExporter::CreateCharacterObject(FCEConvertBuilder& InBuilder, ::cesdk::cegf::SDKGameWorld InGameWorld, ::cesdk::cegf::SDKGameObject InParentGameObject, USceneComponent* InExportComponent)
{
    FTransform CETransform = FUE2CEConnectorUtils::ConvertUETransformToCE(InExportComponent->GetRelativeTransform());
    const auto T = CETransform.GetTranslation();
    const auto R = CETransform.GetRotation();
    const auto S = CETransform.GetScale3D();

    FString GameObjectName = FUE2CEConnectorUtils::GetSceneComponentName(InExportComponent);
    const char* name = CEAssetExchange::StringMapper::Instance().FromUEToCEString(*GameObjectName);
    auto CharacterObject = cesdk::cegf::CreateCharacterObject(&InGameWorld, name, InParentGameObject, {T.X, T.Y, T.Z}, {R.X, R.Y, R.Z, R.W}, {S.X, S.Y, S.Z});
    return CharacterObject;
}

cesdk::cegf::SDKGameObject UCEAnimationComponentExporter::CreateAnimationObject(FCEConvertBuilder& InBuilder, ::cesdk::cegf::SDKGameWorld InGameWorld, ::cesdk::cegf::SDKGameObject InParentGameObject, USceneComponent* InExportComponent)
{
    auto GameObject = UCEBaseComponentExporter::AddGameObjectFromSceneComponent(InBuilder, InGameWorld, InParentGameObject, InExportComponent);
    return GameObject;
}

void UCEAnimationComponentExporter::SetSkeletalMeshComponent(FCEConvertBuilder& InBuilder, cesdk::cegf::SDKGameObject GameObject, USkeletalMeshComponent* Mesh)
{
    if (Mesh && Mesh->GetSkeletalMeshAsset())
    {
        auto MeshComponentInstance = GetOrAddGameObjectComponent(GameObject, "cegf::ModelComponent").componentInstance;
        cesdk::cegf::SDKAnimationComponent MeshComponent(MeshComponentInstance);

        USkeletalMesh* SkeletalMesh = Mesh->GetSkeletalMeshAsset();
        FString MeshName = SkeletalMesh->GetPathName();
        FString Meshpath = InBuilder.GetCEContentPath(SkeletalMesh);
        cesdk::cegf::SDKModelComponent ModelComponent(MeshComponent.componentInstance);
        ModelComponent.SetModelAssetPath(TCHAR_TO_UTF8(*Meshpath));

        if (SkeletalMesh)
        {
            const FSkeletalMeshRenderData* RenderData = SkeletalMesh->GetResourceForRendering();

            int32_t NumLODs = SkeletalMesh->GetLODNum();
            NumLODs = 1;
            for (int32_t LODIndex = 0; LODIndex < NumLODs; ++LODIndex)
            {
                SkeletalMesh->GetLODInfo(LODIndex);

                const FSkeletalMeshLODRenderData& LODData = RenderData->LODRenderData[LODIndex];

                const int32_t NumSections = LODData.RenderSections.Num();
                for (int32_t SectionIndex = 0; SectionIndex < NumSections; ++SectionIndex)
                {
                    int32_t MaterialIndex = LODData.RenderSections[SectionIndex].MaterialIndex;
                    UMaterialInterface* Material = Mesh->GetMaterial(MaterialIndex);
                    if (Material)
                    {
                        FString MaterialPath = InBuilder.GetCEContentPath(Material);
                        ModelComponent.SetModelMaterialPath(TCHAR_TO_UTF8(*MaterialPath), SectionIndex, LODIndex);
                    }
                }
            }
        }
    }

    {
        auto AnimComponentInstance = GetOrAddGameObjectComponent(GameObject, "cegf::AnimationComponent").componentInstance;
        cesdk::cegf::SDKAnimationComponent AnimationComponent(AnimComponentInstance);

        assert(EAnimationMode::Type::AnimationBlueprint == Mesh->GetAnimationMode());

        USkeleton* Skeleton = Mesh->GetSkeletalMeshAsset() ? Mesh->GetSkeletalMeshAsset()->GetSkeleton() : nullptr;

        if (EAnimationMode::Type::AnimationBlueprint == Mesh->GetAnimationMode())
        {
            UAnimInstance* AnimInstance = Mesh->GetAnimInstance();
            AnimInstance->RootMotionMode;
            
            if (Skeleton && AnimInstance)
            {
                UAnimBlueprint* AnimBlueprint = Cast<UAnimBlueprint>(AnimInstance->GetClass()->ClassGeneratedBy);
                FString SkeletonPath = InBuilder.GetCEContentPath(Skeleton);
                FString BlueprintPath = InBuilder.GetCEContentPath(AnimBlueprint);
                AnimationComponent.SetSkeletonAnimation(TCHAR_TO_UTF8(*SkeletonPath), TCHAR_TO_UTF8(*BlueprintPath));
            }
        }
        else if (EAnimationMode::Type::AnimationSingleNode == Mesh->GetAnimationMode())
        {
            UAnimSequence* AnimSeq = Cast<UAnimSequence>(Mesh->AnimationData.AnimToPlay);
            if (Skeleton && AnimSeq)
            {
                FString SkeletonPath = InBuilder.GetCEContentPath(Skeleton);
                FString AnimSeqPath = InBuilder.GetCEContentPath(AnimSeq);
                AnimationComponent.SetSkeletonAnimation(TCHAR_TO_UTF8(*SkeletonPath), TCHAR_TO_UTF8(*AnimSeqPath));
                CE_Log(TEXT("AnimSeqPath: %s"), *(AnimSeqPath));
            }
            CE_Log(TEXT("AnimationSingleNode is not supported"));
        }
        else
        {
            CE_Log(TEXT("AnimationCustomMode is not supported"));
        }
    }
}

void UCEAnimationComponentExporter::SetCapsulelComponent(FCEConvertBuilder& InBuilder, cesdk::cegf::SDKGameObject GameObject, UCapsuleComponent* InCapsuleComp, USkeletalMeshComponent* InMeshComp)
{
    auto CapsuleInstance = GetOrAddGameObjectComponent(GameObject, "cegf::CapsuleComponent").componentInstance;

    GameObject.GetLocalTranslation();

    cesdk::cegf::SDKCapsuleComponent CapsuleComponent(CapsuleInstance);
    float radius = InCapsuleComp->GetUnscaledCapsuleRadius();
    float halfHeight = InCapsuleComp->GetUnscaledCapsuleHalfHeight();
    CapsuleComponent.SetCapsuleSize(radius, halfHeight - radius);


    auto MeshWorldTransform = InMeshComp->GetComponentToWorld();
    auto CapsuleWorldTransform = InCapsuleComp->GetComponentToWorld();

    auto MeshToCapsuleM = CapsuleWorldTransform.GetRelativeTransform(MeshWorldTransform);
    auto CapsuleToMeshM = MeshWorldTransform.GetRelativeTransform(CapsuleWorldTransform);
    FTransform CECapsuleToMeshM = FUE2CEConnectorUtils::ConvertUETransformToCE(CapsuleToMeshM);
    FTransform CEMeshToCapsuleM = FUE2CEConnectorUtils::ConvertUETransformToCE(MeshToCapsuleM);
    const auto T = CEMeshToCapsuleM.GetTranslation();
    CapsuleComponent.SetOffset(T.X, T.Y, T.Z);

    const auto& bodyInstance = InMeshComp->BodyInstance;
    //cesdk::cegf::CollisionType ceCollisionChannel = FUE2CEConnectorUtils::ConvertUECollisionTypeToCE(bodyInstance.GetObjectType());
    uint16_t ceCollisionMask = FUE2CEConnectorUtils::ConvertUECollisionMaskToCE(&bodyInstance);

    CapsuleComponent.SetIsDynamic(true);
    CapsuleComponent.SetIsKinematic(true);
    CapsuleComponent.SetCollisionType(cesdk::cegf::CollisionType::Actor);
    CapsuleComponent.SetCollisionMask(ceCollisionMask);
}

void UCEAnimationComponentExporter::SetCharacterMovementComponent(FCEConvertBuilder& InBuilder, cesdk::cegf::SDKGameObject GameObject, UCharacterMovementComponent* MovementComp)
{
    auto MovementInstance = GetOrAddGameObjectComponent(GameObject, "cegf::CharacterMovementComponent").componentInstance;
    cesdk::cegf::SDKCharacterMovementComponent MovementComponent(MovementInstance);

    //MovementComp->Getcurrent
}
