#include "CEBlueprintTask.h"

#include "Builders/CEConvertBuilder.h"
#include "NodeToCode/NodeToCodeHelper.h"

FString FCEBlueprintTask::GetName() const
{
    return TEXT("BlueprintTask");
}

bool FCEBlueprintTask::Complete()
{
    UBlueprint* Blueprint = Cast<UBlueprint>( ConstCast(mExportAsset));
    FString JsonOutput = FNodeToCodeHelper::GetEventGraphCodeJson(Blueprint);
    if (!JsonOutput.IsEmpty())
    {
        FString JsonSavePath = mSavePath + TEXT(".json");
        if (FPaths::IsRelative(JsonSavePath))
        {
            FFileHelper::SaveStringToFile(JsonOutput, *FPaths::Combine(mBuilder.mRootPath, JsonSavePath));
        }
        else
        {
            FFileHelper::SaveStringToFile(JsonOutput, *JsonSavePath);
        }
    }
    // TODO get typescript by llm
    return true;
}

bool FCEBlueprintTask::CollectDependencyTask()
{
    FCETask::CollectDependencyTask();
    mDependencies.Empty();
    return true;
}