#include "Exporters/CEAnimCompositeExporter.h"
#include "Converters/CEAnimCompositeConverter.h"
#include "Animation/AnimComposite.h"

UCEAnimCompositeExporter::UCEAnimCompositeExporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UAnimComposite::StaticClass();
    FormatExtension.Add(TEXT("nda"));
    FormatDescription.Add(TEXT("CE Animation Composite"));
}
