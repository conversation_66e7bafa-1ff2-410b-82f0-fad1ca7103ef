#pragma once

#include "CoreMinimal.h"
#include "CEExportOptions.generated.h"

UENUM(BlueprintType)
enum class ENaniteEnabled : uint8
{
    Keep UMETA(DisplayName = "Keep original state"),
    Disable UMETA(DisplayName = "Disable"),
    Enable UMETA(DisplayName = "Enable"),
    Disable_Keep UMETA(DisplayName = "Disable when exporting and keep orginal state at last"),
    Enable_Keep UMETA(DisplayName = "Enable when exporting and keep orginal state at last"),
};

UENUM(BlueprintType)
enum class ENaniteExportMode : uint8
{
    Disabled UMETA(DisplayName = "Export as standard mesh"),
    ExportNaniteData UMETA(DisplayName = "Export Nanite data only"),
    ExportWithProxy UMETA(DisplayName = "Export Nanite data with proxy mesh"),
    ExportHybrid UMETA(DisplayName = "Export hybrid format (Nanite + LODs)"),
};

UENUM(BlueprintType)
enum class ETextureFlipGreenChannel : uint8
{
    Keep UMETA(DisplayName = "Keep original state"),
    Disable UMETA(DisplayName = "Disable"),
    Enable UMETA(DisplayName = "Enable"),
};

UCLASS(Config = EditorPerProjectUserSettings, HideCategories = (DebugProperty))
class UE2CECONNECTOR_API UCEExportOptions : public UObject
{
    GENERATED_UCLASS_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = General)
    bool bExportLevelAsCEPrefab;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = General)
    bool bOverwriteExistingTexture;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = General)
    bool bOverwriteExistingMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = General)
    bool bOverwriteExistingMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = General)
    bool bOverwriteExistingWidgetBlueprint;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = General)
    bool bOverwriteExistingLevel;

    // UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = General)
    bool bUseAssetExchangeSdkBalanceMode;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Texture)
    bool bExportTextureSource;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Texture)
    bool bForceUseDefaultMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Texture)
    bool bExportUDIMTextureSource;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Texture)
    ETextureFlipGreenChannel FlipGreenChannelEnum;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Material)
    FString MotherMaterialContentPath;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Material)
    FString SM_DefaultMotherMeterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Material)
    FString HISM_DefaultMotherMeterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Material)
    FString LandscapeMesh_Meterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Material)
    bool bExportAllUsedTextures;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Material)
    bool bExportCustomNodeShaderCode;

    // UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Material)
    int32 BakedTextureSize;
    

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Mesh)
    ENaniteEnabled NaniteEnabledEnum;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Mesh)
    bool bExportNaniteProxy;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Mesh)
    bool bGenerateNaniteLODsForDGWOnly;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Mesh)
    float FallbackPercentTriangles;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Mesh)
    int32 FallbackMaxTriangles;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Mesh)
    int32 IgnoreSmallMeshTriangleSize;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Mesh)
    int32 DisableNaniteIfTriangleLessThan;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Mesh)
    bool bUseFullPrecisionUV;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Mesh)
    bool bUsetFullPrecisionTNB;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Landscape)
    int32 MaxTerrainGridSize;

    /** If enabled, export multi-file according mesh's LOD count. */
    // UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Mesh)
    bool bSplitLODFiles;

    // UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Foliage)
    bool bExportAsFoliageComponent;

    // UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Foliage, Meta = (EditCondition = "bExportAsFoliageComponent"))
    bool bUseDefaultMaterialTestOnly;

    // UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Foliage, Meta = (EditCondition = "bExportAsFoliageComponent", ClampMin = "1"))
    int32 MinClusterSize;

    // UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Foliage, Meta = (EditCondition = "bExportAsFoliageComponent", ClampMin = "1"))
    int32 MaxClusterSize;

    // Nanite Export Options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Nanite)
    ENaniteExportMode NaniteExportMode;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Nanite, Meta = (EditCondition = "NaniteExportMode == ENaniteExportMode::ExportWithProxy || NaniteExportMode == ENaniteExportMode::ExportHybrid"))
    bool bExportNaniteProxy;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Nanite)
    bool bGenerateNaniteLODsForDGWOnly;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Nanite)
    bool bCreateNaniteMetadata;

    /** If enabled, use quantization for vertex tangents and normals, reducing
     * size. Requires extension KHR_mesh_quantization, which may result in the
     * mesh not loading in some glTF viewers. */
    // UPROPERTY(EditAnywhere, BlueprintReadWrite, Config, Category = Mesh)
    // bool bUseMeshQuantization;

    void ResetToDefault();
};
