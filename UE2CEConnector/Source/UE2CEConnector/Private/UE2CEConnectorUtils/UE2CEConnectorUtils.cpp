#include "UE2CEConnectorUtils.h"
#include "AssetExchange/Include/SDKComponent/SDKPhysicsComponent.h"

FString FUE2CEConnectorUtils::GetSceneComponentName(const USceneComponent* SceneComponent)
{
    AActor* Actor = SceneComponent->GetOwner();
    if (SceneComponent != Actor->GetRootComponent())
        return SceneComponent->GetName(); // This will not be duplicated

    FString Name = Actor->GetActorNameOrLabel(); // This may be duplicated but never mind
    return Name;
}

FTransform FUE2CEConnectorUtils::ConvertUETransformToCE(const FTransform& InFTransform)
{
    const auto UETransform = InFTransform.ToMatrixWithScale();
    static const FMatrix Rotation{
        {1, 0, 0, 0},
        {0, 0, -1, 0},
        {0, 1, 0, 0},
        {0, 0, 0, 1},
    };
    static const FMatrix RotationInverse = Rotation.Inverse();

    const FTransform CeTransform{RotationInverse * UETransform * Rotation};
    return CeTransform;
}

cesdk::cegf::CollisionType FUE2CEConnectorUtils::ConvertUECollisionTypeToCE(ECollisionChannel ueCollisionChannel)
{
    switch (ueCollisionChannel)
    {
    case ECollisionChannel::ECC_WorldStatic:
        return cesdk::cegf::CollisionType::WorldStatic;
    case ECollisionChannel::ECC_WorldDynamic:
        return cesdk::cegf::CollisionType::WorldDynamic;
    case ECollisionChannel::ECC_Pawn:
        return cesdk::cegf::CollisionType::Actor;
    default:
        return cesdk::cegf::CollisionType::WorldStatic;
    }
}
namespace {
     const FName OverlapAll_ProfileName = FName(TEXT("OverlapAll"));
     const FName OverlapAllDynamic_ProfileName = FName(TEXT("OverlapAllDynamic"));
     const FName Trigger_ProfileName = FName(TEXT("Trigger"));
     const FName OverlapOnlyPawn_ProfileName = FName(TEXT("OverlapOnlyPawn"));
}

uint16_t FUE2CEConnectorUtils::ConvertUECollisionMaskToCE(const FBodyInstance* BodyInstance) {

    FName profileName = BodyInstance->GetCollisionProfileName();
    if (profileName == UCollisionProfile::BlockAll_ProfileName || profileName == UCollisionProfile::BlockAllDynamic_ProfileName || profileName == OverlapAll_ProfileName || profileName == OverlapAllDynamic_ProfileName)
    {
        return 0xffff;
    }
    else
    {
        auto& ueCollisionMask = BodyInstance->GetCollisionResponse();
        uint16_t ceCollisionMask = 0;
        ceCollisionMask |= (ueCollisionMask.GetResponse(ECollisionChannel::ECC_WorldStatic) > ECollisionResponse::ECR_Ignore ? 1 << ((uint16_t)cesdk::cegf::CollisionType::WorldStatic - 1u) : 0);
        ceCollisionMask |= (ueCollisionMask.GetResponse(ECollisionChannel::ECC_WorldDynamic) > ECollisionResponse::ECR_Ignore ? 1 << ((uint16_t)cesdk::cegf::CollisionType::WorldDynamic - 1u) : 0);
        ceCollisionMask |= (ueCollisionMask.GetResponse(ECollisionChannel::ECC_Pawn) > ECollisionResponse::ECR_Ignore ? 1 << ((uint16_t)cesdk::cegf::CollisionType::Actor - 1u) : 0);
        return ceCollisionMask;
    }
}
bool FUE2CEConnectorUtils::IsUECollisionTrigger(FName collisionProfile)
{

    return collisionProfile == Trigger_ProfileName || collisionProfile == OverlapAll_ProfileName || collisionProfile == OverlapAllDynamic_ProfileName || collisionProfile == OverlapOnlyPawn_ProfileName;
}