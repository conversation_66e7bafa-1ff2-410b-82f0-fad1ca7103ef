#pragma once
#include "CETask.h"
#include "Builders/CEConvertBuilder.h"
class FCEMaterialFunctionTask : public FCETask
{
public:
    FCEMaterialFunctionTask(FCEConvertBuilder& Builder, const UMaterialFunction* function, const FString& SavePath): 
		FCETask(ECETaskPriority::Material, SavePath, Builder, function)
        , MaterialFunction(function)
    {}

    virtual FString GetName() const override;
    virtual bool Complete() override;

private:
    const UMaterialFunction* MaterialFunction;
};