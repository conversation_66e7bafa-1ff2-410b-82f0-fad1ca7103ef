<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Sheen LTC Coeficients</Name>
    <EndUserGroup>Git</EndUserGroup>
  <Location>//UE5/Main/Engine/Source/Runtime/Renderer/Private/LTC.h
  </Location>
  <Function>The linked source contains at the bottom a precomputation tables which will be used in the rendering code for a new lighting model for cloth/fuzz.
  The precomputed table is stored with a file called LTC.h
  </Function>
  <Eula>https://github.com/tizian/ltc-sheen/blob/master/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder></LicenseFolder>
</TpsData>

