#pragma once
#include "CETask.h"
#include "Builders/CEConvertBuilder.h"
#include "CEMeshDefines.h"
#include "CEResource.h"

#define DEBUG_DATA_CHECKED 0

struct VertexChannelAssetData
{
    uint16 Stride = 0u;
    uint16 Frequency = 1u;
    int16 Stream = -1;
    int16 StreamOffset = -1;
    uint64 MiscFlag = 0u;
    uint64 Reserve0 = 0u;
    uint64 Reserve1 = 0u;
    uint32 VertexChannel = 0u;
    uint32 DataFormat = 0u;
    std::vector<uint8> Data;
};

template<class Element>
struct Vec3
{
    Element x{0};
    Element y{0};
    Element z{0};

    Vec3() = default;
    Vec3(const Vec3&) = default;
    Vec3& operator=(const Vec3&) = default;
    Vec3(Vec3&&) = default;
    Vec3& operator=(Vec3&&) = default;
    constexpr Vec3(Element _x, Element _y, Element _z) noexcept
        : x(_x)
        , y(_y)
        , z(_z)
    {}
    explicit Vec3(const Element* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
        , z(pArray[2])
    {}
    const Element* data() const
    {
        return &x;
    }
    Element* data()
    {
        return &x;
    }
    static constexpr size_t Dim()
    {
        return 3;
    }
};

typedef Vec3<uint32> UInt3;
typedef Vec3<int32> Int3;
typedef Vec3<uint16> UShort3;
typedef Vec3<uint8> UChar3;


template<class Element>
struct Vec4
{
    Element x{0};
    Element y{0};
    Element z{0};
    Element w{0};

    Vec4() = default;
    Vec4(const Vec4&) = default;
    Vec4& operator=(const Vec4&) = default;
    Vec4(Vec4&&) = default;
    Vec4& operator=(Vec4&&) = default;
    constexpr Vec4(Element _x, Element _y, Element _z, Element _w) noexcept
        : x(_x)
        , y(_y)
        , z(_z)
        , w(_w)
    {}
    explicit Vec4(const Element* pArray) noexcept
        : x(pArray[0])
        , y(pArray[1])
        , z(pArray[2])
        , w(pArray[3])
    {}
    const Element* data() const
    {
        return &x;
    }

    Element* data()
    {
        return &x;
    }

    static constexpr size_t Dim()
    {
        return 4;
    }
};

typedef Vec4<int> Int4;
typedef Vec4<int16> Short4;
typedef Vec4<uint8> UChar4;
typedef Vec4<uint16> UShort4;

class FCEStaticMeshTask : public FCETask
{
    using VertexChannelData = std::unique_ptr<CrossSchema::VertexChannelAssetDataT>;

public:
    FCEStaticMeshTask(const FString& savePath, FCEConvertBuilder& Builder, const UStaticMesh* StaticMesh)
        : FCETask(ECETaskPriority::Mesh, savePath, Builder, StaticMesh)
        , StaticMesh(StaticMesh)
    {}

    virtual FString GetName() const override
    {
        return StaticMesh->GetName();
    }

    virtual bool Complete() override;
    
    template<typename InputType, typename OutputType>
    void ReorderIndice(const InputType* Input, uint32 IndicesNum, std::vector<OutputType>& Output)
    {
        Output.reserve(IndicesNum);
        uint32 Offset = 0u;
        for (uint32 Index = 0; Index < IndicesNum; ++Index)
        {
            Output.emplace_back(static_cast<OutputType>(Input[Index]));
            Offset += sizeof(InputType);
        }

        const uint32 TriangleCnt = IndicesNum / 3;
        for (uint32 TriangleIndex = 0, Index = 0; TriangleIndex < TriangleCnt; ++TriangleIndex)
        {
            OutputType TmpValue = Output[Index + 1];
            Output[Index + 1] = Output[Index + 2];
            Output[Index + 2] = TmpValue;
            Index += 3;
        }
    }

    template<class T1, class T2>
    void AddVertexChannelData(cross::VertexChannel Channel, cross::VertexFormat Format, std::vector<T1>& Iuput)
    {
        if (!VertexChannelExist(Channel)) 
        {
            VertexChannelAssetData ChannelData;
            ChannelData.Stride = sizeof(T2);
            ChannelData.VertexChannel = static_cast<uint32>(Channel);
            ChannelData.DataFormat = static_cast<uint32>(Format);
            VertexChannelDatas.emplace_back(ChannelData);
        }

        VertexChannelAssetData& VertexChannel = GetChannelAssetData(Channel);
        uint32 OriginSize = static_cast<uint32>(VertexChannel.Data.size());
        VertexChannel.Data.resize(OriginSize + Iuput.size() * sizeof(T1));
        assert(VertexChannel.Data.size() == OriginSize + Iuput.size() * sizeof(T1));
        memcpy(VertexChannel.Data.data() + OriginSize, Iuput.data(), Iuput.size() * sizeof(T1));
    }

    template<typename T>
    void DebugDataCheck(const std::vector<uint8>& InputData, uint32 Size)
    {
#if DEBUG_DATA_CHECKED
        std::vector<T> DebugData;
        DebugData.resize(Size);
        memcpy(DebugData.data(), TmpBuffer.data(), TmpBuffer.size());
        DebugData.clear();
        DebugData.shrink_to_fit();
#endif
    }


private:
    void BuildIndexStream(const FStaticMeshLODResources& LODResource);
    void BuildMeshParts(const FBox& BoundingBox, uint32 IndexNum, uint32 VertexNum);
    uint32 BuildVertexChannel(const FStaticMeshLODResources& LODResource);

    VertexChannelAssetData& GetChannelAssetData(cross::VertexChannel Channel);
    bool VertexChannelExist(cross::VertexChannel Channel) const;

private:
    const UStaticMesh* StaticMesh;
    TSharedRef<FCEResource> Resource = MakeShared<FCEResource>();
    std::vector<uint8> TmpBuffer;
    std::vector<VertexChannelAssetData> VertexChannelDatas;
};