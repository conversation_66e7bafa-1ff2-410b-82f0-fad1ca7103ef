#include "CEAssetExchange.h"
#include "Math/Vector.h"
#include <string>
#include <cmath>
#include <vector>
#include "CELog.h"
#include "IMaterialBakingModule.h"
#include "MaterialBakingStructures.h"
#include "Materials/MaterialExpressionConstant.h"
#include "Materials/MaterialExpressionConstant2Vector.h"
#include "Materials/MaterialExpressionConstant3Vector.h"
#include "Materials/MaterialExpressionConstant4Vector.h"
#include "Materials/MaterialExpressionScalarParameter.h"
#include "Materials/MaterialExpressionTextureSampleParameter2D.h"
#include "Materials/MaterialExpressionVectorParameter.h"
#include "Tasks/CEStaticMeshTask.h"
#include "LandscapeInfo.h"
#include "Landscape.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "SceneTypes.h"
#include "InstancedFoliageActor.h"
#include "Engine/Light.h"
#include "Engine/TextureDefines.h"
#include "TextureResource.h"
#include "Engine/Texture2D.h"
#include "Misc/ScopedSlowTask.h"
#include "Engine/TextureDefines.h"
#include "Internationalization/Internationalization.h"
#include "ImageUtils.h"
#include "IImageWrapper.h"
namespace CEAssetExchange {
std::shared_ptr<CEAssetExchange::IAssetExchangeSDK> CEAssetExchange::CreateAssetExchangeSDKAutoPtr(const char* Staging, const char* engineresourcePath)
{
    // return std::shared_ptr<IAssetExchangeSDK>{CreateAssetExchangeSDK(Staging), std::mem_fn(&IAssetExchangeSDK::Release)};
    return std::shared_ptr<IAssetExchangeSDK>{CreateAssetExchangeSDK(Staging, engineresourcePath), [](IAssetExchangeSDK* This) {
                                                  double time = 0;
                                                  This->Release(&time);
                                                  CE_Log(TEXT("CEAssetExchange: %16.5f"), time);
                                              }};
}
}